<template>
  <div class="task-create-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button link class="back-btn" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2>创建学习任务</h2>
      <p class="page-description">创建新的学习任务，系统将自动生成艾宾浩斯复习计划</p>
    </div>

    <!-- 任务创建表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="task-form"
        @submit.prevent="submitForm"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :lg="12">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">基本信息</h3>

              <el-form-item label="任务标题" prop="title">
                <el-input
                  v-model="form.title"
                  placeholder="请输入任务标题"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="学科分类" prop="subject">
                <el-select v-model="form.subject" placeholder="请选择学科" class="full-width">
                  <el-option
                    v-for="subject in subjectOptions"
                    :key="subject.value"
                    :label="subject.label"
                    :value="subject.value"
                  >
                    <div class="subject-option">
                      <span
                        class="subject-color"
                        :style="{ backgroundColor: subject.color }"
                      ></span>
                      {{ subject.label }}
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="任务内容" prop="content">
                <el-input
                  v-model="form.content"
                  type="textarea"
                  :rows="6"
                  placeholder="请详细描述学习任务的内容、要求和目标"
                  maxlength="5000"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-col>

          <el-col :xs="24" :lg="12">
            <!-- 任务设置 -->
            <div class="form-section">
              <h3 class="section-title">任务设置</h3>

              <el-form-item label="预估时间">
                <div class="time-input-group">
                  <el-input-number
                    v-model="form.estimatedTime"
                    :min="1"
                    :max="300"
                    controls-position="right"
                    class="time-input"
                  />
                  <span class="time-unit">分钟</span>
                  <el-button
                    size="small"
                    type="primary"
                    :icon="Timer"
                    :disabled="!canUseSmartEstimation"
                    @click="showSmartEstimation = true"
                  >
                    智能预估
                  </el-button>
                </div>
                <div v-if="!canUseSmartEstimation" class="estimation-hint">
                  <ElText size="small" type="info">
                    请先填写标题、学科和难度以启用智能预估
                  </ElText>
                </div>
              </el-form-item>

              <el-form-item label="优先级">
                <el-rate v-model="form.priority" :max="5" show-text :texts="priorityTexts" />
              </el-form-item>

              <el-form-item label="难度级别">
                <el-rate v-model="form.difficulty" :max="5" show-text :texts="difficultyTexts" />
              </el-form-item>

              <el-form-item label="标签">
                <el-select
                  v-model="form.tags"
                  multiple
                  filterable
                  allow-create
                  placeholder="添加标签（可自定义）"
                  class="full-width"
                >
                  <el-option v-for="tag in commonTags" :key="tag" :label="tag" :value="tag" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 负载检查提示 -->
            <div v-if="loadWarning" class="load-warning">
              <el-alert :title="loadWarning.message" type="warning" :closable="false">
                <template #default>
                  <div class="warning-content">
                    <p>建议：</p>
                    <ul>
                      <li v-for="suggestion in loadWarning.suggestions" :key="suggestion">
                        {{ suggestion }}
                      </li>
                    </ul>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-col>
        </el-row>

        <!-- 表单操作 -->
        <div class="form-actions">
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" :loading="loading" @click="submitForm"> 创建任务 </el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 智能时间预估对话框 -->
    <ElDialog
      v-model="showSmartEstimation"
      title="智能时间预估"
      width="90%"
      :max-width="800"
      :close-on-click-modal="false"
    >
      <TimeEstimation
        :task-features="taskFeatures"
        :auto-estimate="true"
        @estimation-accepted="handleEstimationAccepted"
      />
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { useTaskStore } from '@/stores/task'
  import { subjectOptions } from '@/mock/taskData'
  import type { CreateTaskRequest } from '@/types'
  import { ElDialog, ElMessage, ElText, type FormInstance, type FormRules } from 'element-plus'
  import { ArrowLeft, Timer } from '@element-plus/icons-vue'
  import TimeEstimation from '@/components/common/TimeEstimation.vue'
  import type { TaskFeatures, TimeEstimationResult } from '@/services/TimeEstimationService'

  const router = useRouter()
  const taskStore = useTaskStore()

  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const loadWarning = ref<any>(null)

  const form = reactive<CreateTaskRequest>({
    title: '',
    content: '',
    subject: 'math',
    estimatedTime: 30,
    priority: 3,
    difficulty: 3,
    tags: []
  })

  // 智能预估相关
  const showSmartEstimation = ref(false)

  const rules: FormRules = {
    title: [
      { required: true, message: '请输入任务标题', trigger: 'blur' },
      { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    content: [
      { required: true, message: '请输入任务内容', trigger: 'blur' },
      { min: 1, max: 5000, message: '内容长度在 1 到 5000 个字符', trigger: 'blur' }
    ],
    subject: [{ required: true, message: '请选择学科', trigger: 'change' }]
  }

  const priorityTexts = ['很低', '较低', '中等', '较高', '很高']
  const difficultyTexts = ['很简单', '简单', '中等', '困难', '很困难']

  const commonTags = [
    '重点',
    '难点',
    '考试',
    '作业',
    '预习',
    '复习',
    '练习题',
    '实验',
    '阅读',
    '背诵',
    '理解',
    '应用'
  ]

  // 计算属性
  const canUseSmartEstimation = computed(() => {
    return form.title.trim() && form.subject && form.difficulty > 0
  })

  const taskFeatures = computed<TaskFeatures | null>(() => {
    if (!canUseSmartEstimation.value) {return null}

    return {
      subject: form.subject,
      difficulty: form.difficulty,
      estimatedTime: form.estimatedTime,
      tags: form.tags,
      description: form.content,
      contentLength: form.content.length,
      hasImages: /\.(jpg|jpeg|png|gif|svg)/i.test(form.content),
      hasFormulas: /[$\\()[\]]/g.test(form.content),
      taskType: 'study'
    }
  })

  // 方法
  const handleEstimationAccepted = (result: TimeEstimationResult) => {
    form.estimatedTime = result.estimatedTime
    showSmartEstimation.value = false
    ElMessage.success(`已采用智能预估时间：${result.estimatedTime}分钟`)
  }

  const goBack = () => {
    router.back()
  }

  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    loadWarning.value = null
  }

  const submitForm = async () => {
    if (!formRef.value) {return}

    try {
      await formRef.value.validate()

      loading.value = true
      loadWarning.value = null

      const result = await taskStore.createTask(form)

      if (result.loadWarning) {
        loadWarning.value = result.loadWarning
        ElMessage.warning('任务创建成功，但请注意学习负载')
      } else {
        ElMessage.success('任务创建成功')
      }

      // 跳转到任务列表
      router.push('/tasks')
    } catch (error: any) {
      console.error('创建任务失败:', error)

      if (error.error?.code === 'INVALID_INPUT') {
        ElMessage.error(error.error.message)
      } else {
        ElMessage.error('创建任务失败，请重试')
      }
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  .task-create-page {
    max-width: 1000px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .back-btn {
    margin-bottom: 16px;
    padding: 8px 12px;
  }

  .page-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .page-description {
    margin: 0;
    color: var(--el-text-color-secondary);
  }

  .form-card {
    padding: 20px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 20px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--el-color-primary);
  }

  .full-width {
    width: 100%;
  }

  .subject-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .subject-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .time-input {
    width: 120px;
    margin-right: 8px;
  }

  .time-unit {
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .load-warning {
    margin-top: 20px;
  }

  .warning-content p {
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  .warning-content ul {
    margin: 0;
    padding-left: 20px;
  }

  .warning-content li {
    margin-bottom: 4px;
  }

  .time-input-group {
    display: flex;
    align-items: center;
    gap: 8px;

    .time-input {
      flex: 1;
    }

    .time-unit {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .estimation-hint {
    margin-top: 4px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 32px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  @media (max-width: 768px) {
    .form-card {
      padding: 16px;
    }

    .form-actions {
      flex-direction: column-reverse;
    }

    .form-actions .el-button {
      width: 100%;
    }
  }
</style>
