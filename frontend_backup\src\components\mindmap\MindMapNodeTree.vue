<template>
  <div class="mindmap-node-tree">
    <div v-if="treeNodes.length === 0" class="empty-tree">
      <el-empty description="暂无节点" size="small" />
    </div>
    
    <div v-else class="tree-content">
      <div
        v-for="node in treeNodes"
        :key="node.id"
        class="tree-node"
        :class="{ selected: selectedNode?.id === node.id }"
        @click="selectNode(node)"
      >
        <div class="node-content" :style="{ paddingLeft: `${node.level * 16}px` }">
          <div class="node-expand" @click.stop="toggleExpand(node)">
            <el-icon v-if="node.children && node.children.length > 0">
              <ArrowRight v-if="!node.expanded" />
              <ArrowDown v-else />
            </el-icon>
          </div>
          
          <div class="node-color" :style="{ backgroundColor: node.color }"></div>
          
          <div class="node-info">
            <span class="node-text">{{ node.text }}</span>
            <span v-if="node.notes" class="node-notes">{{ node.notes }}</span>
          </div>
          
          <div class="node-actions">
            <el-button size="small" link :icon="Edit" @click.stop="editNode(node)" />
            <el-button size="small" link :icon="Plus" @click.stop="addChildNode(node)" />
            <el-button size="small" link :icon="Delete" @click.stop="deleteNode(node)" />
          </div>
        </div>
        
        <div v-if="node.expanded && node.children" class="node-children">
          <MindMapNodeTree
            :nodes="node.children"
            :selected-node="selectedNode"
            @select-node="selectNode"
            @edit-node="editNode"
            @delete-node="deleteNode"
            @add-child="addChildNode"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { ArrowDown, ArrowRight, Delete, Edit, Plus } from '@element-plus/icons-vue'
  import type { MindMapNode } from '@/types/mindmap'

  interface TreeNode extends MindMapNode {
    children?: TreeNode[]
    expanded?: boolean
  }

  interface Props {
    nodes: MindMapNode[]
    selectedNode: MindMapNode | null
  }

  interface Emits {
    (e: 'select-node', node: MindMapNode): void
    (e: 'edit-node', node: MindMapNode): void
    (e: 'delete-node', node: MindMapNode): void
    (e: 'add-child', node: MindMapNode): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 计算树形结构
  const treeNodes = computed(() => {
    const buildTree = (parentId?: string, level = 0): TreeNode[] => {
      return props.nodes
        .filter(node => node.parentId === parentId)
        .map(node => {
          const children = buildTree(node.id, level + 1)
          return {
            ...node,
            level,
            children: children.length > 0 ? children : undefined,
            expanded: true // 默认展开
          }
        })
    }

    return buildTree()
  })

  // 方法
  const selectNode = (node: MindMapNode) => {
    emit('select-node', node)
  }

  const editNode = (node: MindMapNode) => {
    emit('edit-node', node)
  }

  const deleteNode = (node: MindMapNode) => {
    emit('delete-node', node)
  }

  const addChildNode = (node: MindMapNode) => {
    emit('add-child', node)
  }

  const toggleExpand = (node: TreeNode) => {
    node.expanded = !node.expanded
  }
</script>

<style scoped>
  .mindmap-node-tree {
    height: 100%;
    overflow-y: auto;
  }

  .empty-tree {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .tree-content {
    padding: 8px 0;
  }

  .tree-node {
    margin-bottom: 2px;
  }

  .node-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .node-content:hover {
    background: var(--el-fill-color-light);
  }

  .tree-node.selected .node-content {
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary);
  }

  .node-expand {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 2px;
    transition: background-color 0.2s ease;
  }

  .node-expand:hover {
    background: var(--el-fill-color);
  }

  .node-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid var(--el-border-color);
  }

  .node-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
  }

  .node-text {
    font-size: 14px;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .node-notes {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .node-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .node-content:hover .node-actions {
    opacity: 1;
  }

  .node-children {
    margin-left: 16px;
    border-left: 1px solid var(--el-border-color-lighter);
  }
</style>
