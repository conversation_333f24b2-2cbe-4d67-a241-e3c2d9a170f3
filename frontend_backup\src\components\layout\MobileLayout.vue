<template>
  <div class="mobile-layout" :class="{ 'has-bottom-nav': showBottomNav }">
    <!-- 移动端导航 -->
    <MobileNavigation
      :title="pageTitle"
      :show-back="showBackButton"
      :show-fab="showFab"
      :fab-icon="fabIcon"
      @back="handleBack"
      @fab-click="handleFabClick"
      @quick-action="handleQuickAction"
    >
      <template #header-actions>
        <slot name="header-actions" />
      </template>
    </MobileNavigation>

    <!-- 主要内容区域 -->
    <main class="mobile-main" :class="mainClass">
      <div class="mobile-content safe-area-all">
        <!-- 页面内容 -->
        <slot />

        <!-- 底部安全区域占位 -->
        <div v-if="showBottomNav" class="bottom-nav-spacer"></div>
      </div>

      <!-- 滚动到顶部按钮 -->
      <transition name="fade">
        <div
          v-if="showScrollTop"
          class="scroll-top-button"
          @click="scrollToTop"
        >
          <el-icon><ArrowUp /></el-icon>
        </div>
      </transition>
    </main>

    <!-- 全局加载遮罩 -->
    <transition name="fade">
      <div
        v-if="globalLoading"
        v-loading="true"
        element-loading-text="加载中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
        class="global-loading"
      >
      </div>
    </transition>

    <!-- 网络状态提示 -->
    <transition name="slide-down">
      <div v-if="showNetworkStatus" class="network-status" :class="networkStatusClass">
        <el-icon><Connection /></el-icon>
        <span>{{ networkStatusText }}</span>
      </div>
    </transition>

    <!-- 全局消息容器 -->
    <div id="mobile-message-container"></div>

    <!-- 底部操作栏 (可选) -->
    <div v-if="showBottomActions" class="mobile-bottom-actions safe-area-bottom">
      <slot name="bottom-actions" />
    </div>

    <!-- 侧滑返回手势区域 -->
    <div
      v-if="enableSwipeBack"
      class="swipe-back-area"
      @touchstart="handleSwipeStart"
      @touchmove="handleSwipeMove"
      @touchend="handleSwipeEnd"
    ></div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref } from 'vue'
  import { ArrowUp, Connection } from '@element-plus/icons-vue'
  import MobileNavigation from './MobileNavigation.vue'

  interface Props {
    pageTitle?: string
    showBackButton?: boolean
    showBottomNav?: boolean
    showFab?: boolean
    fabIcon?: any
    showBottomActions?: boolean
    enableSwipeBack?: boolean
    mainClass?: string
  }

  interface Emits {
    (_e: 'back'): void
    (_e: 'fab-click'): void
    (_e: 'quick-action', _action: any): void
    (_e: 'scroll', _event: Event): void
  }

  const props = withDefaults(defineProps<Props>(), {
    pageTitle: '学习助手',
    showBackButton: false,
    showBottomNav: true,
    showFab: true,
    fabIcon: undefined,
    showBottomActions: false,
    enableSwipeBack: true,
    mainClass: ''
  })

  const emit = defineEmits<Emits>()



  // 响应式数据
  const showScrollTop = ref(false)
  const globalLoading = ref(false)
  const isOnline = ref(navigator.onLine)
  const showNetworkStatus = ref(false)
  const scrollTop = ref(0)

  // 滑动返回相关
  const swipeStartX = ref(0)
  const swipeStartY = ref(0)
  const swipeCurrentX = ref(0)
  const isSwipeBack = ref(false)

  // 计算属性
  const networkStatusClass = computed(() => ({
    'network-offline': !isOnline.value,
    'network-online': isOnline.value
  }))

  const networkStatusText = computed(() => {
    return isOnline.value ? '网络已连接' : '网络已断开'
  })

  // 方法
  const handleBack = () => {
    emit('back')
  }

  const handleFabClick = () => {
    emit('fab-click')
  }

  const handleQuickAction = (action: any) => {
    emit('quick-action', action)
  }

  const scrollToTop = () => {
    const mainElement = document.querySelector('.mobile-main')
    if (mainElement) {
      mainElement.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  }

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
    showScrollTop.value = scrollTop.value > 300
    emit('scroll', event)
  }

  // 网络状态监听
  const handleOnline = () => {
    isOnline.value = true
    showNetworkStatus.value = true
    setTimeout(() => {
      showNetworkStatus.value = false
    }, 3000)
  }

  const handleOffline = () => {
    isOnline.value = false
    showNetworkStatus.value = true
  }

  // 滑动返回手势
  const handleSwipeStart = (event: TouchEvent) => {
    if (!props.enableSwipeBack) {return}
    
    const touch = event.touches[0]
    swipeStartX.value = touch.clientX
    swipeStartY.value = touch.clientY
    isSwipeBack.value = touch.clientX < 20 // 只在屏幕左边缘开始滑动
  }

  const handleSwipeMove = (event: TouchEvent) => {
    if (!props.enableSwipeBack || !isSwipeBack.value) {return}
    
    const touch = event.touches[0]
    swipeCurrentX.value = touch.clientX
    
    // 防止垂直滚动时触发返回
    const deltaY = Math.abs(touch.clientY - swipeStartY.value)
    const deltaX = touch.clientX - swipeStartX.value
    
    if (deltaY > 50 && deltaX < 50) {
      isSwipeBack.value = false
    }
  }

  const handleSwipeEnd = (event: TouchEvent) => {
    if (!props.enableSwipeBack || !isSwipeBack.value) {return}
    
    const deltaX = swipeCurrentX.value - swipeStartX.value
    const deltaY = Math.abs(event.changedTouches[0].clientY - swipeStartY.value)
    
    // 滑动距离大于100px且垂直偏移小于50px时触发返回
    if (deltaX > 100 && deltaY < 50) {
      handleBack()
    }
    
    isSwipeBack.value = false
  }

  // 设置全局加载状态
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时的处理
    } else {
      // 页面显示时的处理
      // 可以在这里刷新数据
    }
  }

  // 生命周期
  onMounted(() => {
    // 监听滚动事件
    const mainElement = document.querySelector('.mobile-main')
    if (mainElement) {
      mainElement.addEventListener('scroll', handleScroll, { passive: true })
    }

    // 监听网络状态
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 设置视口高度CSS变量 (解决移动端100vh问题)
    const setViewportHeight = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }
    
    setViewportHeight()
    window.addEventListener('resize', setViewportHeight)
    window.addEventListener('orientationchange', () => {
      setTimeout(setViewportHeight, 100)
    })

    // 防止iOS Safari下拉刷新
    document.addEventListener('touchmove', (e) => {
      if (e.touches.length > 1) {return}
      
      const target = e.target as HTMLElement
      const scrollableParent = target.closest('.scrollable, .mobile-main')
      
      if (!scrollableParent) {
        e.preventDefault()
      }
    }, { passive: false })

    // 防止双击缩放
    let lastTouchEnd = 0
    document.addEventListener('touchend', (e) => {
      const now = Date.now()
      if (now - lastTouchEnd <= 300) {
        e.preventDefault()
      }
      lastTouchEnd = now
    }, false)
  })

  onUnmounted(() => {
    const mainElement = document.querySelector('.mobile-main')
    if (mainElement) {
      mainElement.removeEventListener('scroll', handleScroll)
    }

    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })

  // 暴露方法给父组件
  defineExpose({
    setGlobalLoading,
    scrollToTop
  })
</script>

<style scoped>
  .mobile-layout {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  .mobile-main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    padding-top: 68px; /* 顶部导航栏高度 */
  }

  .mobile-layout.has-bottom-nav .mobile-main {
    padding-bottom: 70px; /* 底部导航栏高度 */
  }

  .mobile-content {
    min-height: 100%;
    padding: 16px;
  }

  .bottom-nav-spacer {
    height: 20px;
  }

  .scroll-top-button {
    position: fixed;
    bottom: 140px;
    right: 20px;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--el-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    z-index: 998;
    transition: all 0.3s ease;
  }

  .scroll-top-button:hover {
    transform: scale(1.1);
  }

  .scroll-top-button:active {
    transform: scale(0.95);
  }

  .global-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .network-status {
    position: fixed;
    top: 68px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1001;
    backdrop-filter: blur(10px);
  }

  .network-status.network-online {
    background: rgba(103, 194, 58, 0.9);
    color: white;
  }

  .network-status.network-offline {
    background: rgba(245, 108, 108, 0.9);
    color: white;
  }

  .mobile-bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    padding: 12px 16px;
    z-index: 1000;
  }

  .swipe-back-area {
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 100%;
    z-index: 999;
    pointer-events: auto;
  }

  /* 过渡动画 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-down-enter-active,
  .slide-down-leave-active {
    transition: all 0.3s ease;
  }

  .slide-down-enter-from,
  .slide-down-leave-to {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }

  /* 响应式适配 */
  @media (max-width: 480px) {
    .mobile-content {
      padding: 12px;
    }

    .scroll-top-button {
      width: 40px;
      height: 40px;
      bottom: 120px;
      right: 16px;
    }

    .network-status {
      font-size: 11px;
      padding: 6px 12px;
    }
  }

  /* 横屏适配 */
  @media (orientation: landscape) and (max-height: 500px) {
    .mobile-main {
      padding-top: 56px;
    }

    .mobile-layout.has-bottom-nav .mobile-main {
      padding-bottom: 60px;
    }

    .scroll-top-button {
      bottom: 80px;
    }
  }

  /* 安全区域适配 */
  @supports (padding: max(0px)) {
    .mobile-main {
      padding-top: max(68px, calc(68px + env(safe-area-inset-top)));
    }

    .mobile-layout.has-bottom-nav .mobile-main {
      padding-bottom: max(70px, calc(70px + env(safe-area-inset-bottom)));
    }

    .mobile-bottom-actions {
      padding-bottom: max(12px, calc(12px + env(safe-area-inset-bottom)));
    }
  }
</style>
