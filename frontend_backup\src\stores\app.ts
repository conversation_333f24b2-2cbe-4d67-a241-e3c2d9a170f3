import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 应用基本信息
  const appName = ref('艾宾浩斯学习系统')
  const version = ref('1.0.0')

  // UI状态
  const sidebarCollapsed = ref(false)
  const mobileMenuOpen = ref(false)
  const theme = ref<'light' | 'dark' | 'auto'>('light')
  const language = ref<'zh-CN' | 'en-US'>('zh-CN')

  // 加载状态
  const loading = ref(false)
  const loadingText = ref('')

  // 错误状态
  const error = ref<string | null>(null)

  // 网络状态
  const online = ref(navigator.onLine)

  // 页面状态
  const currentRoute = ref('')
  const breadcrumbs = ref<Array<{ name: string; path: string }>>([])

  // 通知状态
  const notifications = ref<
    Array<{
      id: string
      type: 'success' | 'warning' | 'error' | 'info'
      title: string
      message: string
      timestamp: string
      read: boolean
    }>
  >([])

  // 计算属性
  const isDarkMode = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return theme.value === 'dark'
  })

  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)
  const unreadNotifications = computed(() => notifications.value.filter(n => !n.read))

  // 操作方法
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value
  }

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme

    // 应用主题到DOM
    const html = document.documentElement
    if (newTheme === 'dark' || (newTheme === 'auto' && isDarkMode.value)) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  const setLanguage = (newLanguage: 'zh-CN' | 'en-US') => {
    language.value = newLanguage
  }

  const setLoading = (isLoading: boolean, text = '') => {
    loading.value = isLoading
    loadingText.value = text
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  const setOnlineStatus = (status: boolean) => {
    online.value = status
  }

  const setCurrentRoute = (route: string) => {
    currentRoute.value = route
  }

  const setBreadcrumbs = (crumbs: Array<{ name: string; path: string }>) => {
    breadcrumbs.value = crumbs
  }

  const addNotification = (notification: {
    type: 'success' | 'warning' | 'error' | 'info'
    title: string
    message: string
  }) => {
    const newNotification = {
      id: `notification-${Date.now()}`,
      ...notification,
      timestamp: new Date().toISOString(),
      read: false
    }
    notifications.value.unshift(newNotification)
  }

  const markNotificationRead = (id: string) => {
    const notification = notifications.value.find((n) => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  // 初始化方法
  const init = () => {
    // 监听网络状态
    window.addEventListener('online', () => setOnlineStatus(true))
    window.addEventListener('offline', () => setOnlineStatus(false))

    // 监听系统主题变化
    if (theme.value === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        setTheme('auto') // 重新应用主题
      })
    }

    // 应用初始主题
    setTheme(theme.value)
  }

  return {
    // 基本信息
    appName,
    version,

    // UI状态
    sidebarCollapsed,
    mobileMenuOpen,
    theme,
    language,

    // 加载和错误状态
    loading,
    loadingText,
    error,

    // 网络状态
    online,

    // 页面状态
    currentRoute,
    breadcrumbs,

    // 通知状态
    notifications,

    // 计算属性
    isDarkMode,
    isLoading,
    hasError,
    unreadNotifications,

    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    toggleMobileMenu,
    setTheme,
    setLanguage,
    setLoading,
    setError,
    clearError,
    setOnlineStatus,
    setCurrentRoute,
    setBreadcrumbs,
    addNotification,
    markNotificationRead,
    clearNotifications,
    init
  }
}, {
  persist: {
    key: 'app-store',
    storage: localStorage,
    paths: ['sidebarCollapsed', 'theme', 'language']
  }
})
