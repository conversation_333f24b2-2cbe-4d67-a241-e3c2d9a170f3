<template>
  <div class="mindmap-global-settings">
    <el-tabs v-model="activeTab">
      <!-- 通用设置 -->
      <el-tab-pane label="通用" name="general">
        <div class="settings-section">
          <h4>默认设置</h4>
          
          <div class="setting-group">
            <div class="setting-item">
              <label>默认布局</label>
              <el-select v-model="globalSettings.defaultLayout">
                <el-option label="树形布局" value="tree" />
                <el-option label="径向布局" value="radial" />
                <el-option label="力导向布局" value="force" />
                <el-option label="层次布局" value="hierarchical" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label>自动保存</label>
              <el-switch v-model="globalSettings.autoSave" />
            </div>
            
            <div class="setting-item">
              <label>自动保存间隔</label>
              <el-slider
                v-model="globalSettings.autoSaveInterval"
                :min="30"
                :max="600"
                :step="30"
                :disabled="!globalSettings.autoSave"
              />
              <span class="setting-unit">秒</span>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>界面设置</h4>
          
          <div class="setting-group">
            <div class="setting-item">
              <label>显示网格</label>
              <el-switch v-model="globalSettings.showGrid" />
            </div>
            
            <div class="setting-item">
              <label>显示小地图</label>
              <el-switch v-model="globalSettings.showMinimap" />
            </div>
            
            <div class="setting-item">
              <label>显示工具栏</label>
              <el-switch v-model="globalSettings.showToolbar" />
            </div>
            
            <div class="setting-item">
              <label>显示侧边栏</label>
              <el-switch v-model="globalSettings.showSidebar" />
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 主题设置 -->
      <el-tab-pane label="主题" name="theme">
        <div class="settings-section">
          <h4>预设主题</h4>
          
          <div class="theme-grid">
            <div
              v-for="theme in themes"
              :key="theme.id"
              class="theme-item"
              :class="{ active: globalSettings.theme === theme.id }"
              @click="selectTheme(theme.id)"
            >
              <div class="theme-preview">
                <div
                  v-for="color in theme.colors.accent"
                  :key="color"
                  class="theme-color"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
              <div class="theme-info">
                <h5>{{ theme.name }}</h5>
                <p>{{ theme.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>自定义主题</h4>
          
          <div class="setting-group">
            <div class="setting-item">
              <label>主色调</label>
              <el-color-picker v-model="customTheme.primary" />
            </div>
            
            <div class="setting-item">
              <label>辅助色</label>
              <el-color-picker v-model="customTheme.secondary" />
            </div>
            
            <div class="setting-item">
              <label>背景色</label>
              <el-color-picker v-model="customTheme.background" />
            </div>
            
            <div class="setting-item">
              <label>文字色</label>
              <el-color-picker v-model="customTheme.text" />
            </div>
          </div>
          
          <div class="theme-actions">
            <el-button size="small" @click="previewCustomTheme">预览</el-button>
            <el-button size="small" type="primary" @click="saveCustomTheme">保存主题</el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 快捷键设置 -->
      <el-tab-pane label="快捷键" name="shortcuts">
        <div class="settings-section">
          <h4>编辑快捷键</h4>
          
          <div class="shortcut-list">
            <div
              v-for="(_shortcut, key) in shortcuts"
              :key="key"
              class="shortcut-item"
            >
              <label>{{ getShortcutLabel(key) }}</label>
              <el-input
                v-model="shortcuts[key]"
                placeholder="点击设置快捷键"
                readonly
                @focus="recordShortcut(key)"
              />
            </div>
          </div>
          
          <div class="shortcut-actions">
            <el-button size="small" @click="resetShortcuts">重置快捷键</el-button>
            <el-button size="small" type="primary" @click="saveShortcuts">保存快捷键</el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 导入导出 -->
      <el-tab-pane label="导入导出" name="import-export">
        <div class="settings-section">
          <h4>导出设置</h4>
          
          <div class="setting-group">
            <div class="setting-item">
              <label>默认格式</label>
              <el-select v-model="exportSettings.defaultFormat">
                <el-option label="PNG图片" value="png" />
                <el-option label="JPG图片" value="jpg" />
                <el-option label="SVG矢量图" value="svg" />
                <el-option label="PDF文档" value="pdf" />
                <el-option label="JSON数据" value="json" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label>图片质量</label>
              <el-slider
                v-model="exportSettings.quality"
                :min="0.1"
                :max="1"
                :step="0.1"
              />
            </div>
            
            <div class="setting-item">
              <label>包含背景</label>
              <el-switch v-model="exportSettings.includeBackground" />
            </div>
          </div>
        </div>

        <div class="settings-section">
          <h4>导入设置</h4>
          
          <div class="setting-group">
            <div class="setting-item">
              <label>合并模式</label>
              <el-select v-model="importSettings.mergeMode">
                <el-option label="替换" value="replace" />
                <el-option label="追加" value="append" />
                <el-option label="合并" value="merge" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label>保留样式</label>
              <el-switch v-model="importSettings.preserveStyles" />
            </div>
            
            <div class="setting-item">
              <label>保留ID</label>
              <el-switch v-model="importSettings.preserveIds" />
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="global-actions">
      <el-button @click="resetAllSettings">重置所有设置</el-button>
      <el-button type="primary" @click="saveAllSettings">保存所有设置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'

  interface Emits {
    (e: 'update-settings', settings: any): void
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const activeTab = ref('general')

  const globalSettings = ref({
    defaultLayout: 'tree',
    autoSave: true,
    autoSaveInterval: 300,
    showGrid: true,
    showMinimap: true,
    showToolbar: true,
    showSidebar: true,
    theme: 'default'
  })

  const customTheme = ref({
    primary: '#409EFF',
    secondary: '#67C23A',
    background: '#ffffff',
    text: '#303133'
  })

  const shortcuts = ref({
    addNode: 'Tab',
    deleteNode: 'Delete',
    editNode: 'F2',
    copyNode: 'Ctrl+C',
    pasteNode: 'Ctrl+V',
    undoAction: 'Ctrl+Z',
    redoAction: 'Ctrl+Y',
    selectAll: 'Ctrl+A',
    zoomIn: 'Ctrl+=',
    zoomOut: 'Ctrl+-',
    fitToView: 'Ctrl+0',
    save: 'Ctrl+S',
    export: 'Ctrl+E'
  })

  const exportSettings = ref({
    defaultFormat: 'png',
    quality: 0.9,
    includeBackground: true
  })

  const importSettings = ref({
    mergeMode: 'replace',
    preserveStyles: true,
    preserveIds: false
  })

  const themes = [
    {
      id: 'default',
      name: '默认主题',
      description: '经典蓝色主题',
      colors: {
        primary: '#409EFF',
        secondary: '#67C23A',
        background: '#ffffff',
        text: '#303133',
        accent: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C']
      }
    },
    {
      id: 'dark',
      name: '深色主题',
      description: '护眼深色主题',
      colors: {
        primary: '#409EFF',
        secondary: '#67C23A',
        background: '#1a1a1a',
        text: '#ffffff',
        accent: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C']
      }
    },
    {
      id: 'nature',
      name: '自然主题',
      description: '清新绿色主题',
      colors: {
        primary: '#67C23A',
        secondary: '#85ce61',
        background: '#f0f9ff',
        text: '#2c3e50',
        accent: ['#67C23A', '#85ce61', '#95d475', '#b3e19d']
      }
    },
    {
      id: 'sunset',
      name: '日落主题',
      description: '温暖橙色主题',
      colors: {
        primary: '#E6A23C',
        secondary: '#F56C6C',
        background: '#fff8f0',
        text: '#8b4513',
        accent: ['#E6A23C', '#F56C6C', '#ff7875', '#ffa940']
      }
    }
  ]

  // 方法
  const selectTheme = (themeId: string) => {
    globalSettings.value.theme = themeId
  }

  const getShortcutLabel = (key: string): string => {
    const labels: Record<string, string> = {
      addNode: '添加节点',
      deleteNode: '删除节点',
      editNode: '编辑节点',
      copyNode: '复制节点',
      pasteNode: '粘贴节点',
      undoAction: '撤销',
      redoAction: '重做',
      selectAll: '全选',
      zoomIn: '放大',
      zoomOut: '缩小',
      fitToView: '适应视图',
      save: '保存',
      export: '导出'
    }
    return labels[key] || key
  }

  const recordShortcut = (_key: string) => {
    ElMessage.info('请按下要设置的快捷键组合')
    // 实现快捷键录制逻辑
  }

  const previewCustomTheme = () => {
    ElMessage.info('预览自定义主题功能开发中...')
  }

  const saveCustomTheme = () => {
    ElMessage.success('自定义主题已保存')
  }

  const resetShortcuts = () => {
    shortcuts.value = {
      addNode: 'Tab',
      deleteNode: 'Delete',
      editNode: 'F2',
      copyNode: 'Ctrl+C',
      pasteNode: 'Ctrl+V',
      undoAction: 'Ctrl+Z',
      redoAction: 'Ctrl+Y',
      selectAll: 'Ctrl+A',
      zoomIn: 'Ctrl+=',
      zoomOut: 'Ctrl+-',
      fitToView: 'Ctrl+0',
      save: 'Ctrl+S',
      export: 'Ctrl+E'
    }
    ElMessage.success('快捷键已重置')
  }

  const saveShortcuts = () => {
    ElMessage.success('快捷键设置已保存')
  }

  const resetAllSettings = () => {
    // 重置所有设置
    ElMessage.success('所有设置已重置')
  }

  const saveAllSettings = () => {
    const allSettings = {
      global: globalSettings.value,
      theme: customTheme.value,
      shortcuts: shortcuts.value,
      export: exportSettings.value,
      import: importSettings.value
    }
    
    emit('update-settings', allSettings)
    ElMessage.success('所有设置已保存')
  }
</script>

<style scoped>
  .mindmap-global-settings {
    padding: 20px;
  }

  .settings-section {
    margin-bottom: 24px;
  }

  .settings-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }

  .setting-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .setting-item label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    min-width: 100px;
  }

  .setting-unit {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    margin-left: 8px;
  }

  .theme-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .theme-item {
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .theme-item:hover {
    border-color: var(--el-color-primary);
  }

  .theme-item.active {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .theme-preview {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
  }

  .theme-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
  }

  .theme-info h5 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .theme-info p {
    margin: 0;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .theme-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
  }

  .shortcut-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .shortcut-item label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    min-width: 100px;
  }

  .shortcut-item .el-input {
    width: 150px;
  }

  .shortcut-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
  }

  .global-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
</style>
