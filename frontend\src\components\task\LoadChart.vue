<template>
  <div class="load-chart" :style="{ height: `${height}px` }">
    <div v-if="data.length === 0" class="empty-chart">
      <el-empty description="暂无数据" />
    </div>
    
    <div v-else class="chart-content">
      <!-- 柱状图 -->
      <div v-if="type === 'bar'" class="bar-chart">
        <div class="chart-grid">
          <div class="y-axis">
            <div
              v-for="tick in yAxisTicks"
              :key="tick"
              class="y-tick"
              :style="{ bottom: `${(tick / maxValue) * 100}%` }"
            >
              <span class="tick-label">{{ tick }}min</span>
              <div class="tick-line"></div>
            </div>
          </div>
          
          <div class="chart-bars">
            <div
              v-for="(item, index) in data"
              :key="index"
              class="bar-container"
            >
              <div
                class="bar"
                :class="getBarClass(item.value)"
                :style="{ 
                  height: `${(item.value / maxValue) * 100}%`,
                  animationDelay: `${index * 100}ms`
                }"
                @mouseenter="showTooltip($event, item)"
                @mouseleave="hideTooltip"
              >
                <div class="bar-value">{{ item.value }}min</div>
              </div>
              <div class="bar-label">{{ item.date }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 折线图 -->
      <div v-else class="line-chart">
        <div class="chart-grid">
          <div class="y-axis">
            <div
              v-for="tick in yAxisTicks"
              :key="tick"
              class="y-tick"
              :style="{ bottom: `${(tick / maxValue) * 100}%` }"
            >
              <span class="tick-label">{{ tick }}min</span>
              <div class="tick-line"></div>
            </div>
          </div>
          
          <div class="chart-line">
            <svg class="line-svg" :viewBox="`0 0 ${svgWidth} ${svgHeight}`">
              <!-- 网格线 -->
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
              
              <!-- 面积填充 -->
              <path
                :d="areaPath"
                fill="url(#gradient)"
                opacity="0.3"
              />
              
              <!-- 折线 -->
              <path
                :d="linePath"
                fill="none"
                stroke="var(--el-color-primary)"
                stroke-width="3"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              
              <!-- 数据点 -->
              <circle
                v-for="(point, index) in linePoints"
                :key="index"
                :cx="point.x"
                :cy="point.y"
                r="4"
                fill="var(--el-color-primary)"
                stroke="white"
                stroke-width="2"
                class="data-point"
                @mouseenter="showTooltip($event, data[index])"
                @mouseleave="hideTooltip"
              />
              
              <!-- 渐变定义 -->
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:var(--el-color-primary);stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:var(--el-color-primary);stop-opacity:0.1" />
                </linearGradient>
              </defs>
            </svg>
            
            <!-- X轴标签 -->
            <div class="x-axis">
              <div
                v-for="(item, index) in data"
                :key="index"
                class="x-label"
                :style="{ left: `${(index / (data.length - 1)) * 100}%` }"
              >
                {{ item.date }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图例 -->
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color light"></div>
          <span>轻度负载 (≤60min)</span>
        </div>
        <div class="legend-item">
          <div class="legend-color medium"></div>
          <span>中度负载 (60-120min)</span>
        </div>
        <div class="legend-item">
          <div class="legend-color heavy"></div>
          <span>重度负载 (>120min)</span>
        </div>
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.visible"
      class="chart-tooltip"
      :style="{ 
        left: `${tooltip.x}px`, 
        top: `${tooltip.y}px` 
      }"
    >
      <div class="tooltip-content">
        <div class="tooltip-date">{{ tooltip.data?.date }}</div>
        <div class="tooltip-value">
          学习时长: {{ tooltip.data?.value }}分钟
        </div>
        <div class="tooltip-count">
          任务数量: {{ tooltip.data?.count }}个
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive } from 'vue'

  interface ChartData {
    date: string
    value: number
    count: number
  }

  interface Props {
    data: ChartData[]
    type: 'bar' | 'line'
    height: number
  }

  const props = defineProps<Props>()

  // 响应式数据
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    data: null as ChartData | null
  })

  // 计算属性
  const maxValue = computed(() => {
    const max = Math.max(...props.data.map(item => item.value))
    return Math.max(max, 60) // 最小显示60分钟
  })

  const yAxisTicks = computed(() => {
    const max = maxValue.value
    const tickCount = 5
    const step = Math.ceil(max / tickCount / 10) * 10
    const ticks = []
    
    for (let i = 0; i <= tickCount; i++) {
      ticks.push(i * step)
    }
    
    return ticks.filter(tick => tick <= max)
  })

  const svgWidth = computed(() => 400)
  const svgHeight = computed(() => props.height - 80)

  const linePoints = computed(() => {
    if (props.data.length === 0) {return []}
    
    return props.data.map((item, index) => ({
      x: (index / (props.data.length - 1)) * svgWidth.value,
      y: svgHeight.value - (item.value / maxValue.value) * svgHeight.value
    }))
  })

  const linePath = computed(() => {
    if (linePoints.value.length === 0) {return ''}
    
    let path = `M ${linePoints.value[0].x} ${linePoints.value[0].y}`
    
    for (let i = 1; i < linePoints.value.length; i++) {
      const point = linePoints.value[i]
      path += ` L ${point.x} ${point.y}`
    }
    
    return path
  })

  const areaPath = computed(() => {
    if (linePoints.value.length === 0) {return ''}
    
    let path = `M ${linePoints.value[0].x} ${svgHeight.value}`
    path += ` L ${linePoints.value[0].x} ${linePoints.value[0].y}`
    
    for (let i = 1; i < linePoints.value.length; i++) {
      const point = linePoints.value[i]
      path += ` L ${point.x} ${point.y}`
    }
    
    path += ` L ${linePoints.value[linePoints.value.length - 1].x} ${svgHeight.value}`
    path += ' Z'
    
    return path
  })

  // 方法
  const getBarClass = (value: number): string => {
    if (value <= 60) {return 'bar-light'}
    if (value <= 120) {return 'bar-medium'}
    return 'bar-heavy'
  }

  const showTooltip = (event: MouseEvent, data: ChartData) => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    tooltip.visible = true
    tooltip.x = event.clientX - rect.left + 10
    tooltip.y = event.clientY - rect.top - 10
    tooltip.data = data
  }

  const hideTooltip = () => {
    tooltip.visible = false
    tooltip.data = null
  }
</script>

<style scoped>
  .load-chart {
    position: relative;
    width: 100%;
  }

  .empty-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .chart-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .chart-grid {
    position: relative;
    flex: 1;
    margin-bottom: 40px;
  }

  .y-axis {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 60px;
  }

  .y-tick {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
  }

  .tick-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-right: 8px;
  }

  .tick-line {
    flex: 1;
    height: 1px;
    background: var(--el-border-color-lighter);
  }

  .chart-bars {
    margin-left: 60px;
    height: 100%;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    padding: 0 16px;
  }

  .bar-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
  }

  .bar {
    width: 100%;
    max-width: 40px;
    border-radius: 4px 4px 0 0;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: barGrow 0.8s ease-out forwards;
    transform-origin: bottom;
  }

  @keyframes barGrow {
    from {
      transform: scaleY(0);
    }
    to {
      transform: scaleY(1);
    }
  }

  .bar:hover {
    opacity: 0.8;
    transform: scaleY(1.05);
  }

  .bar-light {
    background: linear-gradient(to top, #67C23A, #85ce61);
  }

  .bar-medium {
    background: linear-gradient(to top, #E6A23C, #ebb563);
  }

  .bar-heavy {
    background: linear-gradient(to top, #F56C6C, #f78989);
  }

  .bar-value {
    position: absolute;
    top: -24px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }

  .bar-label {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-align: center;
  }

  .chart-line {
    margin-left: 60px;
    height: 100%;
    position: relative;
  }

  .line-svg {
    width: 100%;
    height: calc(100% - 30px);
  }

  .data-point {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .data-point:hover {
    r: 6;
  }

  .x-axis {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
  }

  .x-label {
    position: absolute;
    transform: translateX(-50%);
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 30px;
  }

  .chart-legend {
    display: flex;
    justify-content: center;
    gap: 24px;
    padding: 16px 0;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
  }

  .legend-color.light {
    background: #67C23A;
  }

  .legend-color.medium {
    background: #E6A23C;
  }

  .legend-color.heavy {
    background: #F56C6C;
  }

  .chart-tooltip {
    position: absolute;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    pointer-events: none;
  }

  .tooltip-content {
    font-size: 12px;
  }

  .tooltip-date {
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .tooltip-value,
  .tooltip-count {
    color: var(--el-text-color-secondary);
    margin-bottom: 2px;
  }

  @media (max-width: 768px) {
    .chart-legend {
      flex-direction: column;
      gap: 8px;
    }

    .y-axis {
      width: 40px;
    }

    .chart-bars,
    .chart-line {
      margin-left: 40px;
    }

    .tick-label {
      font-size: 10px;
    }
  }
</style>
