import { VueWrapper } from '@vue/test-utils'
import { vi } from 'vitest'
import type { ComponentMountingOptions } from '@vue/test-utils'

/**
 * 测试工具函数集合
 */

/**
 * 创建模拟的Element Plus组件
 */
export const createMockElComponent = (name: string, template = '<div><slot /></div>') => ({
  name,
  template,
  props: ['modelValue', 'disabled', 'loading', 'size', 'type'],
  emits: ['update:modelValue', 'change', 'click', 'input']
})

/**
 * 创建模拟的路由器
 */
export const createMockRouter = () => ({
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  currentRoute: {
    value: {
      path: '/',
      name: 'home',
      params: {},
      query: {},
      meta: {}
    }
  }
})

/**
 * 创建模拟的store
 */
export const createMockStore = (initialState = {}) => ({
  ...initialState,
  $patch: vi.fn(),
  $reset: vi.fn(),
  $subscribe: vi.fn(),
  $onAction: vi.fn()
})

/**
 * 等待Vue的下一个tick和DOM更新
 */
export const waitForUpdate = async (wrapper: VueWrapper<Record<string, unknown>>, timeout = 1000) => {
  await wrapper.vm.$nextTick()
  await new Promise(resolve => setTimeout(resolve, Math.min(timeout, 100)))
}

/**
 * 模拟异步操作
 */
export const mockAsyncOperation = <T>(result: T, delay = 100): Promise<T> => {
  return new Promise(resolve => {
    setTimeout(() => resolve(result), delay)
  })
}

/**
 * 模拟失败的异步操作
 */
export const mockFailedAsyncOperation = (error: string | Error, delay = 100): Promise<never> => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(typeof error === 'string' ? new Error(error) : error)
    }, delay)
  })
}

/**
 * 创建测试用的任务数据
 */
export const createMockTask = (overrides = {}) => ({
  id: 'test-task-1',
  title: '测试任务',
  description: '这是一个测试任务',
  subject: '数学',
  status: 'active',
  difficulty: 3,
  priority: 2,
  estimatedTime: 30,
  actualTime: 0,
  tags: ['测试'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

/**
 * 创建测试用的复习数据
 */
export const createMockReview = (overrides = {}) => ({
  id: 'test-review-1',
  taskId: 'test-task-1',
  reviewIndex: 1,
  quality: 4,
  actualTime: 25,
  completedAt: new Date().toISOString(),
  nextReviewTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  ...overrides
})

/**
 * 创建测试用的思维导图数据
 */
export const createMockMindMap = (overrides = {}) => ({
  id: 'test-mindmap-1',
  title: '测试思维导图',
  description: '这是一个测试思维导图',
  nodes: [
    {
      id: 'node-1',
      label: '根节点',
      x: 0,
      y: 0,
      children: ['node-2', 'node-3'],
      parent: undefined
    },
    {
      id: 'node-2',
      label: '子节点1',
      x: 100,
      y: 50,
      children: [],
      parent: 'node-1'
    },
    {
      id: 'node-3',
      label: '子节点2',
      x: 100,
      y: -50,
      children: [],
      parent: 'node-1'
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

/**
 * 模拟浏览器API
 */
export const mockBrowserAPIs = () => {
  // Mock Notification API
  Object.defineProperty(window, 'Notification', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      close: vi.fn()
    }))
  })

  Object.defineProperty(Notification, 'permission', {
    writable: true,
    value: 'granted'
  })

  Object.defineProperty(Notification, 'requestPermission', {
    writable: true,
    value: vi.fn(() => Promise.resolve('granted'))
  })

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }))

  // Mock requestAnimationFrame
  global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16))
  global.cancelAnimationFrame = vi.fn(id => clearTimeout(id))
}

/**
 * 模拟Element Plus消息组件
 */
export const mockElMessage = () => {
  const mockMessage = {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }

  vi.doMock('element-plus', async () => {
    const actual = await vi.importActual('element-plus')
    return {
      ...actual,
      ElMessage: mockMessage
    }
  })

  return mockMessage
}

/**
 * 模拟Element Plus确认对话框
 */
export const mockElMessageBox = () => {
  const mockMessageBox = {
    confirm: vi.fn(() => Promise.resolve('confirm')),
    alert: vi.fn(() => Promise.resolve('confirm')),
    prompt: vi.fn(() => Promise.resolve({ value: 'test' }))
  }

  vi.doMock('element-plus', async () => {
    const actual = await vi.importActual('element-plus')
    return {
      ...actual,
      ElMessageBox: mockMessageBox
    }
  })

  return mockMessageBox
}

/**
 * 创建组件测试的默认选项
 */
export const createMountOptions = (overrides: ComponentMountingOptions<Record<string, unknown>> = {}) => ({
  global: {
    stubs: {
      'router-link': true,
      'router-view': true,
      transition: false,
      'transition-group': false,
      'el-button': createMockElComponent('ElButton', '<button><slot /></button>'),
      'el-input': createMockElComponent('ElInput', '<input />'),
      'el-select': createMockElComponent('ElSelect', '<select><slot /></select>'),
      'el-option': createMockElComponent('ElOption', '<option><slot /></option>'),
      'el-card': createMockElComponent('ElCard', '<div class="el-card"><slot /></div>'),
      'el-table': createMockElComponent('ElTable', '<table><slot /></table>'),
      'el-table-column': createMockElComponent('ElTableColumn', '<td><slot /></td>'),
      ...overrides.global?.stubs
    },
    mocks: {
      $router: createMockRouter(),
      $route: {
        path: '/',
        name: 'home',
        params: {},
        query: {},
        meta: {}
      },
      ...overrides.global?.mocks
    },
    ...overrides.global
  },
  ...overrides
})

/**
 * 测试组件的可访问性
 */
export const testAccessibility = (wrapper: VueWrapper<Record<string, unknown>>) => {
  // 检查是否有适当的ARIA属性
  const ariaElements = wrapper.findAll('[aria-label], [aria-labelledby], [aria-describedby]')
  
  // 检查是否有适当的语义化标签
  const semanticElements = wrapper.findAll('main, section, article, aside, nav, header, footer')
  
  // 检查是否有适当的标题结构
  const headings = wrapper.findAll('h1, h2, h3, h4, h5, h6')
  
  return {
    hasAriaAttributes: ariaElements.length > 0,
    hasSemanticElements: semanticElements.length > 0,
    hasHeadings: headings.length > 0,
    ariaElementsCount: ariaElements.length,
    semanticElementsCount: semanticElements.length,
    headingsCount: headings.length
  }
}

/**
 * 测试组件的响应式行为
 */
export const testResponsiveness = (wrapper: VueWrapper<Record<string, unknown>>) => {
  // 模拟不同的视口大小
  const viewports = [
    { width: 1200, height: 800, name: 'desktop' },
    { width: 768, height: 1024, name: 'tablet' },
    { width: 375, height: 667, name: 'mobile' }
  ]

  const results = []

  for (const viewport of viewports) {
    // 这里可以添加视口大小变化的模拟逻辑
    // 由于在测试环境中，我们主要检查CSS类和结构
    
    results.push({
      viewport: viewport.name,
      isVisible: wrapper.isVisible(),
      hasResponsiveClasses: wrapper.classes().some(cls => 
        cls.includes('mobile') || cls.includes('tablet') || cls.includes('desktop')
      )
    })
  }

  return results
}

/**
 * 清理测试环境
 */
export const cleanupTest = () => {
  vi.clearAllMocks()
  vi.clearAllTimers()
  
  // 清理DOM
  document.body.innerHTML = ''
  
  // 清理localStorage和sessionStorage
  localStorage.clear()
  sessionStorage.clear()
}
