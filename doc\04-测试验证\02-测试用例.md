# [TEST-CASE-001] 测试用例

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的详细测试用例，覆盖所有功能需求和业务场景。

## 🔧 P0核心功能测试用例

### [TEST-CASE-001] 学习任务创建功能测试
**测试ID**：TEST-CASE-001  
**关联需求**：[REQ-FUNC-001]  
**测试类型**：功能测试  
**优先级**：P0

**前置条件**：
- 用户已登录系统
- 系统处于正常工作状态

**测试步骤**：
1. 点击"新建任务"按钮
2. 输入任务标题："英语单词 Unit 3"
3. 选择学科：英语
4. 设置优先级：3级，难度：2级
5. 输入任务内容："单词列表和释义"
6. 预估学习时间：30分钟
7. 添加标签："#词汇 #Unit3"
8. 点击"创建任务"按钮

**预期结果**：
- [ ] 任务创建成功，显示成功提示
- [ ] 自动生成9个复习时间点
- [ ] 任务保存到本地存储
- [ ] 如有负载超载，显示预警信息

**测试数据**：
```json
{
  "title": "英语单词 Unit 3",
  "subject": "english",
  "priority": 3,
  "difficulty": 2,
  "content": "单词列表和释义",
  "estimatedTime": 30,
  "tags": ["词汇", "Unit3"]
}
```

### [TEST-CASE-002] 艾宾浩斯复习计划生成测试
**测试ID**：TEST-CASE-002  
**关联需求**：[REQ-FUNC-002]  
**测试类型**：算法测试  
**优先级**：P0

**前置条件**：
- 已创建学习任务
- 任务创建时间已确定

**测试步骤**：
1. 创建任务，记录创建时间 T0
2. 检查生成的复习计划
3. 验证9个时间节点的准确性

**预期结果**：
- [ ] 生成9个复习时间点
- [ ] 时间间隔符合艾宾浩斯曲线：[5分钟, 30分钟, 12小时, 1天, 3天, 1周, 2周, 1月, 2月]
- [ ] 复习时间避开用户设置的休息时间
- [ ] 支持手动调整复习时间

**测试数据**：
```javascript
// 创建时间：2025-01-31 10:00:00
// 预期复习时间：
// 1. 2025-01-31 10:05:00 (5分钟后)
// 2. 2025-01-31 10:30:00 (30分钟后)
// 3. 2025-01-31 22:00:00 (12小时后)
// 4. 2025-02-01 10:00:00 (1天后)
// 5. 2025-02-03 10:00:00 (3天后)
// 6. 2025-02-07 10:00:00 (1周后)
// 7. 2025-02-14 10:00:00 (2周后)
// 8. 2025-02-28 10:00:00 (1月后)
// 9. 2025-03-31 10:00:00 (2月后)
```

### [TEST-CASE-003] 负载均衡检查功能测试
**测试ID**：TEST-CASE-003  
**关联需求**：[REQ-FUNC-003]  
**测试类型**：业务逻辑测试  
**优先级**：P0

**前置条件**：
- 用户已设置每日学习时间限制：120分钟
- 已有部分任务和复习安排

**测试步骤**：
1. 创建新任务，预估时间60分钟
2. 选择目标日期（已有60分钟任务安排）
3. 检查系统负载计算和预警

**预期结果**：
- [ ] 正确计算当日总负载：120分钟 (100%)
- [ ] 显示重度负载预警
- [ ] 提供调整建议
- [ ] 推荐替代日期

**测试数据**：
```json
{
  "targetDate": "2025-02-01",
  "existingLoad": 60,
  "newTaskDuration": 60,
  "userDailyLimit": 120,
  "expectedLoadLevel": "heavy",
  "expectedLoadPercentage": 100
}
```

### [TEST-CASE-004] 复习提醒功能测试
**测试ID**：TEST-CASE-004  
**关联需求**：[REQ-FUNC-004]  
**测试类型**：功能测试  
**优先级**：P0

**前置条件**：
- 已创建学习任务
- 复习时间已到达
- 浏览器允许通知权限

**测试步骤**：
1. 等待复习时间到达
2. 检查浏览器通知
3. 检查应用内消息
4. 点击通知进入复习页面

**预期结果**：
- [ ] 准时发送浏览器通知
- [ ] 应用内消息正确显示
- [ ] 点击通知正确跳转
- [ ] 免打扰时间内不发送通知

## 🚀 P1重要功能测试用例

### [TEST-CASE-005] 智能时间预估功能测试
**测试ID**：TEST-CASE-005  
**关联需求**：[REQ-FUNC-007]  
**测试类型**：算法测试  
**优先级**：P1

**前置条件**：
- 用户有历史学习数据
- 不同学科的学习效率已建立

**测试步骤**：
1. 创建新任务，不填写预估时间
2. 系统自动计算预估时间
3. 检查预估结果和置信度

**预期结果**：
- [ ] 预估时间误差在±30%范围内
- [ ] 置信度计算准确
- [ ] 影响因素说明清晰

## 🎨 P2有用功能测试用例

### [TEST-CASE-006] 思维导图创建功能测试
**测试ID**：TEST-CASE-006  
**关联需求**：[REQ-FUNC-009]  
**测试类型**：功能测试  
**优先级**：P2

**前置条件**：
- 用户已登录系统
- 思维导图模块已加载

**测试步骤**：
1. 点击"新建思维导图"
2. 创建中心节点
3. 添加子节点
4. 建立节点连接
5. 保存思维导图

**预期结果**：
- [ ] 思维导图正确创建和保存
- [ ] 节点编辑功能正常
- [ ] 连接关系正确建立

## 📊 性能测试用例

### [TEST-CASE-007] 系统响应时间测试
**测试ID**：TEST-CASE-007  
**测试类型**：性能测试  
**优先级**：P0

**测试场景**：
- 页面加载时间测试
- API响应时间测试
- 大数据量操作测试

**性能指标**：
- [ ] 页面加载时间 < 2秒
- [ ] API响应时间 < 500ms
- [ ] 1000个任务列表加载 < 3秒

## 🔗 相关文档

- [测试计划](./01-测试计划.md)
- [测试数据](./03-测试数据.md)
- [功能需求规格](../01-需求分析/02-功能需求规格.md)

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：测试工程师  
**审核人**：项目经理  
**状态**：草稿
