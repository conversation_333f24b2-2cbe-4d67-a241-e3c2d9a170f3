<template>
  <div class="mindmap-style-panel">
    <div v-if="!selectedNode" class="no-selection">
      <el-empty description="请选择一个节点" size="small" />
    </div>
    
    <div v-else class="style-content">
      <!-- 基本样式 -->
      <div class="style-section">
        <h5>基本样式</h5>
        
        <div class="style-item">
          <label>背景颜色</label>
          <el-color-picker
            v-model="nodeStyle.backgroundColor"
            @change="updateStyle"
          />
        </div>
        
        <div class="style-item">
          <label>边框颜色</label>
          <el-color-picker
            v-model="nodeStyle.borderColor"
            @change="updateStyle"
          />
        </div>
        
        <div class="style-item">
          <label>边框宽度</label>
          <el-slider
            v-model="nodeStyle.borderWidth"
            :min="0"
            :max="10"
            @change="updateStyle"
          />
        </div>
        
        <div class="style-item">
          <label>形状</label>
          <el-select v-model="nodeStyle.shape" @change="updateStyle">
            <el-option label="矩形" value="rectangle" />
            <el-option label="圆形" value="circle" />
            <el-option label="椭圆" value="ellipse" />
            <el-option label="菱形" value="diamond" />
          </el-select>
        </div>
      </div>

      <!-- 文字样式 -->
      <div class="style-section">
        <h5>文字样式</h5>
        
        <div class="style-item">
          <label>字体大小</label>
          <el-slider
            v-model="nodeStyle.fontSize"
            :min="10"
            :max="32"
            @change="updateStyle"
          />
        </div>
        
        <div class="style-item">
          <label>字体粗细</label>
          <el-select v-model="nodeStyle.fontWeight" @change="updateStyle">
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="细体" value="lighter" />
          </el-select>
        </div>
      </div>

      <!-- 尺寸 -->
      <div class="style-section">
        <h5>尺寸</h5>
        
        <div class="style-item">
          <label>宽度</label>
          <el-input-number
            v-model="nodeStyle.width"
            :min="60"
            :max="300"
            @change="updateStyle"
          />
        </div>
        
        <div class="style-item">
          <label>高度</label>
          <el-input-number
            v-model="nodeStyle.height"
            :min="30"
            :max="200"
            @change="updateStyle"
          />
        </div>
      </div>

      <!-- 预设样式 -->
      <div class="style-section">
        <h5>预设样式</h5>
        
        <div class="preset-styles">
          <div
            v-for="preset in presetStyles"
            :key="preset.name"
            class="preset-item"
            @click="applyPreset(preset)"
          >
            <div
              class="preset-preview"
              :style="{
                backgroundColor: preset.style.backgroundColor,
                borderColor: preset.style.borderColor,
                borderWidth: `${preset.style.borderWidth}px`,
                borderStyle: 'solid'
              }"
            ></div>
            <span class="preset-name">{{ preset.name }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="style-actions">
        <el-button size="small" @click="resetStyle">重置样式</el-button>
        <el-button size="small" type="primary" @click="saveAsPreset">保存为预设</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { MindMapNode } from '@/types/mindmap'

  interface Props {
    selectedNode: MindMapNode | null
  }

  interface Emits {
    (e: 'update-style', style: Partial<MindMapNode>): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 节点样式
  const nodeStyle = ref({
    backgroundColor: '#409EFF',
    borderColor: '#ccc',
    borderWidth: 1,
    shape: 'rectangle' as const,
    fontSize: 14,
    fontWeight: 'normal' as const,
    width: 120,
    height: 40
  })

  // 预设样式
  const presetStyles = [
    {
      name: '默认',
      style: {
        backgroundColor: '#409EFF',
        borderColor: '#ccc',
        borderWidth: 1,
        shape: 'rectangle' as const
      }
    },
    {
      name: '成功',
      style: {
        backgroundColor: '#67C23A',
        borderColor: '#67C23A',
        borderWidth: 2,
        shape: 'rectangle' as const
      }
    },
    {
      name: '警告',
      style: {
        backgroundColor: '#E6A23C',
        borderColor: '#E6A23C',
        borderWidth: 2,
        shape: 'rectangle' as const
      }
    },
    {
      name: '危险',
      style: {
        backgroundColor: '#F56C6C',
        borderColor: '#F56C6C',
        borderWidth: 2,
        shape: 'rectangle' as const
      }
    },
    {
      name: '信息',
      style: {
        backgroundColor: '#909399',
        borderColor: '#909399',
        borderWidth: 1,
        shape: 'circle' as const
      }
    },
    {
      name: '简约',
      style: {
        backgroundColor: '#ffffff',
        borderColor: '#dcdfe6',
        borderWidth: 1,
        shape: 'rectangle' as const
      }
    }
  ]

  // 监听选中节点变化
  watch(() => props.selectedNode, (newNode) => {
    if (newNode) {
      nodeStyle.value = {
        backgroundColor: newNode.backgroundColor || newNode.color,
        borderColor: newNode.borderColor || '#ccc',
        borderWidth: newNode.borderWidth || 1,
        shape: (newNode.shape || 'rectangle') as any,
        fontSize: newNode.fontSize || 14,
        fontWeight: (newNode.fontWeight || 'normal') as any,
        width: newNode.width || 120,
        height: newNode.height || 40
      }
    }
  }, { immediate: true })

  // 方法
  const updateStyle = () => {
    if (!props.selectedNode) {return}
    
    emit('update-style', {
      backgroundColor: nodeStyle.value.backgroundColor,
      borderColor: nodeStyle.value.borderColor,
      borderWidth: nodeStyle.value.borderWidth,
      shape: nodeStyle.value.shape,
      fontSize: nodeStyle.value.fontSize,
      fontWeight: nodeStyle.value.fontWeight,
      width: nodeStyle.value.width,
      height: nodeStyle.value.height
    })
  }

  const applyPreset = (preset: any) => {
    if (!props.selectedNode) {return}
    
    Object.assign(nodeStyle.value, preset.style)
    updateStyle()
    ElMessage.success(`已应用"${preset.name}"样式`)
  }

  const resetStyle = () => {
    if (!props.selectedNode) {return}
    
    nodeStyle.value = {
      backgroundColor: '#409EFF',
      borderColor: '#ccc',
      borderWidth: 1,
      shape: 'rectangle',
      fontSize: 14,
      fontWeight: 'normal',
      width: 120,
      height: 40
    }
    updateStyle()
    ElMessage.success('样式已重置')
  }

  const saveAsPreset = () => {
    // 实现保存为预设功能
    ElMessage.info('保存预设功能开发中...')
  }
</script>

<style scoped>
  .mindmap-style-panel {
    height: 100%;
    overflow-y: auto;
  }

  .no-selection {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .style-content {
    padding: 8px 0;
  }

  .style-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .style-section:last-child {
    border-bottom: none;
  }

  .style-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .style-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .style-item label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    min-width: 60px;
  }

  .style-item .el-select,
  .style-item .el-input-number {
    width: 100px;
  }

  .style-item .el-slider {
    width: 80px;
  }

  .preset-styles {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .preset-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .preset-item:hover {
    border-color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }

  .preset-preview {
    width: 40px;
    height: 20px;
    border-radius: 2px;
  }

  .preset-name {
    font-size: 10px;
    color: var(--el-text-color-secondary);
  }

  .style-actions {
    display: flex;
    gap: 8px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .style-actions .el-button {
    flex: 1;
  }
</style>
