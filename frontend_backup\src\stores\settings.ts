/**
 * 设置状态管理
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface AppSettings {
  // 界面设置
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  fontSize: 'small' | 'medium' | 'large'
  sidebarCollapsed: boolean
  
  // 学习设置
  defaultStudyTime: number
  breakTime: number
  autoSave: boolean
  autoSaveInterval: number
  
  // 复习设置
  reviewReminders: boolean
  reviewReminderTime: number
  overdueReminders: boolean
  overdueReminderInterval: number
  
  // 通知设置
  browserNotifications: boolean
  inAppNotifications: boolean
  doNotDisturbEnabled: boolean
  doNotDisturbStart: string
  doNotDisturbEnd: string
  
  // 数据设置
  dataBackup: boolean
  dataBackupInterval: number
  dataRetention: number
  
  // 隐私设置
  analytics: boolean
  crashReporting: boolean
  
  // 实验性功能
  experimentalFeatures: boolean
  betaFeatures: string[]
}

export const useSettingsStore = defineStore('settings', () => {
  // 默认设置
  const defaultSettings: AppSettings = {
    // 界面设置
    theme: 'light',
    language: 'zh-CN',
    fontSize: 'medium',
    sidebarCollapsed: false,
    
    // 学习设置
    defaultStudyTime: 30,
    breakTime: 5,
    autoSave: true,
    autoSaveInterval: 30,
    
    // 复习设置
    reviewReminders: true,
    reviewReminderTime: 5,
    overdueReminders: true,
    overdueReminderInterval: 30,
    
    // 通知设置
    browserNotifications: false,
    inAppNotifications: true,
    doNotDisturbEnabled: false,
    doNotDisturbStart: '22:00',
    doNotDisturbEnd: '08:00',
    
    // 数据设置
    dataBackup: true,
    dataBackupInterval: 24,
    dataRetention: 90,
    
    // 隐私设置
    analytics: true,
    crashReporting: true,
    
    // 实验性功能
    experimentalFeatures: false,
    betaFeatures: []
  }

  // 状态
  const settings = ref<AppSettings>({ ...defaultSettings })
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isDarkMode = computed(() => {
    if (settings.value.theme === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return settings.value.theme === 'dark'
  })

  const isLargeFont = computed(() => settings.value.fontSize === 'large')
  const isSmallFont = computed(() => settings.value.fontSize === 'small')

  const isDoNotDisturbActive = computed(() => {
    if (!settings.value.doNotDisturbEnabled) {return false}
    
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    const start = settings.value.doNotDisturbStart
    const end = settings.value.doNotDisturbEnd
    
    // 处理跨天的情况
    if (start > end) {
      return currentTime >= start || currentTime <= end
    } else {
      return currentTime >= start && currentTime <= end
    }
  })

  // 方法
  const updateSetting = <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
    settings.value[key] = value
    applySettings()
  }

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    applySettings()
  }

  const resetSettings = () => {
    settings.value = { ...defaultSettings }
    applySettings()
  }

  const resetSection = (section: 'ui' | 'learning' | 'review' | 'notification' | 'data' | 'privacy' | 'experimental') => {
    switch (section) {
      case 'ui':
        updateSettings({
          theme: defaultSettings.theme,
          language: defaultSettings.language,
          fontSize: defaultSettings.fontSize,
          sidebarCollapsed: defaultSettings.sidebarCollapsed
        })
        break
      case 'learning':
        updateSettings({
          defaultStudyTime: defaultSettings.defaultStudyTime,
          breakTime: defaultSettings.breakTime,
          autoSave: defaultSettings.autoSave,
          autoSaveInterval: defaultSettings.autoSaveInterval
        })
        break
      case 'review':
        updateSettings({
          reviewReminders: defaultSettings.reviewReminders,
          reviewReminderTime: defaultSettings.reviewReminderTime,
          overdueReminders: defaultSettings.overdueReminders,
          overdueReminderInterval: defaultSettings.overdueReminderInterval
        })
        break
      case 'notification':
        updateSettings({
          browserNotifications: defaultSettings.browserNotifications,
          inAppNotifications: defaultSettings.inAppNotifications,
          doNotDisturbEnabled: defaultSettings.doNotDisturbEnabled,
          doNotDisturbStart: defaultSettings.doNotDisturbStart,
          doNotDisturbEnd: defaultSettings.doNotDisturbEnd
        })
        break
      case 'data':
        updateSettings({
          dataBackup: defaultSettings.dataBackup,
          dataBackupInterval: defaultSettings.dataBackupInterval,
          dataRetention: defaultSettings.dataRetention
        })
        break
      case 'privacy':
        updateSettings({
          analytics: defaultSettings.analytics,
          crashReporting: defaultSettings.crashReporting
        })
        break
      case 'experimental':
        updateSettings({
          experimentalFeatures: defaultSettings.experimentalFeatures,
          betaFeatures: defaultSettings.betaFeatures
        })
        break
    }
  }

  // 应用设置到DOM和其他系统
  const applySettings = () => {
    // 应用主题
    const html = document.documentElement
    if (isDarkMode.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }

    // 应用字体大小
    html.classList.remove('font-small', 'font-medium', 'font-large')
    html.classList.add(`font-${settings.value.fontSize}`)

    // 应用语言
    html.setAttribute('lang', settings.value.language)

    // 触发自定义事件，让其他组件知道设置已更新
    window.dispatchEvent(new CustomEvent('settings-updated', {
      detail: settings.value
    }))
  }

  // 导出设置
  const exportSettings = () => {
    const data = {
      settings: settings.value,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ebbinghaus-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 导入设置
  const importSettings = (file: File): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string)
          
          if (data.settings && typeof data.settings === 'object') {
            // 验证设置格式
            const validSettings: Partial<AppSettings> = {}
            
            for (const [key, value] of Object.entries(data.settings)) {
              if (key in defaultSettings) {
                validSettings[key as keyof AppSettings] = value as any
              }
            }
            
            updateSettings(validSettings)
            resolve(true)
          } else {
            reject(new Error('无效的设置文件格式'))
          }
        } catch (_error) {
          reject(new Error('设置文件解析失败'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsText(file)
    })
  }

  // 获取设置分组
  const getSettingsGroup = (group: 'ui' | 'learning' | 'review' | 'notification' | 'data' | 'privacy' | 'experimental') => {
    switch (group) {
      case 'ui':
        return {
          theme: settings.value.theme,
          language: settings.value.language,
          fontSize: settings.value.fontSize,
          sidebarCollapsed: settings.value.sidebarCollapsed
        }
      case 'learning':
        return {
          defaultStudyTime: settings.value.defaultStudyTime,
          breakTime: settings.value.breakTime,
          autoSave: settings.value.autoSave,
          autoSaveInterval: settings.value.autoSaveInterval
        }
      case 'review':
        return {
          reviewReminders: settings.value.reviewReminders,
          reviewReminderTime: settings.value.reviewReminderTime,
          overdueReminders: settings.value.overdueReminders,
          overdueReminderInterval: settings.value.overdueReminderInterval
        }
      case 'notification':
        return {
          browserNotifications: settings.value.browserNotifications,
          inAppNotifications: settings.value.inAppNotifications,
          doNotDisturbEnabled: settings.value.doNotDisturbEnabled,
          doNotDisturbStart: settings.value.doNotDisturbStart,
          doNotDisturbEnd: settings.value.doNotDisturbEnd
        }
      case 'data':
        return {
          dataBackup: settings.value.dataBackup,
          dataBackupInterval: settings.value.dataBackupInterval,
          dataRetention: settings.value.dataRetention
        }
      case 'privacy':
        return {
          analytics: settings.value.analytics,
          crashReporting: settings.value.crashReporting
        }
      case 'experimental':
        return {
          experimentalFeatures: settings.value.experimentalFeatures,
          betaFeatures: settings.value.betaFeatures
        }
      default:
        return {}
    }
  }

  // 初始化
  const init = () => {
    applySettings()
    
    // 监听系统主题变化
    if (settings.value.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', applySettings)
    }
  }

  return {
    // 状态
    settings,
    loading,
    error,
    
    // 计算属性
    isDarkMode,
    isLargeFont,
    isSmallFont,
    isDoNotDisturbActive,
    
    // 方法
    updateSetting,
    updateSettings,
    resetSettings,
    resetSection,
    applySettings,
    exportSettings,
    importSettings,
    getSettingsGroup,
    init
  }
}, {
  persist: {
    key: 'settings-store',
    storage: localStorage,
    paths: ['settings']
  }
})
