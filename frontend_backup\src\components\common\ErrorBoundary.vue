<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-fallback">
      <div class="error-content">
        <div class="error-icon">
          <ElIcon size="64">
            <WarningFilled />
          </ElIcon>
        </div>
        
        <div class="error-message">
          <h3>{{ errorTitle }}</h3>
          <p>{{ errorMessage }}</p>
        </div>
        
        <div class="error-actions">
          <ElButton type="primary" @click="retry">
            <ElIcon><Refresh /></ElIcon>
            重试
          </ElButton>
          
          <ElButton @click="goHome">
            <ElIcon><HomeFilled /></ElIcon>
            返回首页
          </ElButton>
          
          <ElButton v-if="showDetails" @click="toggleDetails">
            <ElIcon><InfoFilled /></ElIcon>
            {{ showErrorDetails ? '隐藏' : '显示' }}详情
          </ElButton>
        </div>
        
        <!-- 错误详情 -->
        <div v-if="showErrorDetails" class="error-details">
          <ElCollapse>
            <ElCollapseItem title="错误信息" name="error">
              <div class="error-info">
                <p><strong>错误类型：</strong>{{ errorInfo?.type || 'Unknown' }}</p>
                <p><strong>错误时间：</strong>{{ formatTime(errorInfo?.timestamp) }}</p>
                <p><strong>页面地址：</strong>{{ errorInfo?.url || window.location.href }}</p>
                <p v-if="errorInfo?.componentName"><strong>组件名称：</strong>{{ errorInfo.componentName }}</p>
              </div>
            </ElCollapseItem>
            
            <ElCollapseItem v-if="errorInfo?.stack" title="错误堆栈" name="stack">
              <pre class="error-stack">{{ errorInfo.stack }}</pre>
            </ElCollapseItem>
            
            <ElCollapseItem v-if="errorInfo?.componentTrace" title="组件追踪" name="trace">
              <pre class="error-trace">{{ errorInfo.componentTrace }}</pre>
            </ElCollapseItem>
          </ElCollapse>
          
          <div class="error-report">
            <ElButton size="small" @click="reportError">
              <ElIcon><Warning /></ElIcon>
              报告错误
            </ElButton>
            
            <ElButton size="small" @click="copyErrorInfo">
              <ElIcon><CopyDocument /></ElIcon>
              复制错误信息
            </ElButton>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 正常内容 -->
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import { nextTick, onErrorCaptured, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ElButton,
  ElCollapse,
  ElCollapseItem,
  ElIcon,
  ElMessage
} from 'element-plus'
import {
  CopyDocument,
  HomeFilled,
  InfoFilled,
  Refresh,
  Warning,
  WarningFilled
} from '@element-plus/icons-vue'
import { ErrorLevel, ErrorType, handleError } from '@/utils/errorHandler'

interface Props {
  fallbackComponent?: string
  showDetails?: boolean
  enableRetry?: boolean
  maxRetries?: number
  onError?: (__error: unknown, __errorInfo: unknown) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallbackComponent: 'div',
  showDetails: false,
  enableRetry: true,
  maxRetries: 3,
  onError: undefined
})

interface ErrorInfo {
  type: string
  message: string
  stack?: string
  componentName?: string
  componentTrace?: string
  timestamp: number
  url: string
  retryCount: number
}



const emit = defineEmits<{
  error: [error: Error, errorInfo: ErrorInfo]
  retry: [retryCount: number]
  recover: []
}>()

// 响应式数据
const hasError = ref(false)
const errorInfo = ref<ErrorInfo | null>(null)
const showErrorDetails = ref(false)
const retryCount = ref(0)

const router = useRouter()

// 计算属性
const errorTitle = computed(() => {
  if (!errorInfo.value) {return '出现了一个错误'}
  
  const titleMap: Record<string, string> = {
    'ChunkLoadError': '资源加载失败',
    'TypeError': '类型错误',
    'ReferenceError': '引用错误',
    'SyntaxError': '语法错误',
    'NetworkError': '网络错误'
  }
  
  return titleMap[errorInfo.value.type] || '应用程序错误'
})

const errorMessage = computed(() => {
  if (!errorInfo.value) {return '应用程序遇到了一个意外错误'}
  
  const messageMap: Record<string, string> = {
    'ChunkLoadError': '页面资源加载失败，可能是网络问题或版本更新导致的',
    'TypeError': '程序执行过程中遇到了类型错误',
    'ReferenceError': '程序尝试访问未定义的变量或函数',
    'SyntaxError': '代码语法错误',
    'NetworkError': '网络连接异常，请检查网络设置'
  }
  
  return messageMap[errorInfo.value.type] || errorInfo.value.message || '未知错误'
})

// 错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary caught error:', error, info)
  
  const errorData: ErrorInfo = {
    type: error.name || 'Unknown',
    message: error.message || 'Unknown error',
    stack: error.stack,
    componentName: instance?.$options.name || instance?.$options.__name,
    componentTrace: info,
    timestamp: Date.now(),
    url: window.location.href,
    retryCount: retryCount.value
  }
  
  errorInfo.value = errorData
  hasError.value = true
  
  // 调用错误处理器
  handleError(error, ErrorType.CLIENT, ErrorLevel.HIGH)
  
  // 触发事件
  emit('error', error, errorData)
  props.onError?.(error, errorData)
  
  // 阻止错误继续传播
  return false
})

// 方法
const retry = async () => {
  if (retryCount.value >= props.maxRetries) {
    ElMessage.warning(`已达到最大重试次数 (${props.maxRetries})`)
    return
  }
  
  retryCount.value++
  emit('retry', retryCount.value)
  
  // 重置错误状态
  hasError.value = false
  errorInfo.value = null
  showErrorDetails.value = false
  
  // 等待下一个tick确保组件重新渲染
  await nextTick()
  
  ElMessage.info(`正在重试... (${retryCount.value}/${props.maxRetries})`)
}

const goHome = () => {
  router.push('/')
}

const toggleDetails = () => {
  showErrorDetails.value = !showErrorDetails.value
}

const reportError = () => {
  if (!errorInfo.value) {return}
  
  try {
    // 这里可以集成错误报告服务
    const reportData = {
      ...errorInfo.value,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user-id'),
      sessionId: sessionStorage.getItem('session-id')
    }
    
    // 模拟发送错误报告
    console.log('Error report:', reportData)
    
    ElMessage.success('错误报告已发送，感谢您的反馈')
  } catch (_error) {
    ElMessage.error('发送错误报告失败')
  }
}

const copyErrorInfo = async () => {
  if (!errorInfo.value) {return}
  
  try {
    const errorText = `
错误类型: ${errorInfo.value.type}
错误消息: ${errorInfo.value.message}
发生时间: ${formatTime(errorInfo.value.timestamp)}
页面地址: ${errorInfo.value.url}
组件名称: ${errorInfo.value.componentName || 'Unknown'}
错误堆栈:
${errorInfo.value.stack || 'No stack trace available'}
    `.trim()
    
    await navigator.clipboard.writeText(errorText)
    ElMessage.success('错误信息已复制到剪贴板')
  } catch (_error) {
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

const formatTime = (timestamp?: number) => {
  if (!timestamp) {return 'Unknown'}
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  // 监听全局错误
  window.addEventListener('error', (event) => {
    if (!hasError.value) {
      const errorData: ErrorInfo = {
        type: 'GlobalError',
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        retryCount: 0
      }
      
      errorInfo.value = errorData
      hasError.value = true
    }
  })
  
  // 监听未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (!hasError.value) {
      const errorData: ErrorInfo = {
        type: 'UnhandledPromiseRejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        retryCount: 0
      }
      
      errorInfo.value = errorData
      hasError.value = true
    }
  })
})

// 暴露方法
defineExpose({
  retry,
  reset: () => {
    hasError.value = false
    errorInfo.value = null
    retryCount.value = 0
  },
  getErrorInfo: () => errorInfo.value
})
</script>

<style scoped lang="scss">
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  
  .error-content {
    text-align: center;
    max-width: 600px;
    
    .error-icon {
      margin-bottom: 24px;
      color: #f56c6c;
    }
    
    .error-message {
      margin-bottom: 32px;
      
      h3 {
        margin: 0 0 12px 0;
        font-size: 24px;
        color: #303133;
      }
      
      p {
        margin: 0;
        font-size: 16px;
        color: #606266;
        line-height: 1.6;
      }
    }
    
    .error-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }
    
    .error-details {
      text-align: left;
      margin-top: 24px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .error-info {
        p {
          margin: 8px 0;
          font-size: 14px;
          
          strong {
            color: #303133;
          }
        }
      }
      
      .error-stack,
      .error-trace {
        background: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.4;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
      }
      
      .error-report {
        margin-top: 16px;
        display: flex;
        gap: 8px;
        justify-content: center;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .error-fallback {
    min-height: 300px;
    padding: 20px 16px;
    
    .error-content {
      .error-message {
        h3 {
          font-size: 20px;
        }
        
        p {
          font-size: 14px;
        }
      }
      
      .error-actions {
        flex-direction: column;
        align-items: center;
        
        .el-button {
          width: 200px;
        }
      }
      
      .error-details {
        padding: 16px;
        
        .error-report {
          flex-direction: column;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
