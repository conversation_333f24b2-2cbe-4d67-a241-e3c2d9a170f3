<template>
  <div class="review-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h2>复习计划</h2>
          <p>基于艾宾浩斯记忆曲线的智能复习系统</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Setting"
            @click="showReminderSettings = true"
          >
            提醒设置
          </el-button>
        </div>
      </div>
    </div>

    <el-card>
      <div v-loading="loading" class="content-area">
        <div v-if="reviewPlans.length === 0" class="empty-state">
          <el-empty description="暂无复习计划">
            <el-button type="primary" @click="$router.push('/tasks')">
              去创建任务
            </el-button>
          </el-empty>
        </div>

        <div v-else class="review-cards">
          <ReviewCard
            v-for="plan in reviewPlans"
            :key="plan.id"
            :review-plan="plan"
            @start-review="handleStartReview"
            @view-timeline="handleViewTimeline"
            @view-history="handleViewHistory"
            @edit-task="handleEditTask"
            @reset-progress="handleResetProgress"
          />
        </div>
      </div>
    </el-card>

    <ReviewExecution
      v-model="showExecutionDialog"
      :review="currentReview || undefined"
      :task="currentTask || undefined"
      @complete="handleReviewComplete"
    />

    <!-- 提醒设置对话框 -->
    <ElDialog
      v-model="showReminderSettings"
      title="复习提醒设置"
      width="90%"
      :max-width="600"
      :close-on-click-modal="false"
    >
      <ReviewReminderManager />
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElDialog, ElMessage } from 'element-plus'
  import { Setting } from '@element-plus/icons-vue'
  import ReviewCard from '@/components/review/ReviewCard.vue'
  import ReviewExecution from '@/components/review/ReviewExecution.vue'
  import ReviewReminderManager from '@/components/review/ReviewReminderManager.vue'
  import type { ReviewPlan, ReviewRecord, Task } from '@/types'

  const router = useRouter()

  // 响应式数据
  const showExecutionDialog = ref(false)
  const showReminderSettings = ref(false)
  const currentReview = ref<ReviewRecord | null>(null)
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)
  const reviewPlans = ref<ReviewPlan[]>([])

  // 模拟数据
  const mockReviewPlans: ReviewPlan[] = [
    {
      id: '1',
      taskId: '1',
      task: {
        id: '1',
        title: '英语单词 Unit 3',
        description: '学习Unit 3的30个核心词汇',
        subject: '英语',
        estimatedTime: 30,
        difficulty: 2,
        priority: 'medium',
        status: 'in-progress',
        dueDate: '2025-01-31T17:05:00Z',
        tags: ['词汇', 'Unit3'],
        createdAt: '2025-01-15T10:00:00Z',
        updatedAt: '2025-01-20T15:30:00Z'
      },
      startTime: '2025-01-15T10:00:00Z',
      reviews: [
        {
          id: 'r1',
          planId: '1',
          reviewTime: '2025-01-15T10:00:00Z',
          quality: 4,
          duration: 25,
          notes: '第一次复习，掌握良好',
          createdAt: '2025-01-15T10:00:00Z'
        },
        {
          id: 'r2',
          planId: '1',
          reviewTime: '2025-01-16T10:00:00Z',
          quality: 3,
          duration: 20,
          notes: '第二次复习，有些遗忘',
          createdAt: '2025-01-16T10:00:00Z'
        }
      ],
      completedCount: 2,
      totalCount: 9,
      nextReviewTime: '2025-02-01T10:00:00Z',
      isCompleted: false,
      createdAt: '2025-01-15T10:00:00Z',
      updatedAt: '2025-01-20T15:30:00Z'
    },
    {
      id: '2',
      taskId: '2',
      task: {
        id: '2',
        title: '数学二次函数练习',
        description: '完成二次函数的图像绘制和性质分析练习题',
        subject: '数学',
        estimatedTime: 45,
        difficulty: 4,
        priority: 'high',
        status: 'in-progress',
        dueDate: '2025-01-31T22:00:00Z',
        tags: ['函数', '图像'],
        createdAt: '2025-01-10T09:00:00Z',
        updatedAt: '2025-01-18T14:20:00Z'
      },
      startTime: '2025-01-10T09:00:00Z',
      reviews: [
        {
          id: 'r3',
          planId: '2',
          reviewTime: '2025-01-10T09:00:00Z',
          quality: 5,
          duration: 40,
          notes: '第一次复习，理解透彻',
          createdAt: '2025-01-10T09:00:00Z'
        },
        {
          id: 'r4',
          planId: '2',
          reviewTime: '2025-01-11T09:00:00Z',
          quality: 4,
          duration: 35,
          notes: '第二次复习，掌握良好',
          createdAt: '2025-01-11T09:00:00Z'
        },
        {
          id: 'r5',
          planId: '2',
          reviewTime: '2025-01-13T09:00:00Z',
          quality: 4,
          duration: 30,
          notes: '第三次复习，巩固知识',
          createdAt: '2025-01-13T09:00:00Z'
        }
      ],
      completedCount: 5,
      totalCount: 9,
      nextReviewTime: '2025-02-02T09:00:00Z',
      isCompleted: false,
      createdAt: '2025-01-10T09:00:00Z',
      updatedAt: '2025-01-18T14:20:00Z'
    },
    {
      id: '3',
      taskId: '3',
      task: {
        id: '3',
        title: '物理力学概念',
        description: '学习牛顿三定律和力的分析方法',
        subject: '物理',
        estimatedTime: 60,
        difficulty: 3,
        priority: 'high',
        status: 'completed',
        dueDate: '2025-01-30T16:00:00Z',
        tags: ['力学', '定律'],
        createdAt: '2025-01-05T14:00:00Z',
        updatedAt: '2025-01-25T16:30:00Z'
      },
      startTime: '2025-01-05T14:00:00Z',
      reviews: [
        {
          id: 'r6',
          planId: '3',
          reviewTime: '2025-01-05T14:00:00Z',
          quality: 3,
          duration: 55,
          notes: '第一次学习，概念较难',
          createdAt: '2025-01-05T14:00:00Z'
        },
        {
          id: 'r7',
          planId: '3',
          reviewTime: '2025-01-06T14:00:00Z',
          quality: 4,
          duration: 45,
          notes: '第二次复习，理解加深',
          createdAt: '2025-01-06T14:00:00Z'
        },
        {
          id: 'r8',
          planId: '3',
          reviewTime: '2025-01-08T14:00:00Z',
          quality: 5,
          duration: 35,
          notes: '第三次复习，完全掌握',
          createdAt: '2025-01-08T14:00:00Z'
        }
      ],
      completedCount: 9,
      totalCount: 9,
      nextReviewTime: null,
      isCompleted: true,
      createdAt: '2025-01-05T14:00:00Z',
      updatedAt: '2025-01-25T16:30:00Z'
    }
  ]

  // 方法
  const refreshData = async () => {
    loading.value = true
    try {
      // 模拟加载延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      reviewPlans.value = mockReviewPlans
    } catch (_error) {
      ElMessage.error('加载复习计划失败')
    } finally {
      loading.value = false
    }
  }

  const handleStartReview = (plan: ReviewPlan) => {
    const nextReviewRecord = plan.reviews.find(r => r.status === 'scheduled')
    if (nextReviewRecord) {
      currentReview.value = nextReviewRecord
      currentTask.value = plan.task
      showExecutionDialog.value = true
    } else {
      ElMessage.warning('暂无待复习的内容')
    }
  }

  const handleViewTimeline = () => {
    ElMessage.info('时间轴功能开发中...')
  }

  const handleViewHistory = () => {
    ElMessage.info('复习历史功能开发中...')
  }

  const handleEditTask = (plan: ReviewPlan) => {
    router.push(`/tasks/${plan.taskId}/edit`)
  }

  const handleResetProgress = () => {
    ElMessage.info('重置进度功能开发中...')
  }

  const handleReviewComplete = async (data: {
    rating: number
    notes: string
    duration: number
  }) => {
    if (!currentReview.value) {return}

    const success = await reviewStore.executeReview(
      currentReview.value.taskId,
      currentReview.value.intervalId,
      data.rating,
      data.notes,
      data.duration
    )

    if (success) {
      currentReview.value = null
      currentTask.value = null
    }
  }

  // 生命周期
  onMounted(() => {
    refreshData()
  })
</script>

<style scoped>
  .review-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .page-header {
    margin-bottom: 24px;
    padding: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 20px;
    }

    .header-text {
      flex: 1;
    }

    .header-actions {
      flex-shrink: 0;
    }
  }

  .content-area {
    background: var(--el-bg-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .header-content h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .header-content p {
    margin: 0;
    color: var(--el-text-color-regular);
  }

  .header-stats {
    display: flex;
    gap: 40px;
  }

  .quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-bg-color);
    border-radius: 8px;
  }

  .filter-controls {
    display: flex;
    gap: 12px;
  }

  .content-area {
    min-height: 400px;
  }

  .cards-view {
    margin-bottom: 20px;
  }

  .review-cards {
    display: grid;
    gap: 16px;
  }

  .timeline-view {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
  }

  .timeline-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .timeline-header h3 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .plan-selector {
    text-align: center;
  }

  .plan-selector h3 {
    margin-bottom: 24px;
    color: var(--el-text-color-primary);
  }

  .plan-list {
    display: grid;
    gap: 12px;
    max-width: 600px;
    margin: 0 auto;
  }

  .plan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .plan-item:hover {
    background: var(--el-fill-color);
    transform: translateY(-2px);
  }

  .plan-info h4 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
  }

  .plan-progress {
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  .list-view {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
  }

  .progress-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  @media (max-width: 768px) {
    .review-page {
      padding: 12px;
    }

    .page-header {
      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .header-actions {
        .el-button {
          width: 100%;
        }
      }
    }

    .header-stats {
      justify-content: space-around;
      gap: 20px;
    }

    .quick-actions {
      flex-direction: column;
    }

    .view-controls {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .timeline-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
</style>
