import { FullConfig, chromium } from '@playwright/test'
import fs from 'fs'
import path from 'path'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for E2E tests...')

  try {
    // 清理测试数据
    console.log('📊 Cleaning up test data...')
    
    const browser = await chromium.launch()
    const page = await browser.newPage()
    
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173'
    
    try {
      await page.goto(baseURL, { timeout: 5000 })
      
      // 清理浏览器存储
      await page.evaluate(() => {
        localStorage.clear()
        sessionStorage.clear()
        
        // 清理IndexedDB（如果使用）
        if ('indexedDB' in window) {
          indexedDB.databases?.().then(databases => {
            databases.forEach(db => {
              if (db.name?.startsWith('test-')) {
                indexedDB.deleteDatabase(db.name)
              }
            })
          })
        }
      })
      
      console.log('✅ Browser storage cleaned')
    } catch (error) {
      console.log('⚠️  Could not clean browser storage:', error.message)
    } finally {
      await browser.close()
    }

    // 清理测试文件
    console.log('📁 Cleaning up test files...')
    
    const testResultsDir = path.join(process.cwd(), 'test-results')
    const playwrightReportDir = path.join(process.cwd(), 'playwright-report')
    
    // 清理旧的测试结果（保留最近的几次）
    if (fs.existsSync(testResultsDir)) {
      const files = fs.readdirSync(testResultsDir)
      const sortedFiles = files
        .map(file => ({
          name: file,
          path: path.join(testResultsDir, file),
          mtime: fs.statSync(path.join(testResultsDir, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime())
      
      // 保留最近的5个文件，删除其他的
      const filesToDelete = sortedFiles.slice(5)
      filesToDelete.forEach(file => {
        try {
          if (fs.statSync(file.path).isDirectory()) {
            fs.rmSync(file.path, { recursive: true, force: true })
          } else {
            fs.unlinkSync(file.path)
          }
          console.log(`🗑️  Deleted old test file: ${file.name}`)
        } catch (error) {
          console.log(`⚠️  Could not delete file ${file.name}:`, error.message)
        }
      })
    }

    // 生成测试报告摘要
    console.log('📋 Generating test summary...')
    
    const summaryPath = path.join(process.cwd(), 'test-summary.json')
    const summary = {
      timestamp: new Date().toISOString(),
      testRun: {
        completed: true,
        duration: process.uptime(),
        environment: {
          node: process.version,
          platform: process.platform,
          arch: process.arch
        }
      },
      cleanup: {
        browserStorageCleared: true,
        oldFilesRemoved: true
      }
    }
    
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    console.log('✅ Test summary generated')

    // 检查测试覆盖率（如果有）
    const coverageDir = path.join(process.cwd(), 'coverage')
    if (fs.existsSync(coverageDir)) {
      console.log('📊 Test coverage reports available in coverage/')
    }

    // 输出有用的信息
    console.log('\n📈 Test Results:')
    console.log(`   - Test results: ${testResultsDir}`)
    console.log(`   - HTML report: ${playwrightReportDir}`)
    console.log(`   - Summary: ${summaryPath}`)

    console.log('✅ Global teardown completed successfully!')

  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // 不抛出错误，避免影响测试结果
  }
}

export default globalTeardown
