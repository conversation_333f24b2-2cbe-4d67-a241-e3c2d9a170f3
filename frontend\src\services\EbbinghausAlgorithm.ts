/**
 * 艾宾浩斯记忆曲线算法实现
 * 基于复习效果评分动态调整复习间隔
 */

export interface ReviewSession {
  reviewIndex: number    // 复习次数（0-8，对应9个时间点）
  quality: number        // 复习质量评分（1-5分）
  difficulty: number     // 感知难度（1-5分）
  timestamp: string      // 复习时间
  interval: number       // 实际间隔（毫秒）
}

export interface LearningProgress {
  taskId: string
  sessions: ReviewSession[]
  easinessFactor: number     // 简易因子（1.3-2.5）
  consecutiveCorrect: number // 连续正确次数
  totalReviews: number      // 总复习次数
  averageQuality: number    // 平均质量评分
  lastReviewTime: string    // 最后复习时间
  nextReviewTime: string    // 下次复习时间
}

export class EbbinghausAlgorithm {
  // 标准艾宾浩斯间隔（毫秒）
  private readonly standardIntervals = [
    5 * 60 * 1000,        // 5分钟
    30 * 60 * 1000,       // 30分钟
    12 * 60 * 60 * 1000,  // 12小时
    1 * 24 * 60 * 60 * 1000,    // 1天
    3 * 24 * 60 * 60 * 1000,    // 3天
    7 * 24 * 60 * 60 * 1000,    // 1周
    14 * 24 * 60 * 60 * 1000,   // 2周
    30 * 24 * 60 * 60 * 1000,   // 1月
    60 * 24 * 60 * 60 * 1000    // 2月
  ]

  // 质量评分对应的简易因子调整
  private readonly qualityFactors = {
    1: -0.8,  // 很差：大幅降低简易因子
    2: -0.54, // 较差：降低简易因子
    3: -0.02, // 一般：略微降低简易因子
    4: 0.1,   // 良好：提高简易因子
    5: 0.15   // 很好：大幅提高简易因子
  }

  // 难度对间隔的影响系数
  private readonly difficultyFactors = {
    1: 1.2,   // 很简单：延长间隔
    2: 1.1,   // 简单：略微延长间隔
    3: 1.0,   // 一般：标准间隔
    4: 0.8,   // 困难：缩短间隔
    5: 0.6    // 很困难：大幅缩短间隔
  }

  /**
   * 计算下次复习间隔
   * @param reviewIndex 当前复习次数（0-8）
   * @param quality 复习质量评分（1-5）
   * @param difficulty 感知难度（1-5）
   * @param easinessFactor 当前简易因子（可选）
   * @returns 下次复习间隔（毫秒）
   */
  calculateNextInterval(
    reviewIndex: number,
    quality: number,
    difficulty: number = 3,
    easinessFactor: number = 2.5
  ): number {
    // 验证参数
    if (reviewIndex < 0 || reviewIndex > 8) {
      throw new Error('复习次数必须在0-8之间')
    }
    if (quality < 1 || quality > 5) {
      throw new Error('质量评分必须在1-5之间')
    }
    if (difficulty < 1 || difficulty > 5) {
      throw new Error('难度评分必须在1-5之间')
    }

    // 如果评分过低（1-2分），重置到较早的复习阶段
    if (quality <= 2) {
      const resetIndex = Math.max(0, reviewIndex - 2)
      return this.calculateStandardInterval(resetIndex, difficulty, easinessFactor)
    }

    // 计算新的简易因子
    const newEasinessFactor = this.updateEasinessFactor(easinessFactor, quality)

    // 如果是前两次复习，使用固定间隔
    if (reviewIndex <= 1) {
      return this.calculateStandardInterval(reviewIndex, difficulty, newEasinessFactor)
    }

    // 使用SM-2算法计算间隔
    const baseInterval = this.standardIntervals[reviewIndex] || this.standardIntervals[8]
    const adjustedInterval = baseInterval * newEasinessFactor * this.difficultyFactors[difficulty as keyof typeof this.difficultyFactors]

    // 确保间隔在合理范围内
    return Math.max(
      5 * 60 * 1000, // 最小5分钟
      Math.min(
        90 * 24 * 60 * 60 * 1000, // 最大90天
        Math.round(adjustedInterval)
      )
    )
  }

  /**
   * 更新简易因子
   * @param currentFactor 当前简易因子
   * @param quality 复习质量评分
   * @returns 新的简易因子
   */
  private updateEasinessFactor(currentFactor: number, quality: number): number {
    const adjustment = this.qualityFactors[quality as keyof typeof this.qualityFactors]
    const newFactor = currentFactor + adjustment

    // 简易因子范围限制在1.3-2.5之间
    return Math.max(1.3, Math.min(2.5, newFactor))
  }

  /**
   * 计算标准间隔（用于前两次复习或重置情况）
   * @param reviewIndex 复习次数
   * @param difficulty 难度评分
   * @param easinessFactor 简易因子
   * @returns 间隔时间（毫秒）
   */
  private calculateStandardInterval(
    reviewIndex: number,
    difficulty: number,
    easinessFactor: number
  ): number {
    const baseInterval = this.standardIntervals[reviewIndex] || this.standardIntervals[0]
    const difficultyFactor = this.difficultyFactors[difficulty as keyof typeof this.difficultyFactors]
    
    return Math.round(baseInterval * difficultyFactor)
  }

  /**
   * 获取复习进度信息
   * @param sessions 历史复习记录
   * @returns 学习进度信息
   */
  getProgress(sessions: ReviewSession[]): {
    currentLevel: number
    nextInterval: number
    easinessFactor: number
    averageQuality: number
    consecutiveCorrect: number
    masteryLevel: string
  } {
    if (sessions.length === 0) {
      return {
        currentLevel: 0,
        nextInterval: this.standardIntervals[0],
        easinessFactor: 2.5,
        averageQuality: 0,
        consecutiveCorrect: 0,
        masteryLevel: '未开始'
      }
    }

    const lastSession = sessions[sessions.length - 1]
    const averageQuality = sessions.reduce((sum, s) => sum + s.quality, 0) / sessions.length
    
    // 计算连续正确次数（质量评分>=3）
    let consecutiveCorrect = 0
    for (let i = sessions.length - 1; i >= 0; i--) {
      if (sessions[i].quality >= 3) {
        consecutiveCorrect++
      } else {
        break
      }
    }

    // 计算当前简易因子
    let easinessFactor = 2.5
    for (const session of sessions) {
      easinessFactor = this.updateEasinessFactor(easinessFactor, session.quality)
    }

    // 计算下次间隔
    const nextInterval = this.calculateNextInterval(
      lastSession.reviewIndex + 1,
      lastSession.quality,
      lastSession.difficulty,
      easinessFactor
    )

    // 确定掌握程度
    const masteryLevel = this.getMasteryLevel(averageQuality, consecutiveCorrect, sessions.length)

    return {
      currentLevel: lastSession.reviewIndex + 1,
      nextInterval,
      easinessFactor,
      averageQuality,
      consecutiveCorrect,
      masteryLevel
    }
  }

  /**
   * 获取掌握程度描述
   * @param averageQuality 平均质量评分
   * @param consecutiveCorrect 连续正确次数
   * @param totalReviews 总复习次数
   * @returns 掌握程度描述
   */
  private getMasteryLevel(
    averageQuality: number,
    consecutiveCorrect: number,
    totalReviews: number
  ): string {
    if (totalReviews >= 9 && averageQuality >= 4.5) {
      return '完全掌握'
    } else if (totalReviews >= 6 && averageQuality >= 4.0) {
      return '熟练掌握'
    } else if (totalReviews >= 4 && averageQuality >= 3.5) {
      return '基本掌握'
    } else if (totalReviews >= 2 && averageQuality >= 3.0) {
      return '初步掌握'
    } else if (consecutiveCorrect >= 2) {
      return '正在学习'
    } else {
      return '需要加强'
    }
  }

  /**
   * 获取推荐的复习策略
   * @param sessions 历史复习记录
   * @returns 复习策略建议
   */
  getReviewStrategy(sessions: ReviewSession[]): {
    strategy: string
    reason: string
    suggestions: string[]
  } {
    if (sessions.length === 0) {
      return {
        strategy: '开始学习',
        reason: '这是第一次学习该内容',
        suggestions: [
          '仔细阅读和理解内容',
          '做好笔记记录重点',
          '及时进行第一次复习'
        ]
      }
    }

    const progress = this.getProgress(sessions)

    if (progress.averageQuality < 2.5) {
      return {
        strategy: '重点复习',
        reason: '平均评分较低，需要加强记忆',
        suggestions: [
          '回到基础内容重新学习',
          '增加复习频率',
          '寻找更好的学习方法',
          '考虑寻求帮助或指导'
        ]
      }
    } else if (progress.consecutiveCorrect < 2) {
      return {
        strategy: '稳固复习',
        reason: '最近复习效果不稳定',
        suggestions: [
          '保持当前复习节奏',
          '注意复习质量',
          '及时总结易错点'
        ]
      }
    } else if (progress.averageQuality >= 4.0 && progress.consecutiveCorrect >= 3) {
      return {
        strategy: '维持复习',
        reason: '掌握情况良好，保持现有节奏',
        suggestions: [
          '按计划进行复习',
          '可以适当延长复习间隔',
          '注意长期记忆保持'
        ]
      }
    } else {
      return {
        strategy: '常规复习',
        reason: '学习进展正常',
        suggestions: [
          '继续按计划复习',
          '保持学习积极性',
          '注意复习质量'
        ]
      }
    }
  }

  /**
   * 格式化间隔时间为可读文本
   * @param interval 间隔时间（毫秒）
   * @returns 格式化的时间文本
   */
  formatInterval(interval: number): string {
    const days = Math.floor(interval / (1000 * 60 * 60 * 24))
    const hours = Math.floor((interval % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((interval % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}天${hours > 0 ? hours + '小时' : ''}`
    } else if (hours > 0) {
      return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
    } else {
      return `${minutes}分钟`
    }
  }

  /**
   * 获取标准艾宾浩斯时间点描述
   * @returns 时间点描述数组
   */
  getStandardTimePoints(): Array<{
    index: number
    name: string
    interval: number
    description: string
  }> {
    const timePoints = [
      '5分钟后', '30分钟后', '12小时后',
      '1天后', '3天后', '1周后',
      '2周后', '1月后', '2月后'
    ]

    return this.standardIntervals.map((interval, index) => ({
      index,
      name: timePoints[index],
      interval,
      description: `第${index + 1}次复习：${this.formatInterval(interval)}`
    }))
  }
}

export default EbbinghausAlgorithm
