<template>
  <div class="task-status-indicator">
    <!-- 任务状态徽章 -->
    <div v-if="task" class="status-badge" :class="statusClass">
      <ElIcon class="status-icon">
        <component :is="statusIcon" />
      </ElIcon>
      <span class="status-text">{{ statusText }}</span>
    </div>
    
    <!-- 进度环 -->
    <div v-if="showProgress && task" class="progress-ring">
      <svg class="progress-svg" :width="size" :height="size">
        <circle
          class="progress-background"
          :cx="center"
          :cy="center"
          :r="radius"
          :stroke-width="strokeWidth"
        />
        <circle
          class="progress-foreground"
          :cx="center"
          :cy="center"
          :r="radius"
          :stroke-width="strokeWidth"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="progressOffset"
          :style="{ stroke: progressColor }"
        />
      </svg>
      <div class="progress-text">
        {{ Math.round(progress) }}%
      </div>
    </div>
    
    <!-- 复习提醒 -->
    <div v-if="reviewStatus" class="review-reminder" :class="reviewClass">
      <ElIcon class="reminder-icon">
        <component :is="reviewIcon" />
      </ElIcon>
      <span class="reminder-text">{{ reviewText }}</span>
    </div>
    
    <!-- 优先级指示器 -->
    <div v-if="task && task.priority > 3" class="priority-indicator">
      <ElIcon class="priority-icon">
        <Flag />
      </ElIcon>
      <span class="priority-text">高优先级</span>
    </div>
    
    <!-- 详细信息弹窗 -->
    <ElPopover
      v-if="task"
      placement="top"
      :width="300"
      trigger="hover"
      :show-after="500"
    >
      <template #reference>
        <div class="info-trigger">
          <ElIcon><InfoFilled /></ElIcon>
        </div>
      </template>
      
      <div class="task-details">
        <div class="detail-header">
          <h4>{{ task.title }}</h4>
          <ElTag :type="getSubjectTagType(task.subject)" size="small">
            {{ task.subject }}
          </ElTag>
        </div>
        
        <div class="detail-content">
          <div class="detail-row">
            <span class="label">状态：</span>
            <span class="value">{{ statusText }}</span>
          </div>
          
          <div class="detail-row">
            <span class="label">进度：</span>
            <span class="value">{{ Math.round(progress) }}%</span>
          </div>
          
          <div class="detail-row">
            <span class="label">预估时间：</span>
            <span class="value">{{ task.estimatedTime }}分钟</span>
          </div>
          
          <div class="detail-row">
            <span class="label">难度：</span>
            <span class="value">{{ task.difficulty }}级</span>
          </div>
          
          <div class="detail-row">
            <span class="label">优先级：</span>
            <span class="value">{{ task.priority }}</span>
          </div>
          
          <div v-if="nextReviewTime" class="detail-row">
            <span class="label">下次复习：</span>
            <span class="value">{{ formatReviewTime(nextReviewTime) }}</span>
          </div>
        </div>
        
        <div class="detail-actions">
          <ElButton size="small" @click="$emit('start-review', task.id)">
            开始复习
          </ElButton>
          <ElButton size="small" @click="$emit('view-task', task.id)">
            查看详情
          </ElButton>
        </div>
      </div>
    </ElPopover>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  ElButton,
  ElIcon,
  ElPopover,
  ElTag
} from 'element-plus'
import {
  Bell,
  Check,
  CircleCheck,
  Clock,
  Flag,
  InfoFilled,
  Timer,
  Warning
} from '@element-plus/icons-vue'
import type { Task } from '@/types'

interface Props {
  task: Task | null
  progress?: number
  reviewStatus?: 'pending' | 'in-progress' | 'completed' | 'overdue'
  nextReviewTime?: string
  showProgress?: boolean
  size?: number
}

interface Emits {
  (e: 'start-review', taskId: string): void
  (e: 'view-task', taskId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
  reviewStatus: 'pending',
  nextReviewTime: '',
  showProgress: true,
  size: 40
})

const emit = defineEmits<Emits>()

// 计算属性
const center = computed(() => props.size / 2)
const radius = computed(() => (props.size - 8) / 2)
const strokeWidth = computed(() => 3)
const circumference = computed(() => 2 * Math.PI * radius.value)

const progressOffset = computed(() => {
  const progress = Math.max(0, Math.min(100, props.progress))
  return circumference.value - (progress / 100) * circumference.value
})

const progressColor = computed(() => {
  if (props.progress >= 80) {return '#67c23a'}
  if (props.progress >= 50) {return '#e6a23c'}
  return '#f56c6c'
})

const statusClass = computed(() => {
  if (!props.task) {return ''}
  
  const statusMap: Record<string, string> = {
    'pending': 'status-pending',
    'in-progress': 'status-progress',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  
  return statusMap[props.task.status] || 'status-default'
})

const statusIcon = computed(() => {
  if (!props.task) {return Clock}
  
  const iconMap: Record<string, any> = {
    'pending': Clock,
    'in-progress': Timer,
    'completed': CircleCheck,
    'cancelled': Warning
  }
  
  return iconMap[props.task.status] || Clock
})

const statusText = computed(() => {
  if (!props.task) {return ''}
  
  const textMap: Record<string, string> = {
    'pending': '待开始',
    'in-progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  
  return textMap[props.task.status] || props.task.status
})

const reviewClass = computed(() => {
  const classMap: Record<string, string> = {
    'pending': 'review-pending',
    'in-progress': 'review-progress',
    'completed': 'review-completed',
    'overdue': 'review-overdue'
  }
  
  return classMap[props.reviewStatus || ''] || 'review-default'
})

const reviewIcon = computed(() => {
  const iconMap: Record<string, any> = {
    'pending': Clock,
    'in-progress': Timer,
    'completed': Check,
    'overdue': Warning
  }
  
  return iconMap[props.reviewStatus || ''] || Bell
})

const reviewText = computed(() => {
  const textMap: Record<string, string> = {
    'pending': '待复习',
    'in-progress': '复习中',
    'completed': '已复习',
    'overdue': '逾期'
  }
  
  return textMap[props.reviewStatus || ''] || ''
})

// 方法
const getSubjectTagType = (subject: string) => {
  const typeMap: Record<string, string> = {
    '数学': 'primary',
    '英语': 'success',
    '物理': 'warning',
    '化学': 'danger',
    '语文': 'info'
  }
  return typeMap[subject] || 'default'
}

const formatReviewTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diffMs = date.getTime() - now.getTime()
  const diffHours = Math.round(diffMs / (1000 * 60 * 60))
  
  if (diffHours < 0) {
    return '已逾期'
  } else if (diffHours < 24) {
    return `${diffHours}小时后`
  } else {
    const diffDays = Math.round(diffHours / 24)
    return `${diffDays}天后`
  }
}
</script>

<style scoped lang="scss">
.task-status-indicator {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  
  .status-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    
    &.status-pending {
      background: #f0f9ff;
      color: #409eff;
      border: 1px solid #b3d8ff;
    }
    
    &.status-progress {
      background: #fdf6ec;
      color: #e6a23c;
      border: 1px solid #f5dab1;
    }
    
    &.status-completed {
      background: #f0f9ff;
      color: #67c23a;
      border: 1px solid #c2e7b0;
    }
    
    &.status-cancelled {
      background: #fef0f0;
      color: #f56c6c;
      border: 1px solid #fbc4c4;
    }
    
    .status-icon {
      font-size: 10px;
    }
    
    .status-text {
      white-space: nowrap;
    }
  }
  
  .progress-ring {
    position: relative;
    display: inline-block;
    
    .progress-svg {
      transform: rotate(-90deg);
      
      .progress-background {
        fill: none;
        stroke: #e4e7ed;
      }
      
      .progress-foreground {
        fill: none;
        stroke-linecap: round;
        transition: stroke-dashoffset 0.3s ease;
      }
    }
    
    .progress-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 10px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .review-reminder {
    display: flex;
    align-items: center;
    gap: 2px;
    padding: 1px 4px;
    border-radius: 8px;
    font-size: 9px;
    
    &.review-pending {
      background: #f0f9ff;
      color: #409eff;
    }
    
    &.review-progress {
      background: #fdf6ec;
      color: #e6a23c;
    }
    
    &.review-completed {
      background: #f0f9ff;
      color: #67c23a;
    }
    
    &.review-overdue {
      background: #fef0f0;
      color: #f56c6c;
    }
    
    .reminder-icon {
      font-size: 8px;
    }
    
    .reminder-text {
      white-space: nowrap;
    }
  }
  
  .priority-indicator {
    display: flex;
    align-items: center;
    gap: 2px;
    padding: 1px 4px;
    background: #fef0f0;
    color: #f56c6c;
    border-radius: 8px;
    font-size: 9px;
    
    .priority-icon {
      font-size: 8px;
    }
    
    .priority-text {
      white-space: nowrap;
    }
  }
  
  .info-trigger {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background: #409eff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .el-icon {
      font-size: 10px;
      color: white;
    }
  }
}

.task-details {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    
    h4 {
      margin: 0;
      font-size: 14px;
      color: #303133;
      flex: 1;
      margin-right: 8px;
    }
  }
  
  .detail-content {
    margin-bottom: 12px;
    
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 12px;
      
      .label {
        color: #606266;
        font-weight: 500;
      }
      
      .value {
        color: #303133;
      }
    }
  }
  
  .detail-actions {
    display: flex;
    gap: 8px;
    
    .el-button {
      flex: 1;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.review-reminder.review-overdue {
  animation: pulse 2s infinite;
}
</style>
