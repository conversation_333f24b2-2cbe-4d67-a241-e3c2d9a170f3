<template>
  <div
    class="app-sidebar"
    :class="{
      collapsed: collapsed,
      'mobile-open': mobileOpen,
    }"
  >
    <!-- 菜单列表 -->
    <el-menu :default-active="$route.path" :collapse="collapsed" router class="sidebar-menu">
      <el-menu-item index="/dashboard">
        <el-icon><House /></el-icon>
        <template #title>学习概览</template>
      </el-menu-item>

      <el-menu-item index="/tasks">
        <el-icon><Document /></el-icon>
        <template #title>任务管理</template>
      </el-menu-item>

      <el-menu-item index="/review">
        <el-icon><Clock /></el-icon>
        <template #title>复习计划</template>
      </el-menu-item>

      <el-menu-item index="/mindmap">
        <el-icon><Share /></el-icon>
        <template #title>思维导图</template>
      </el-menu-item>

      <!-- 分割线 -->
      <el-divider />

      <!-- 快速操作 -->
      <el-menu-item index="/tasks/create">
        <el-icon><Plus /></el-icon>
        <template #title>创建任务</template>
      </el-menu-item>
    </el-menu>

    <!-- 底部统计信息 -->
    <div v-if="!collapsed" class="sidebar-footer">
      <div class="stats-card">
        <div class="stat-item">
          <span class="stat-label">今日任务</span>
          <span class="stat-value">{{ taskStats.todayReviews }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">完成率</span>
          <span class="stat-value">{{ taskStats.completionRate }}%</span>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div v-if="mobileOpen" class="mobile-overlay" @click="$emit('mobileToggle')"></div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useTaskStore } from '@/stores/task'
  import { Clock, Document, House, Plus, Share } from '@element-plus/icons-vue'

  interface Props {
    collapsed: boolean
    mobileOpen: boolean
  }

  defineProps<Props>()
  defineEmits<{
    toggle: []
    mobileToggle: []
  }>()

  const taskStore = useTaskStore()

  const taskStats = computed(() => taskStore.taskStats)
</script>

<style scoped>
  .app-sidebar {
    width: 240px;
    background: #fff;
    border-right: 1px solid var(--el-border-color);
    transition: width 0.3s;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .app-sidebar.collapsed {
    width: 64px;
  }

  .sidebar-menu {
    flex: 1;
    border: none;
  }

  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--el-border-color);
  }

  .stats-card {
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 12px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .stat-item:last-child {
    margin-bottom: 0;
  }

  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-color-primary);
  }

  /* 移动端样式 */
  @media (max-width: 768px) {
    .app-sidebar {
      position: fixed;
      left: -240px;
      top: 60px;
      height: calc(100vh - 60px);
      z-index: 999;
      transition: left 0.3s;
    }

    .app-sidebar.mobile-open {
      left: 0;
    }

    .mobile-overlay {
      position: fixed;
      top: 0;
      left: 240px;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: -1;
    }
  }
</style>
