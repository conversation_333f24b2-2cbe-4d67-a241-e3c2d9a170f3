// 任务相关类型定义
export interface Task {
  id: string
  title: string
  content: string
  subject: Subject
  estimatedTime: number
  priority: number
  difficulty: number
  tags: string[]
  status: TaskStatus
  createdAt: string
  nextReviewTime: string
  reviewSchedule: ReviewItem[]
}

export interface ReviewItem {
  reviewTime: string
  reviewIndex: number
  status: ReviewStatus
}

export interface CreateTaskRequest {
  title: string
  content: string
  subject: Subject
  estimatedTime?: number
  priority?: number
  difficulty?: number
  tags?: string[]
}

export interface LoadWarning {
  level: 'light' | 'medium' | 'heavy'
  message: string
  suggestions: string[]
}

// 枚举类型
export type Subject =
  | 'chinese'
  | 'math'
  | 'english'
  | 'physics'
  | 'chemistry'
  | 'biology'
  | 'history'
  | 'geography'

export type TaskStatus = 'active' | 'completed' | 'paused' | 'deleted'

export type ReviewStatus = 'scheduled' | 'completed' | 'skipped' | 'overdue'

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  requestId?: string
}

// 学科选项
export interface SubjectOption {
  label: string
  value: Subject
  color: string
}

// 任务筛选器
export interface TaskFilter {
  subject: Subject | ''
  priority: number
  status: TaskStatus | ''
  searchText: string
}

// 学习统计
export interface LearningStats {
  totalTasks: number
  completedTasks: number
  todayReviews: number
  weeklyProgress: number
  averageScore: number
}

// 复习执行结果
export interface ReviewResult {
  taskId: string
  reviewIndex: number
  score: number
  timeSpent: number
  notes?: string
  completedAt: string
}

// 思维导图节点
export interface MindMapNode {
  id: string
  label: string
  x: number
  y: number
  taskId?: string
  children: string[]
  parent?: string

  // 任务关联扩展
  taskStatus?: TaskStatus
  taskPriority?: number
  taskProgress?: number
  reviewStatus?: 'pending' | 'in-progress' | 'completed' | 'overdue'
  nextReviewTime?: string

  // 节点样式
  color?: string
  size?: 'small' | 'medium' | 'large'
  shape?: 'circle' | 'rectangle' | 'diamond'

  // 节点状态
  selected?: boolean
  highlighted?: boolean
  collapsed?: boolean
}

// 思维导图连接
export interface MindMapEdge {
  id: string
  source: string
  target: string
  label?: string
}

// 用户偏好设置
export interface UserPreferences {
  studyTimeStart: string
  studyTimeEnd: string
  breakDuration: number
  dailyStudyLimit: number
  reminderEnabled: boolean
  reminderSound: boolean
  theme: 'light' | 'dark' | 'auto'
}

// 复习效果评分
export enum ReviewRating {
  VERY_POOR = 1,    // 很差
  POOR = 2,         // 差
  AVERAGE = 3,      // 一般
  GOOD = 4,         // 好
  EXCELLENT = 5     // 很好
}

// 艾宾浩斯复习时间点（相对于首次学习的时间间隔）
export const EBBINGHAUS_INTERVALS = [
  { id: 1, name: '20分钟后', minutes: 20, hours: 0, days: 0 },
  { id: 2, name: '1小时后', minutes: 0, hours: 1, days: 0 },
  { id: 3, name: '8小时后', minutes: 0, hours: 8, days: 0 },
  { id: 4, name: '1天后', minutes: 0, hours: 0, days: 1 },
  { id: 5, name: '2天后', minutes: 0, hours: 0, days: 2 },
  { id: 6, name: '4天后', minutes: 0, hours: 0, days: 4 },
  { id: 7, name: '7天后', minutes: 0, hours: 0, days: 7 },
  { id: 8, name: '15天后', minutes: 0, hours: 0, days: 15 },
  { id: 9, name: '30天后', minutes: 0, hours: 0, days: 30 }
] as const

// 扩展的复习记录
export interface ReviewRecord {
  id: string
  taskId: string
  intervalId: number          // 对应EBBINGHAUS_INTERVALS的id
  scheduledTime: string       // 计划复习时间
  actualTime?: string         // 实际复习时间
  status: ReviewStatus
  rating?: ReviewRating       // 复习效果评分（1-5）
  difficulty?: number         // 感知难度（1-5）
  notes?: string             // 复习笔记
  duration?: number          // 复习耗时（分钟）
  nextReviewTime?: string     // 下次复习时间
  easinessFactor?: number     // 简易因子
  createdAt: string
  updatedAt: string
}

// 复习计划
export interface ReviewPlan {
  id: string
  taskId: string
  task: Task                 // 关联的任务
  startTime: string          // 开始学习时间
  reviews: ReviewRecord[]    // 复习记录列表
  completedCount: number     // 已完成复习次数
  totalCount: number         // 总复习次数（固定为9）
  nextReviewTime?: string    // 下次复习时间
  isCompleted: boolean       // 是否完成所有复习
  createdAt: string
  updatedAt: string
}
