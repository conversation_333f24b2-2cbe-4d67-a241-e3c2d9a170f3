/**
 * HTTP客户端，包含完善的错误处理
 */

import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { handleNetworkError, handleServerError } from './errorHandler'

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  skipErrorHandler?: boolean
  showLoading?: boolean
  showSuccessMessage?: boolean
  successMessage?: string
  retryCount?: number
  retryDelay?: number
}

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: number
}

// 错误响应接口
export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp: number
}

class HttpClient {
  private instance: AxiosInstance
  private pendingRequests: Map<string, AbortController> = new Map()
  private retryQueue: Map<string, number> = new Map()

  constructor(baseURL?: string) {
    this.instance = axios.create({
      baseURL: baseURL || import.meta.env.VITE_API_BASE_URL || '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加请求ID用于取消重复请求
        const requestId = this.generateRequestId(config)
        config.metadata = { requestId }

        // 取消重复请求
        this.cancelDuplicateRequest(requestId)

        // 创建新的AbortController
        const controller = new AbortController()
        config.signal = controller.signal
        this.pendingRequests.set(requestId, controller)

        // 添加认证token
        const token = localStorage.getItem('auth-token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求时间戳
        config.headers['X-Request-Time'] = Date.now().toString()

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        // 移除已完成的请求
        const requestId = response.config.metadata?.requestId
        if (requestId) {
          this.pendingRequests.delete(requestId)
          this.retryQueue.delete(requestId)
        }

        // 检查业务状态码
        const data = response.data as ApiResponse
        if (data && typeof data === 'object' && 'code' in data) {
          if (data.code !== 200 && data.code !== 0) {
            return this.handleBusinessError(data, response.config)
          }
        }

        return response
      },
      (error) => {
        return this.handleResponseError(error)
      }
    )
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(config: AxiosRequestConfig): string {
    const { method, url, params, data } = config
    return `${method}-${url}-${JSON.stringify(params)}-${JSON.stringify(data)}`
  }

  /**
   * 取消重复请求
   */
  private cancelDuplicateRequest(requestId: string) {
    const existingController = this.pendingRequests.get(requestId)
    if (existingController) {
      existingController.abort('Duplicate request cancelled')
      this.pendingRequests.delete(requestId)
    }
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(data: ApiResponse, config: RequestConfig) {
    const error = new Error(data.message || '业务处理失败')
    ;(error as any).code = data.code
    ;(error as any).data = data

    if (!config.skipErrorHandler) {
      handleServerError(error, data.code)
    }

    return Promise.reject(error)
  }

  /**
   * 处理响应错误
   */
  private async handleResponseError(error: AxiosError) {
    const config = error.config as RequestConfig
    const requestId = config?.metadata?.requestId

    // 移除已完成的请求
    if (requestId) {
      this.pendingRequests.delete(requestId)
    }

    // 如果是取消的请求，直接返回
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }

    // 处理网络错误
    if (!error.response) {
      if (!config?.skipErrorHandler) {
        handleNetworkError(error, config?.url)
      }
      return Promise.reject(error)
    }

    const { status } = error.response

    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        await this.handleUnauthorized()
        break
      case 403:
        this.handleForbidden()
        break
      case 404:
        this.handleNotFound(config?.url)
        break
      case 429:
        return this.handleRateLimit(error)
      case 500:
      case 502:
      case 503:
      case 504:
        return this.handleServerError(error)
      default:
        if (!config?.skipErrorHandler) {
          handleServerError(error, status)
        }
    }

    return Promise.reject(error)
  }

  /**
   * 处理401未授权
   */
  private async handleUnauthorized() {
    // 清除本地token
    localStorage.removeItem('auth-token')
    sessionStorage.removeItem('auth-token')

    // 显示登录提示
    try {
      await ElMessageBox.confirm(
        '登录状态已过期，请重新登录',
        '提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 跳转到登录页面
      window.location.href = '/login'
    } catch {
      // 用户取消，不做处理
    }
  }

  /**
   * 处理403禁止访问
   */
  private handleForbidden() {
    ElMessage.error('权限不足，无法访问该资源')
  }

  /**
   * 处理404未找到
   */
  private handleNotFound(url?: string) {
    ElMessage.error(`请求的资源不存在: ${url}`)
  }

  /**
   * 处理429限流
   */
  private async handleRateLimit(error: AxiosError): Promise<any> {
    const config = error.config as RequestConfig
    const retryAfter = error.response?.headers['retry-after']
    const delay = retryAfter ? parseInt(retryAfter) * 1000 : 5000

    ElMessage.warning(`请求过于频繁，${delay / 1000}秒后自动重试`)

    // 延迟重试
    await new Promise(resolve => setTimeout(resolve, delay))
    return this.instance.request(config)
  }

  /**
   * 处理服务器错误（5xx）
   */
  private async handleServerError(error: AxiosError): Promise<any> {
    const config = error.config as RequestConfig
    const requestId = config?.metadata?.requestId

    if (!requestId) {
      return Promise.reject(error)
    }

    const retryCount = this.retryQueue.get(requestId) || 0
    const maxRetries = config.retryCount || 3
    const retryDelay = config.retryDelay || 1000

    if (retryCount < maxRetries) {
      this.retryQueue.set(requestId, retryCount + 1)

      ElMessage.warning(`服务器错误，正在重试 (${retryCount + 1}/${maxRetries})`)

      // 指数退避重试
      const delay = retryDelay * Math.pow(2, retryCount)
      await new Promise(resolve => setTimeout(resolve, delay))

      return this.instance.request(config)
    } else {
      this.retryQueue.delete(requestId)
      ElMessage.error('服务器错误，请稍后重试')
      return Promise.reject(error)
    }
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, config)
    return response.data
  }

  /**
   * POST请求
   */
  public async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config)
    
    if (config?.showSuccessMessage) {
      ElMessage.success(config.successMessage || '操作成功')
    }
    
    return response.data
  }

  /**
   * PUT请求
   */
  public async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config)
    
    if (config?.showSuccessMessage) {
      ElMessage.success(config.successMessage || '更新成功')
    }
    
    return response.data
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config)
    
    if (config?.showSuccessMessage) {
      ElMessage.success(config.successMessage || '删除成功')
    }
    
    return response.data
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(
    url: string, 
    file: File, 
    config?: RequestConfig & { 
      onProgress?: (progress: number) => void 
    }
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      },
      onUploadProgress: (progressEvent) => {
        if (config?.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          config.onProgress(progress)
        }
      }
    })

    return response.data
  }

  /**
   * 取消所有请求
   */
  public cancelAllRequests() {
    this.pendingRequests.forEach((controller) => {
      controller.abort('All requests cancelled')
    })
    this.pendingRequests.clear()
    this.retryQueue.clear()
  }

  /**
   * 取消特定请求
   */
  public cancelRequest(requestId: string) {
    const controller = this.pendingRequests.get(requestId)
    if (controller) {
      controller.abort('Request cancelled')
      this.pendingRequests.delete(requestId)
      this.retryQueue.delete(requestId)
    }
  }

  /**
   * 获取请求统计
   */
  public getRequestStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      retryingRequests: this.retryQueue.size,
      pendingRequestIds: Array.from(this.pendingRequests.keys()),
      retryingRequestIds: Array.from(this.retryQueue.keys())
    }
  }
}

// 创建默认HTTP客户端实例
export const httpClient = new HttpClient()

// 导出便捷方法
export const { get, post, put, delete: del, upload } = httpClient

export default httpClient
