# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript/TypeScript files
[*.{js,jsx,ts,tsx,vue}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS/SCSS files
[*.{css,scss,sass,less}]
indent_style = space
indent_size = 2

# Configuration files
[*.{toml,ini}]
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2
end_of_line = lf

# Batch files
[*.{bat,cmd}]
end_of_line = crlf

# Makefile
[Makefile]
indent_style = tab

# Package files
[package.json]
indent_style = space
indent_size = 2

# Lock files (don't format)
[{package-lock.json,yarn.lock,pnpm-lock.yaml}]
insert_final_newline = false
trim_trailing_whitespace = false

# Generated files (don't format)
[{auto-imports.d.ts,components.d.ts}]
insert_final_newline = false
trim_trailing_whitespace = false
