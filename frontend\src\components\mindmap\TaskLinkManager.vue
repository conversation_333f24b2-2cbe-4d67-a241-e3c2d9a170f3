<template>
  <div class="task-link-manager">
    <ElDialog
      v-model="visible"
      title="关联学习任务"
      width="90%"
      :max-width="800"
      :close-on-click-modal="false"
    >
      <div class="manager-content">
        <!-- 节点信息 -->
        <div class="node-info">
          <h3>节点信息</h3>
          <div class="node-details">
            <div class="detail-item">
              <span class="label">节点名称：</span>
              <span class="value">{{ node?.label }}</span>
            </div>
            <div class="detail-item">
              <span class="label">当前关联：</span>
              <span class="value">
                {{ linkedTask ? linkedTask.title : '未关联任务' }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 任务选择 -->
        <div class="task-selection">
          <h3>选择关联任务</h3>
          <div class="selection-controls">
            <ElInput
              v-model="searchKeyword"
              placeholder="搜索任务..."
              :prefix-icon="Search"
              clearable
              @input="filterTasks"
            />
            <ElSelect
              v-model="selectedSubject"
              placeholder="筛选学科"
              clearable
              @change="filterTasks"
            >
              <ElOption
                v-for="subject in subjects"
                :key="subject"
                :label="subject"
                :value="subject"
              />
            </ElSelect>
          </div>
          
          <div class="task-list">
            <div v-if="filteredTasks.length === 0" class="empty-tasks">
              <ElEmpty description="没有找到匹配的任务">
                <ElButton type="primary" @click="$emit('create-task', node)">
                  创建新任务
                </ElButton>
              </ElEmpty>
            </div>
            
            <div v-else class="task-items">
              <div
                v-for="task in filteredTasks"
                :key="task.id"
                :class="[
                  'task-item',
                  { 'selected': selectedTaskId === task.id }
                ]"
                @click="selectTask(task.id)"
              >
                <div class="task-header">
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-badges">
                    <ElTag :type="getSubjectTagType(task.subject)" size="small">
                      {{ task.subject }}
                    </ElTag>
                    <ElTag :type="getStatusTagType(task.status)" size="small">
                      {{ getStatusText(task.status) }}
                    </ElTag>
                  </div>
                </div>
                
                <div class="task-meta">
                  <span class="meta-item">
                    <ElIcon><Clock /></ElIcon>
                    {{ task.estimatedTime }}分钟
                  </span>
                  <span class="meta-item">
                    <ElIcon><Star /></ElIcon>
                    难度{{ task.difficulty }}级
                  </span>
                  <span class="meta-item">
                    <ElIcon><Flag /></ElIcon>
                    优先级{{ task.priority }}
                  </span>
                </div>
                
                <div v-if="task.description" class="task-description">
                  {{ task.description.substring(0, 100) }}
                  {{ task.description.length > 100 ? '...' : '' }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 关联配置 -->
        <div v-if="selectedTaskId" class="link-config">
          <h3>关联配置</h3>
          <div class="config-options">
            <ElForm :model="linkConfig" label-width="120px">
              <ElFormItem label="节点颜色">
                <ElColorPicker v-model="linkConfig.nodeColor" />
              </ElFormItem>
              
              <ElFormItem label="节点大小">
                <ElRadioGroup v-model="linkConfig.nodeSize">
                  <ElRadio label="small">小</ElRadio>
                  <ElRadio label="medium">中</ElRadio>
                  <ElRadio label="large">大</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
              
              <ElFormItem label="节点形状">
                <ElRadioGroup v-model="linkConfig.nodeShape">
                  <ElRadio label="circle">圆形</ElRadio>
                  <ElRadio label="rectangle">矩形</ElRadio>
                  <ElRadio label="diamond">菱形</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
              
              <ElFormItem label="同步状态">
                <ElSwitch
                  v-model="linkConfig.syncStatus"
                  active-text="自动同步任务状态"
                  inactive-text="手动管理状态"
                />
              </ElFormItem>
              
              <ElFormItem label="显示进度">
                <ElSwitch
                  v-model="linkConfig.showProgress"
                  active-text="显示任务进度"
                  inactive-text="隐藏进度信息"
                />
              </ElFormItem>
            </ElForm>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="visible = false">取消</ElButton>
          <ElButton v-if="linkedTask" @click="unlinkTask">解除关联</ElButton>
          <ElButton
            type="primary"
            :disabled="!selectedTaskId"
            @click="confirmLink"
          >
            确认关联
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import {
  ElButton,
  ElColorPicker,
  ElDialog,
  ElEmpty,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  ElSwitch,
  ElTag
} from 'element-plus'
import {
  Clock,
  Flag,
  Search,
  Star
} from '@element-plus/icons-vue'
import type { MindMapNode, Task } from '@/types'
import { useTaskStore } from '@/stores/task'

interface Props {
  modelValue: boolean
  node: MindMapNode | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'link-task', data: {
    nodeId: string
    taskId: string
    config: LinkConfig
  }): void
  (e: 'unlink-task', nodeId: string): void
  (e: 'create-task', node: MindMapNode): void
}

interface LinkConfig {
  nodeColor: string
  nodeSize: 'small' | 'medium' | 'large'
  nodeShape: 'circle' | 'rectangle' | 'diamond'
  syncStatus: boolean
  showProgress: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Stores
const taskStore = useTaskStore()

// 响应式数据
const visible = ref(false)
const searchKeyword = ref('')
const selectedSubject = ref('')
const selectedTaskId = ref('')

const linkConfig = ref<LinkConfig>({
  nodeColor: '#409eff',
  nodeSize: 'medium',
  nodeShape: 'circle',
  syncStatus: true,
  showProgress: true
})

// 计算属性
const subjects = computed(() => {
  const allSubjects = taskStore.tasks.map(task => task.subject)
  return [...new Set(allSubjects)]
})

const linkedTask = computed(() => {
  if (!props.node?.taskId) {return null}
  return taskStore.tasks.find(task => task.id === props.node.taskId)
})

const filteredTasks = computed(() => {
  let tasks = taskStore.tasks

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    tasks = tasks.filter(task =>
      task.title.toLowerCase().includes(keyword) ||
      task.description.toLowerCase().includes(keyword)
    )
  }

  // 学科过滤
  if (selectedSubject.value) {
    tasks = tasks.filter(task => task.subject === selectedSubject.value)
  }

  return tasks
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue && props.node) {
    // 重置状态
    searchKeyword.value = ''
    selectedSubject.value = ''
    selectedTaskId.value = props.node.taskId || ''
    
    // 如果已有关联任务，加载其配置
    if (linkedTask.value) {
      loadTaskConfig()
    }
  }
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const filterTasks = () => {
  // 过滤逻辑在计算属性中处理
}

const selectTask = (taskId: string) => {
  selectedTaskId.value = taskId
}

const getSubjectTagType = (subject: string) => {
  const typeMap: Record<string, string> = {
    '数学': 'primary',
    '英语': 'success',
    '物理': 'warning',
    '化学': 'danger',
    '语文': 'info'
  }
  return typeMap[subject] || 'default'
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'in-progress': 'warning',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待开始',
    'in-progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

const loadTaskConfig = () => {
  if (!props.node) {return}
  
  // 从节点数据加载配置
  linkConfig.value = {
    nodeColor: props.node.color || '#409eff',
    nodeSize: props.node.size || 'medium',
    nodeShape: props.node.shape || 'circle',
    syncStatus: true,
    showProgress: true
  }
}

const confirmLink = () => {
  if (!props.node || !selectedTaskId.value) {return}
  
  emit('link-task', {
    nodeId: props.node.id,
    taskId: selectedTaskId.value,
    config: linkConfig.value
  })
  
  ElMessage.success('任务关联成功')
  visible.value = false
}

const unlinkTask = () => {
  if (!props.node) {return}
  
  emit('unlink-task', props.node.id)
  ElMessage.success('任务关联已解除')
  visible.value = false
}
</script>

<style scoped lang="scss">
.task-link-manager {
  .manager-content {
    .node-info {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      h3 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .node-details {
        .detail-item {
          display: flex;
          margin-bottom: 8px;
          
          .label {
            min-width: 80px;
            color: #606266;
            font-weight: 500;
          }
          
          .value {
            color: #303133;
          }
        }
      }
    }
    
    .task-selection {
      margin-bottom: 24px;
      
      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .selection-controls {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        
        .el-input {
          flex: 1;
        }
        
        .el-select {
          width: 150px;
        }
      }
      
      .task-list {
        max-height: 400px;
        overflow-y: auto;
        
        .empty-tasks {
          text-align: center;
          padding: 40px 0;
        }
        
        .task-items {
          .task-item {
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              border-color: #409eff;
              background: #f0f9ff;
            }
            
            &.selected {
              border-color: #409eff;
              background: #e6f7ff;
            }
            
            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;
              
              .task-title {
                font-weight: 500;
                color: #303133;
                flex: 1;
              }
              
              .task-badges {
                display: flex;
                gap: 4px;
              }
            }
            
            .task-meta {
              display: flex;
              gap: 16px;
              margin-bottom: 8px;
              
              .meta-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #909399;
              }
            }
            
            .task-description {
              font-size: 12px;
              color: #606266;
              line-height: 1.4;
            }
          }
        }
      }
    }
    
    .link-config {
      h3 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
      }
      
      .config-options {
        .el-form {
          .el-form-item {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .task-link-manager {
    .manager-content {
      .task-selection {
        .selection-controls {
          flex-direction: column;
          
          .el-select {
            width: 100%;
          }
        }
      }
      
      .task-list {
        .task-items {
          .task-item {
            .task-header {
              flex-direction: column;
              gap: 8px;
            }
            
            .task-meta {
              flex-direction: column;
              gap: 4px;
            }
          }
        }
      }
    }
    
    .dialog-footer {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
