<template>
  <div class="app-header">
    <div class="header-left">
      <!-- 菜单切换按钮 -->
      <el-button link class="menu-toggle" @click="toggleSidebar">
        <el-icon><Menu /></el-icon>
      </el-button>

      <!-- Logo和标题 -->
      <div class="logo-section">
        <el-icon class="logo-icon" color="#409EFF" size="24">
          <Reading />
        </el-icon>
        <h1 class="app-title">艾宾浩斯学习系统</h1>
      </div>
    </div>

    <div class="header-center">
      <!-- 搜索框 -->
      <el-input
        v-model="searchText"
        placeholder="搜索任务..."
        class="search-input"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="header-right">
      <!-- 通知 -->
      <el-badge :value="unreadCount" :hidden="unreadCount === 0">
        <el-button type="text" @click="showNotifications">
          <el-icon size="18"><Bell /></el-icon>
        </el-button>
      </el-badge>

      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserCommand">
        <el-button type="text" class="user-button">
          <el-avatar size="small" src="/avatar.jpg">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">学习者</span>
          <el-icon><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人设置</el-dropdown-item>
            <el-dropdown-item command="preferences">学习偏好</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { useAppStore } from '@/stores/app'
  import { useTaskStore } from '@/stores/task'
  import { ArrowDown, Bell, Menu, Reading, Search, User } from '@element-plus/icons-vue'

  const appStore = useAppStore()
  const taskStore = useTaskStore()

  const searchText = ref('')

  const unreadCount = computed(() => {
    return appStore.notifications.filter((n) => !n.read).length
  })

  const toggleSidebar = () => {
    appStore.toggleSidebar()
  }

  const handleSearch = (value: string) => {
    taskStore.setFilters({ searchText: value })
  }

  const showNotifications = () => {
    // 显示通知面板
    // 显示通知
  }

  const handleUserCommand = (command: string) => {
    switch (command) {
      case 'profile':
        // 个人设置
        break
      case 'preferences':
        // 学习偏好
        break
      case 'logout':
        // 退出登录
        break
    }
  }
</script>

<style scoped>
  .app-header {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid var(--el-border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .menu-toggle {
    font-size: 18px;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .app-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }

  .header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
  }

  .search-input {
    width: 100%;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
  }

  .username {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  @media (max-width: 768px) {
    .app-header {
      padding: 0 12px;
    }

    .header-center {
      display: none;
    }

    .app-title {
      font-size: 16px;
    }

    .username {
      display: none;
    }
  }
</style>
