<template>
  <Teleport to="body">
    <div class="notification-container">
      <TransitionGroup name="notification" tag="div">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'notification-item',
            `notification-${message.type}`
          ]"
        >
          <div class="notification-icon">
            <ElIcon>
              <InfoFilled v-if="message.type === 'info'" />
              <SuccessFilled v-else-if="message.type === 'success'" />
              <WarningFilled v-else-if="message.type === 'warning'" />
              <CircleCloseFilled v-else-if="message.type === 'error'" />
            </ElIcon>
          </div>
          
          <div class="notification-content">
            <div class="notification-title">{{ message.title }}</div>
            <div class="notification-message">{{ message.message }}</div>
            
            <div v-if="message.actions && message.actions.length > 0" class="notification-actions">
              <ElButton
                v-for="(action, index) in message.actions"
                :key="index"
                size="small"
                :type="index === 0 ? 'primary' : 'default'"
                @click="handleAction(action, message.id)"
              >
                {{ action.text }}
              </ElButton>
            </div>
          </div>
          
          <div class="notification-close">
            <ElButton
              link
              size="small"
              @click="removeMessage(message.id)"
            >
              <ElIcon><Close /></ElIcon>
            </ElButton>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { 
  CircleCloseFilled, 
  Close, 
  InfoFilled, 
  SuccessFilled,
  WarningFilled 
} from '@element-plus/icons-vue'
import NotificationService, { type InAppMessage } from '@/services/NotificationService'

const messages = ref<InAppMessage[]>([])
const notificationService = NotificationService.getInstance()

let unsubscribe: (() => void) | null = null

onMounted(() => {
  // 订阅消息变化
  unsubscribe = notificationService.subscribeToMessages((newMessages) => {
    messages.value = newMessages
  })
  
  // 初始化消息列表
  messages.value = notificationService.getInAppMessages()
})

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe()
  }
})

const handleAction = (action: { text: string; action: () => void }, messageId: string) => {
  action.action()
  removeMessage(messageId)
}

const removeMessage = (messageId: string) => {
  notificationService.removeInAppMessage(messageId)
}
</script>

<style scoped lang="scss">
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  pointer-events: none;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  pointer-events: auto;
  min-width: 320px;
  
  &.notification-info {
    border-left-color: #409eff;
    
    .notification-icon {
      color: #409eff;
    }
  }
  
  &.notification-success {
    border-left-color: #67c23a;
    
    .notification-icon {
      color: #67c23a;
    }
  }
  
  &.notification-warning {
    border-left-color: #e6a23c;
    
    .notification-icon {
      color: #e6a23c;
    }
  }
  
  &.notification-error {
    border-left-color: #f56c6c;
    
    .notification-icon {
      color: #f56c6c;
    }
  }
}

.notification-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
}

.notification-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.notification-close {
  flex-shrink: 0;
  margin-top: -4px;
  margin-right: -4px;
}

// 动画效果
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.notification-move {
  transition: transform 0.3s ease;
}

// 移动端适配
@media (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification-item {
    min-width: auto;
    margin-bottom: 8px;
    padding: 12px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
  
  .notification-actions {
    flex-direction: column;
    gap: 6px;
    
    .el-button {
      width: 100%;
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .notification-item {
    background: #2d2d2d;
    color: #e4e7ed;
    
    .notification-title {
      color: #e4e7ed;
    }
    
    .notification-message {
      color: #c0c4cc;
    }
  }
}
</style>
