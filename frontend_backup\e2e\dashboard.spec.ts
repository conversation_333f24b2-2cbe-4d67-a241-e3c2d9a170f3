import { expect, test } from '@playwright/test'

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到仪表板页面
    await page.goto('/')
  })

  test('should display dashboard page correctly', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/学习仪表板|Dashboard/)

    // 检查主要元素是否存在
    await expect(page.locator('.dashboard')).toBeVisible()
    await expect(page.locator('.page-header')).toBeVisible()
    
    // 检查页面标题
    await expect(page.locator('h2')).toContainText('学习仪表板')
  })

  test('should display statistics cards', async ({ page }) => {
    // 等待统计卡片加载
    await page.waitForSelector('.stat-card', { timeout: 5000 })

    // 检查统计卡片
    const statCards = page.locator('.stat-card')
    await expect(statCards).toHaveCount(4) // 假设有4个统计卡片

    // 检查统计卡片内容
    await expect(page.locator('.stat-card')).toContainText(['总任务数', '活跃任务', '已完成', '今日复习'])
  })

  test('should navigate to task management', async ({ page }) => {
    // 查找并点击任务管理链接
    const taskLink = page.locator('a[href*="/tasks"], button:has-text("任务管理")')
    
    if (await taskLink.count() > 0) {
      await taskLink.first().click()
      
      // 验证导航是否成功
      await expect(page).toHaveURL(/.*\/tasks/)
      await expect(page.locator('h2')).toContainText('任务管理')
    }
  })

  test('should navigate to review page', async ({ page }) => {
    // 查找并点击复习计划链接
    const reviewLink = page.locator('a[href*="/review"], button:has-text("复习计划")')
    
    if (await reviewLink.count() > 0) {
      await reviewLink.first().click()
      
      // 验证导航是否成功
      await expect(page).toHaveURL(/.*\/review/)
      await expect(page.locator('h2')).toContainText('复习计划')
    }
  })

  test('should display charts when data is available', async ({ page }) => {
    // 等待图表容器加载
    await page.waitForSelector('.charts-section, .chart-container', { timeout: 10000 })

    // 检查是否有图表容器
    const chartContainers = page.locator('.chart-container, .echarts-container')
    
    if (await chartContainers.count() > 0) {
      await expect(chartContainers.first()).toBeVisible()
    }
  })

  test('should handle responsive design', async ({ page }) => {
    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.dashboard')).toBeVisible()

    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('.dashboard')).toBeVisible()

    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.dashboard')).toBeVisible()
  })

  test('should show loading states', async ({ page }) => {
    // 刷新页面以观察加载状态
    await page.reload()

    // 检查是否有加载指示器
    const loadingIndicators = page.locator('.el-loading, .loading, [loading="true"]')
    
    // 加载指示器可能很快消失，所以使用较短的超时
    if (await loadingIndicators.count() > 0) {
      await expect(loadingIndicators.first()).toBeVisible()
    }
  })

  test('should handle empty data states', async ({ page }) => {
    // 这个测试可能需要模拟空数据状态
    // 或者在测试环境中设置空数据

    // 检查是否有空状态提示
    const emptyStates = page.locator('.el-empty, .empty-state, .no-data')
    
    // 如果有空状态，验证其显示
    if (await emptyStates.count() > 0) {
      await expect(emptyStates.first()).toBeVisible()
    }
  })

  test('should be accessible', async ({ page }) => {
    // 检查页面是否有适当的标题结构
    const headings = page.locator('h1, h2, h3, h4, h5, h6')
    await expect(headings).toHaveCount(1) // 至少有一个标题

    // 检查是否有适当的语义化标签
    await expect(page.locator('main, [role="main"]')).toBeVisible()

    // 检查是否有跳转链接（如果实现了）
    // 检查跳转链接存在性
    page.locator('a[href="#main"], .skip-link')
    // 跳转链接是可选的，所以不强制要求
  })

  test('should handle keyboard navigation', async ({ page }) => {
    // 测试Tab键导航
    await page.keyboard.press('Tab')
    
    // 检查焦点是否正确移动
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()

    // 继续Tab导航
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // 验证焦点仍然可见
    await expect(page.locator(':focus')).toBeVisible()
  })

  test('should update data in real-time', async ({ page }) => {
    // 记录初始状态
    // 获取初始任务数量用于验证
    await page.locator('.stat-card .stat-value').first().textContent()

    // 如果有刷新按钮，点击它
    const refreshButton = page.locator('button:has-text("刷新"), .refresh-btn')
    
    if (await refreshButton.count() > 0) {
      await refreshButton.click()
      
      // 等待数据更新
      await page.waitForTimeout(1000)
      
      // 验证数据可能已更新（或至少没有错误）
      await expect(page.locator('.stat-card .stat-value').first()).toBeVisible()
    }
  })

  test('should handle error states gracefully', async ({ page }) => {
    // 这个测试可能需要模拟网络错误或服务器错误
    
    // 检查是否有错误处理机制
    const errorElements = page.locator('.error-message, .el-alert, .error-state')
    
    // 如果有错误状态，验证其显示
    if (await errorElements.count() > 0) {
      await expect(errorElements.first()).toBeVisible()
    }
  })

  test('should maintain state across page refreshes', async ({ page }) => {
    // 如果有用户设置或状态，测试其持久性
    
    // 刷新页面
    await page.reload()
    
    // 验证页面仍然正常工作
    await expect(page.locator('.dashboard')).toBeVisible()
    await expect(page.locator('.page-header')).toBeVisible()
  })
})
