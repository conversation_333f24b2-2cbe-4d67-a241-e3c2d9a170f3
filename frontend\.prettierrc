{"semi": false, "singleQuote": true, "tabWidth": 2, "useTabs": false, "trailingComma": "none", "printWidth": 100, "endOfLine": "lf", "arrowParens": "avoid", "bracketSpacing": true, "bracketSameLine": false, "quoteProps": "as-needed", "vueIndentScriptAndStyle": true, "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "proseWrap": "preserve", "insertPragma": false, "requirePragma": false, "overrides": [{"files": "*.vue", "options": {"parser": "vue"}}, {"files": "*.json", "options": {"trailingComma": "none"}}, {"files": "*.md", "options": {"proseWrap": "always", "printWidth": 80}}]}