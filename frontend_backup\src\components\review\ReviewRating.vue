<template>
  <div class="review-rating">
    <ElCard class="rating-card">
      <template #header>
        <div class="card-header">
          <ElIcon><Star /></ElIcon>
          <span>复习效果评分</span>
        </div>
      </template>
      
      <div class="rating-content">
        <!-- 任务信息 -->
        <div class="task-info">
          <h3>{{ task?.title }}</h3>
          <p class="task-description">{{ task?.description }}</p>
          <div class="task-meta">
            <ElTag :type="getSubjectTagType(task?.subject)">{{ task?.subject }}</ElTag>
            <span class="duration">本次复习时长：{{ formatDuration(actualDuration) }}</span>
          </div>
        </div>
        
        <!-- 评分区域 -->
        <div class="rating-section">
          <h4>请为本次复习效果评分</h4>
          <p class="rating-desc">评分将影响下次复习的时间间隔</p>
          
          <div class="rating-options">
            <div
              v-for="option in ratingOptions"
              :key="option.value"
              :class="[
                'rating-option',
                { 'selected': selectedRating === option.value }
              ]"
              @click="selectRating(option.value)"
            >
              <div class="rating-icon">
                <ElIcon :size="24">
                  <component :is="option.icon" />
                </ElIcon>
              </div>
              <div class="rating-info">
                <div class="rating-score">{{ option.value }}分</div>
                <div class="rating-label">{{ option.label }}</div>
                <div class="rating-description">{{ option.description }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 复习笔记 -->
        <div class="notes-section">
          <h4>复习笔记（可选）</h4>
          <ElInput
            v-model="reviewNotes"
            type="textarea"
            :rows="3"
            placeholder="记录本次复习的心得、难点或需要注意的地方..."
            maxlength="500"
            show-word-limit
          />
        </div>
        
        <!-- 难度感知 -->
        <div class="difficulty-section">
          <h4>感知难度</h4>
          <p class="difficulty-desc">这个内容对您来说有多难？</p>
          <ElRate
            v-model="perceivedDifficulty"
            :max="5"
            :colors="difficultyColors"
            :texts="difficultyTexts"
            show-text
            text-color="#ff9900"
          />
        </div>
        
        <!-- 下次复习时间预览 -->
        <div v-if="selectedRating" class="next-review-preview">
          <h4>下次复习时间</h4>
          <div class="preview-info">
            <ElIcon><Clock /></ElIcon>
            <span>{{ getNextReviewTimeText() }}</span>
          </div>
          <p class="preview-desc">
            基于您的评分，系统建议在 {{ getIntervalText() }} 后进行下次复习
          </p>
        </div>
      </div>
      
      <template #footer>
        <div class="rating-actions">
          <ElButton @click="$emit('cancel')">
            取消
          </ElButton>
          <ElButton
            type="primary"
            :disabled="!selectedRating"
            @click="submitRating"
          >
            提交评分
          </ElButton>
        </div>
      </template>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElButton, ElCard, ElIcon, ElInput, ElMessage, ElRate, ElTag } from 'element-plus'
import {
  Check,
  CircleClose,
  Clock,
  Star,
  Trophy,
  Warning
} from '@element-plus/icons-vue'
import type { Task } from '@/types'
import { EbbinghausAlgorithm } from '@/services/EbbinghausAlgorithm'

interface Props {
  task: Task | null
  reviewIndex: number
  startTime: string
  endTime: string
}

interface Emits {
  (e: 'submit', rating: {
    quality: number
    duration: number
    notes: string
    difficulty: number
    nextReviewTime: string
  }): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const selectedRating = ref<number>(0)
const reviewNotes = ref('')
const perceivedDifficulty = ref(3)
const algorithm = new EbbinghausAlgorithm()

// 评分选项
const ratingOptions = [
  {
    value: 1,
    label: '很差',
    description: '完全不记得，需要重新学习',
    icon: CircleClose
  },
  {
    value: 2,
    label: '较差',
    description: '记得很少，有很多遗忘',
    icon: Warning
  },
  {
    value: 3,
    label: '一般',
    description: '记得一些，但不够清晰',
    icon: Warning
  },
  {
    value: 4,
    label: '良好',
    description: '记得比较清楚，偶有遗忘',
    icon: Check
  },
  {
    value: 5,
    label: '很好',
    description: '记得非常清楚，完全掌握',
    icon: Trophy
  }
]

// 难度颜色和文本
const difficultyColors = ['#99A9BF', '#F7BA2A', '#FF9900', '#FF5722', '#E53E3E']
const difficultyTexts = ['很简单', '简单', '一般', '困难', '很困难']

// 计算属性
const actualDuration = computed(() => {
  if (!props.startTime || !props.endTime) {return 0}
  const start = new Date(props.startTime).getTime()
  const end = new Date(props.endTime).getTime()
  return Math.round((end - start) / 1000 / 60) // 分钟
})

// 方法
const selectRating = (rating: number) => {
  selectedRating.value = rating
}

const getSubjectTagType = (subject?: string) => {
  const typeMap: Record<string, string> = {
    '数学': 'primary',
    '英语': 'success',
    '物理': 'warning',
    '化学': 'danger',
    '语文': 'info'
  }
  return typeMap[subject || ''] || 'default'
}

const formatDuration = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins}分钟`
}

const getNextReviewTimeText = () => {
  if (!selectedRating.value) {return ''}
  
  const nextInterval = algorithm.calculateNextInterval(
    props.reviewIndex,
    selectedRating.value,
    perceivedDifficulty.value
  )
  
  const nextTime = new Date(Date.now() + nextInterval)
  return nextTime.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getIntervalText = () => {
  if (!selectedRating.value) {return ''}
  
  const nextInterval = algorithm.calculateNextInterval(
    props.reviewIndex,
    selectedRating.value,
    perceivedDifficulty.value
  )
  
  const days = Math.floor(nextInterval / (1000 * 60 * 60 * 24))
  const hours = Math.floor((nextInterval % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((nextInterval % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) {
    return `${days}天${hours > 0 ? hours + '小时' : ''}`
  } else if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
  } else {
    return `${minutes}分钟`
  }
}

const submitRating = () => {
  if (!selectedRating.value) {
    ElMessage.warning('请选择评分')
    return
  }
  
  const nextInterval = algorithm.calculateNextInterval(
    props.reviewIndex,
    selectedRating.value,
    perceivedDifficulty.value
  )
  
  const nextReviewTime = new Date(Date.now() + nextInterval).toISOString()
  
  emit('submit', {
    quality: selectedRating.value,
    duration: actualDuration.value,
    notes: reviewNotes.value,
    difficulty: perceivedDifficulty.value,
    nextReviewTime
  })
  
  ElMessage.success('评分提交成功')
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped lang="scss">
.review-rating {
  max-width: 600px;
  margin: 0 auto;
}

.rating-card {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #303133;
  }
}

.rating-content {
  .task-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 18px;
    }
    
    .task-description {
      margin: 0 0 12px 0;
      color: #606266;
      line-height: 1.5;
    }
    
    .task-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .duration {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .rating-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .rating-desc {
      margin: 0 0 16px 0;
      color: #909399;
      font-size: 14px;
    }
    
    .rating-options {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .rating-option {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }
      
      &.selected {
        border-color: #409eff;
        background: #e6f7ff;
        
        .rating-icon {
          color: #409eff;
        }
      }
      
      .rating-icon {
        flex-shrink: 0;
        color: #c0c4cc;
        transition: color 0.3s ease;
      }
      
      .rating-info {
        flex: 1;
        
        .rating-score {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .rating-label {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
          margin-bottom: 4px;
        }
        
        .rating-description {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }
  
  .notes-section,
  .difficulty-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .difficulty-desc {
      margin: 0 0 12px 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .next-review-preview {
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 8px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .preview-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #409eff;
    }
    
    .preview-desc {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.rating-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 移动端适配
@media (max-width: 768px) {
  .review-rating {
    max-width: none;
    margin: 0;
  }
  
  .rating-content {
    .rating-section {
      .rating-option {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        
        .rating-info {
          .rating-score {
            font-size: 20px;
          }
        }
      }
    }
  }
  
  .rating-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
