# [DES-UI-001] 用户界面设计规范

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的用户界面技术实现规范，包括 [TERM-022] 前端技术栈架构、组件设计、状态管理和 [TERM-021] 交互设计技术实现方案。

## 🏗️ 前端架构设计

### [DES-ARCH-003] 前端技术架构
**设计ID**：DES-ARCH-003  
**架构模式**：组件化 + 模块化 + 状态管理  
**实现需求**：[REQ-UX-001] 用户体验原则

**架构层次图**：
```
┌─────────────────────────────────────────┐
│              视图层 (View Layer)        │
│                Vue 3 组件               │
│  ┌─────────────┬─────────────────────┐ │
│  │ 页面组件    │ Layout Components   │ │
│  │ Page        │ Header/Sidebar/     │ │
│  │ Components  │ Footer              │ │
│  ├─────────────┼─────────────────────┤ │
│  │ 业务组件    │ 通用组件            │ │
│  │ Business    │ Common Components   │ │
│  │ Components  │ Button/Input/Modal  │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
                    ↓ 状态管理
┌─────────────────────────────────────────┐
│            状态管理层 (State Layer)     │
│                  Pinia                  │
│  ┌─────────────┬─────────────────────┐ │
│  │ 业务状态    │ 应用状态            │ │
│  │ Task Store  │ App Store           │ │
│  │ MindMap     │ User Store          │ │
│  │ Store       │ UI Store            │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
                    ↓ 服务调用
┌─────────────────────────────────────────┐
│            服务层 (Service Layer)       │
│                API Services             │
│  ┌─────────────┬─────────────────────┐ │
│  │ HTTP Client │ 工具服务            │ │
│  │ Axios       │ Utils/Helpers       │ │
│  │ Interceptor │ Validators          │ │
│  └─────────────┴─────────────────────┘ │
└─────────────────────────────────────────┘
```

**技术栈配置**：
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue({
      script: {
        defineModel: true,
        propsDestructure: true
      }
    })
  ],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@services': resolve(__dirname, 'src/services'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types')
    }
  },
  
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },
  
  build: {
    target: 'es2020',
    cssCodeSplit: true,
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'pinia'],
          'ui': ['element-plus'],
          'utils': ['axios', 'dayjs'],
          'mindmap': ['cytoscape']
        }
      }
    }
  },
  
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  }
})
```

**项目结构**：
```
src/
├── components/           # 组件目录
│   ├── common/          # 通用组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── views/               # 页面视图
│   ├── task/           # 任务管理页面
│   ├── mindmap/        # 思维导图页面
│   ├── analysis/       # 学习分析页面
│   └── settings/       # 设置页面
├── stores/              # 状态管理
│   ├── task.ts         # 任务状态
│   ├── mindmap.ts      # 思维导图状态
│   ├── user.ts         # 用户状态
│   └── app.ts          # 应用状态
├── services/            # API服务
│   ├── api/            # API接口
│   ├── http/           # HTTP客户端
│   └── utils/          # 工具函数
├── types/               # 类型定义
├── styles/              # 样式文件
├── router/              # 路由配置
└── main.ts             # 应用入口
```

**实现需求**：[REQ-UX-001] 用户体验原则  
**相关设计**：[DES-COMP-001] ~ [DES-COMP-005]

## 🧩 组件设计规范

### [DES-COMP-001] 任务管理组件
**组件ID**：DES-COMP-001  
**组件名称**：TaskManager 任务管理组件  
**实现需求**：[REQ-FUNC-005] 任务列表管理功能

**组件架构**：
```typescript
// TaskManager.vue
<template>
  <div class="task-manager">
    <!-- 工具栏 -->
    <TaskToolbar
      v-model:filters="filters"
      v-model:sortBy="sortBy"
      @create-task="handleCreateTask"
      @refresh="handleRefresh"
    />
    
    <!-- 任务列表 -->
    <TaskList
      :tasks="filteredTasks"
      :loading="loading"
      :selection="selectedTasks"
      @select="handleTaskSelect"
      @edit="handleTaskEdit"
      @delete="handleTaskDelete"
      @status-change="handleStatusChange"
    />
    
    <!-- 分页组件 -->
    <TaskPagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="totalTasks"
      @change="handlePageChange"
    />
    
    <!-- 任务编辑对话框 -->
    <TaskEditDialog
      v-model:visible="editDialogVisible"
      :task="editingTask"
      @save="handleTaskSave"
      @cancel="handleEditCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useTaskStore } from '@/stores/task'
import { useLoadingStore } from '@/stores/loading'
import type { Task, TaskFilters, TaskSort } from '@/types/task'

// 组件属性
interface Props {
  mode?: 'list' | 'grid' | 'kanban'
  showFilters?: boolean
  showPagination?: boolean
  selectable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'list',
  showFilters: true,
  showPagination: true,
  selectable: false
})

// 组件事件
interface Emits {
  taskSelect: [tasks: Task[]]
  taskCreate: [task: Task]
  taskUpdate: [task: Task]
  taskDelete: [taskId: string]
}

const emit = defineEmits<Emits>()

// 状态管理
const taskStore = useTaskStore()
const loadingStore = useLoadingStore()

// 响应式数据
const filters = ref<TaskFilters>({
  subject: '',
  status: '',
  priority: null,
  keyword: '',
  dateRange: null
})

const sortBy = ref<TaskSort>({
  field: 'createdAt',
  order: 'desc'
})

const currentPage = ref(1)
const pageSize = ref(20)
const selectedTasks = ref<Task[]>([])
const editDialogVisible = ref(false)
const editingTask = ref<Task | null>(null)

// 计算属性
const loading = computed(() => loadingStore.isLoading('tasks'))

const filteredTasks = computed(() => {
  return taskStore.getFilteredTasks(filters.value, sortBy.value)
})

const totalTasks = computed(() => taskStore.totalCount)

// 方法
const handleCreateTask = () => {
  editingTask.value = null
  editDialogVisible.value = true
}

const handleTaskEdit = (task: Task) => {
  editingTask.value = { ...task }
  editDialogVisible.value = true
}

const handleTaskDelete = async (taskId: string) => {
  try {
    await taskStore.deleteTask(taskId)
    emit('taskDelete', taskId)
    ElMessage.success('任务删除成功')
  } catch (error) {
    ElMessage.error('任务删除失败')
  }
}

const handleTaskSave = async (task: Task) => {
  try {
    if (task.id) {
      await taskStore.updateTask(task)
      emit('taskUpdate', task)
      ElMessage.success('任务更新成功')
    } else {
      const newTask = await taskStore.createTask(task)
      emit('taskCreate', newTask)
      ElMessage.success('任务创建成功')
    }
    editDialogVisible.value = false
  } catch (error) {
    ElMessage.error('任务保存失败')
  }
}

const handleTaskSelect = (tasks: Task[]) => {
  selectedTasks.value = tasks
  emit('taskSelect', tasks)
}

const handleStatusChange = async (taskId: string, status: TaskStatus) => {
  try {
    await taskStore.updateTaskStatus(taskId, status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const handleRefresh = () => {
  taskStore.fetchTasks({
    page: currentPage.value,
    pageSize: pageSize.value,
    filters: filters.value,
    sort: sortBy.value
  })
}

const handlePageChange = () => {
  handleRefresh()
}

// 监听器
watch([filters, sortBy], () => {
  currentPage.value = 1
  handleRefresh()
}, { deep: true })

// 生命周期
onMounted(() => {
  handleRefresh()
})

// 暴露给父组件的方法
defineExpose({
  refresh: handleRefresh,
  getSelectedTasks: () => selectedTasks.value,
  clearSelection: () => { selectedTasks.value = [] }
})
</script>

<style scoped lang="scss">
.task-manager {
  @apply flex flex-col h-full;
  
  .task-toolbar {
    @apply flex-shrink-0 mb-4;
  }
  
  .task-list {
    @apply flex-1 overflow-hidden;
  }
  
  .task-pagination {
    @apply flex-shrink-0 mt-4 flex justify-center;
  }
}
</style>
```

**组件特性**：
- **响应式设计**：支持桌面端和触摸屏设备
- **状态管理**：集成Pinia状态管理
- **类型安全**：完整的TypeScript类型定义
- **可配置性**：支持多种显示模式和配置选项
- **事件通信**：标准的Vue 3事件系统

**实现需求**：[REQ-FUNC-005] 任务列表管理功能  
**相关设计**：[DES-STATE-001] 状态管理设计

### [DES-COMP-002] 思维导图组件
**组件ID**：DES-COMP-002  
**组件名称**：MindMapEditor 思维导图编辑器组件  
**实现需求**：[REQ-FUNC-009] 思维导图创建功能

**组件实现**：
```typescript
// MindMapEditor.vue
<template>
  <div class="mindmap-editor">
    <!-- 工具栏 -->
    <MindMapToolbar
      :can-undo="canUndo"
      :can-redo="canRedo"
      :zoom-level="zoomLevel"
      @undo="handleUndo"
      @redo="handleRedo"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @fit-view="handleFitView"
      @save="handleSave"
      @export="handleExport"
    />
    
    <!-- 画布容器 -->
    <div
      ref="canvasContainer"
      class="mindmap-canvas"
      @contextmenu.prevent
    />
    
    <!-- 属性面板 -->
    <MindMapPropertyPanel
      v-if="selectedNode"
      :node="selectedNode"
      :visible="propertyPanelVisible"
      @update="handleNodeUpdate"
      @close="handlePropertyPanelClose"
    />
    
    <!-- 节点编辑对话框 -->
    <NodeEditDialog
      v-model:visible="nodeEditVisible"
      :node="editingNode"
      @save="handleNodeSave"
      @cancel="handleNodeEditCancel"
    />
    
    <!-- 任务关联对话框 -->
    <TaskAssociationDialog
      v-model:visible="taskAssociationVisible"
      :node="associatingNode"
      @associate="handleTaskAssociate"
      @cancel="handleTaskAssociationCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { MindMapRenderer } from '@/services/mindmap/renderer'
import { useMindMapStore } from '@/stores/mindmap'
import type { MindMapNode, MindMapConfig } from '@/types/mindmap'

// 组件属性
interface Props {
  mindMapId?: string
  readonly?: boolean
  showToolbar?: boolean
  showPropertyPanel?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showToolbar: true,
  showPropertyPanel: true
})

// 组件事件
interface Emits {
  nodeSelect: [node: MindMapNode | null]
  nodeCreate: [node: MindMapNode]
  nodeUpdate: [node: MindMapNode]
  nodeDelete: [nodeId: string]
  save: [mindMapData: any]
}

const emit = defineEmits<Emits>()

// 状态管理
const mindMapStore = useMindMapStore()

// 响应式数据
const canvasContainer = ref<HTMLElement>()
const mindMapRenderer = ref<MindMapRenderer>()
const selectedNode = ref<MindMapNode | null>(null)
const editingNode = ref<MindMapNode | null>(null)
const associatingNode = ref<MindMapNode | null>(null)

const canUndo = ref(false)
const canRedo = ref(false)
const zoomLevel = ref(1)
const propertyPanelVisible = ref(false)
const nodeEditVisible = ref(false)
const taskAssociationVisible = ref(false)

// 思维导图配置
const mindMapConfig: MindMapConfig = {
  layout: {
    name: 'cose',
    animate: true,
    fit: true
  },
  style: [], // 样式在renderer中定义
  interaction: {
    zoomingEnabled: !props.readonly,
    panningEnabled: true,
    boxSelectionEnabled: !props.readonly
  },
  performance: {
    textureOnViewport: false,
    motionBlur: true,
    wheelSensitivity: 0.1
  }
}

// 方法
const initializeMindMap = async () => {
  if (!canvasContainer.value) return

  mindMapRenderer.value = new MindMapRenderer(
    canvasContainer.value,
    mindMapConfig
  )

  // 设置事件监听
  setupEventListeners()

  // 加载思维导图数据
  if (props.mindMapId) {
    await loadMindMapData(props.mindMapId)
  } else {
    // 创建新的思维导图
    createNewMindMap()
  }
}

const setupEventListeners = () => {
  if (!mindMapRenderer.value) return

  const renderer = mindMapRenderer.value

  // 节点选择事件
  renderer.on('nodeSelect', (node: MindMapNode | null) => {
    selectedNode.value = node
    propertyPanelVisible.value = !!node
    emit('nodeSelect', node)
  })

  // 节点双击编辑事件
  renderer.on('nodeDoubleClick', (node: MindMapNode) => {
    if (!props.readonly) {
      editingNode.value = node
      nodeEditVisible.value = true
    }
  })

  // 节点右键菜单事件
  renderer.on('nodeContextMenu', (node: MindMapNode, position: any) => {
    if (!props.readonly) {
      showNodeContextMenu(node, position)
    }
  })

  // 画布点击事件
  renderer.on('canvasClick', (position: any) => {
    if (!props.readonly) {
      showCanvasContextMenu(position)
    }
  })

  // 缩放变化事件
  renderer.on('zoomChange', (level: number) => {
    zoomLevel.value = level
  })

  // 撤销重做状态变化
  renderer.on('undoRedoChange', (undo: boolean, redo: boolean) => {
    canUndo.value = undo
    canRedo.value = redo
  })
}

const loadMindMapData = async (mindMapId: string) => {
  try {
    const mindMapData = await mindMapStore.fetchMindMap(mindMapId)
    mindMapRenderer.value?.importFromJSON(mindMapData)
  } catch (error) {
    ElMessage.error('思维导图加载失败')
  }
}

const createNewMindMap = () => {
  // 创建中心节点
  const centerNode = {
    label: '中心主题',
    type: 'center' as const,
    content: '请编辑中心主题内容'
  }

  mindMapRenderer.value?.addNode(centerNode, { x: 0, y: 0 })
}

const handleNodeUpdate = (node: MindMapNode) => {
  mindMapRenderer.value?.updateNode(node.id, node)
  emit('nodeUpdate', node)
}

const handleNodeSave = (node: MindMapNode) => {
  if (editingNode.value?.id) {
    // 更新现有节点
    handleNodeUpdate(node)
  } else {
    // 创建新节点
    const nodeId = mindMapRenderer.value?.addNode(node)
    if (nodeId) {
      emit('nodeCreate', { ...node, id: nodeId })
    }
  }
  nodeEditVisible.value = false
  editingNode.value = null
}

const handleTaskAssociate = async (taskData: any) => {
  if (!associatingNode.value) return

  try {
    // 创建任务并关联到节点
    const task = await mindMapStore.createTaskFromNode(
      associatingNode.value.id,
      taskData
    )

    // 更新节点状态
    mindMapRenderer.value?.associateTask(
      associatingNode.value.id,
      task.id
    )

    ElMessage.success('任务关联成功')
    taskAssociationVisible.value = false
    associatingNode.value = null
  } catch (error) {
    ElMessage.error('任务关联失败')
  }
}

const handleSave = async () => {
  if (!mindMapRenderer.value) return

  try {
    const mindMapData = mindMapRenderer.value.exportToJSON()
    
    if (props.mindMapId) {
      await mindMapStore.updateMindMap(props.mindMapId, mindMapData)
    } else {
      await mindMapStore.createMindMap(mindMapData)
    }

    emit('save', mindMapData)
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleExport = (format: string) => {
  if (!mindMapRenderer.value) return

  const data = mindMapRenderer.value.exportToJSON()
  
  // 根据格式导出
  switch (format) {
    case 'json':
      downloadJSON(data, 'mindmap.json')
      break
    case 'png':
      exportToPNG()
      break
    case 'svg':
      exportToSVG()
      break
  }
}

// 工具栏操作
const handleUndo = () => mindMapRenderer.value?.undo()
const handleRedo = () => mindMapRenderer.value?.redo()
const handleZoomIn = () => mindMapRenderer.value?.zoomIn()
const handleZoomOut = () => mindMapRenderer.value?.zoomOut()
const handleFitView = () => mindMapRenderer.value?.fitToView()

// 生命周期
onMounted(async () => {
  await nextTick()
  await initializeMindMap()
})

onUnmounted(() => {
  mindMapRenderer.value?.destroy()
})

// 暴露给父组件的方法
defineExpose({
  save: handleSave,
  export: handleExport,
  fitView: handleFitView,
  getSelectedNode: () => selectedNode.value,
  getMindMapData: () => mindMapRenderer.value?.exportToJSON()
})
</script>

<style scoped lang="scss">
.mindmap-editor {
  @apply flex flex-col h-full relative;
  
  .mindmap-toolbar {
    @apply flex-shrink-0 border-b border-gray-200 bg-white;
  }
  
  .mindmap-canvas {
    @apply flex-1 relative overflow-hidden bg-gray-50;
    
    // Cytoscape容器样式
    :deep(.cy-container) {
      @apply w-full h-full;
    }
  }
  
  .mindmap-property-panel {
    @apply absolute right-0 top-0 bottom-0 w-80 bg-white border-l border-gray-200;
    @apply transform transition-transform duration-300;
    
    &.hidden {
      @apply translate-x-full;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .mindmap-editor {
    .mindmap-property-panel {
      @apply w-full;
    }
  }
}
</style>
```

**组件特性**：
- **高性能渲染**：基于Cytoscape.js的图形渲染引擎
- **丰富交互**：支持拖拽、缩放、选择等交互操作
- **任务集成**：与任务管理系统深度集成
- **导入导出**：支持多种格式的导入导出
- **响应式设计**：适配不同屏幕尺寸

**实现需求**：[REQ-FUNC-009] 思维导图创建功能  
**相关设计**：[DES-UI-005] Cytoscape.js集成方案

## 📊 状态管理设计

### [DES-STATE-001] Pinia状态管理架构
**设计ID**：DES-STATE-001  
**状态管理库**：Pinia 2.1+  
**实现需求**：前端状态管理需求

**状态管理架构**：
```typescript
// stores/task.ts - 任务状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi } from '@/services/api/task'
import type { Task, TaskFilters, TaskSort, CreateTaskData } from '@/types/task'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const totalCount = ref(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const pendingTasks = computed(() => 
    tasks.value.filter(task => task.status === 'pending')
  )

  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed')
  )

  const tasksBySubject = computed(() => {
    const grouped: Record<string, Task[]> = {}
    tasks.value.forEach(task => {
      if (!grouped[task.subject]) {
        grouped[task.subject] = []
      }
      grouped[task.subject].push(task)
    })
    return grouped
  })

  const getFilteredTasks = computed(() => {
    return (filters: TaskFilters, sort: TaskSort) => {
      let filtered = [...tasks.value]

      // 应用筛选条件
      if (filters.subject) {
        filtered = filtered.filter(task => task.subject === filters.subject)
      }

      if (filters.status) {
        filtered = filtered.filter(task => task.status === filters.status)
      }

      if (filters.priority !== null) {
        filtered = filtered.filter(task => task.priority === filters.priority)
      }

      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase()
        filtered = filtered.filter(task => 
          task.title.toLowerCase().includes(keyword) ||
          task.content.toLowerCase().includes(keyword)
        )
      }

      if (filters.dateRange) {
        const [start, end] = filters.dateRange
        filtered = filtered.filter(task => {
          const taskDate = new Date(task.createdAt)
          return taskDate >= start && taskDate <= end
        })
      }

      // 应用排序
      filtered.sort((a, b) => {
        const aValue = a[sort.field]
        const bValue = b[sort.field]
        
        if (sort.order === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })

      return filtered
    }
  })

  // 操作方法
  const fetchTasks = async (params?: {
    page?: number
    pageSize?: number
    filters?: TaskFilters
    sort?: TaskSort
  }) => {
    loading.value = true
    error.value = null

    try {
      const response = await taskApi.getTasks(params)
      tasks.value = response.data.tasks
      totalCount.value = response.data.pagination.total
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: CreateTaskData): Promise<Task> => {
    loading.value = true
    error.value = null

    try {
      const response = await taskApi.createTask(taskData)
      const newTask = response.data
      
      // 添加到本地状态
      tasks.value.unshift(newTask)
      totalCount.value += 1
      
      return newTask
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (task: Task): Promise<Task> => {
    loading.value = true
    error.value = null

    try {
      const response = await taskApi.updateTask(task.id, task)
      const updatedTask = response.data
      
      // 更新本地状态
      const index = tasks.value.findIndex(t => t.id === task.id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }
      
      return updatedTask
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (taskId: string): Promise<void> => {
    loading.value = true
    error.value = null

    try {
      await taskApi.deleteTask(taskId)
      
      // 从本地状态移除
      const index = tasks.value.findIndex(t => t.id === taskId)
      if (index !== -1) {
        tasks.value.splice(index, 1)
        totalCount.value -= 1
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTaskStatus = async (taskId: string, status: TaskStatus): Promise<void> => {
    const task = tasks.value.find(t => t.id === taskId)
    if (!task) return

    const originalStatus = task.status
    
    // 乐观更新
    task.status = status
    task.updatedAt = new Date()

    try {
      await taskApi.updateTaskStatus(taskId, status)
    } catch (err) {
      // 回滚状态
      task.status = originalStatus
      error.value = err instanceof Error ? err.message : '状态更新失败'
      throw err
    }
  }

  const getTaskById = (taskId: string): Task | undefined => {
    return tasks.value.find(task => task.id === taskId)
  }

  const clearTasks = () => {
    tasks.value = []
    currentTask.value = null
    totalCount.value = 0
    error.value = null
  }

  const setCurrentTask = (task: Task | null) => {
    currentTask.value = task
  }

  // 返回状态和方法
  return {
    // 状态
    tasks: readonly(tasks),
    currentTask: readonly(currentTask),
    totalCount: readonly(totalCount),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    pendingTasks,
    completedTasks,
    tasksBySubject,
    getFilteredTasks,

    // 方法
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    getTaskById,
    clearTasks,
    setCurrentTask
  }
})
```

**状态管理特性**：
- **类型安全**：完整的TypeScript类型支持
- **响应式**：基于Vue 3 Composition API
- **持久化**：支持状态持久化到本地存储
- **模块化**：按功能模块划分状态
- **性能优化**：计算属性缓存和乐观更新

**实现需求**：前端状态管理需求  
**相关设计**：[DES-COMP-001] ~ [DES-COMP-005]

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-01-31 | 初始用户界面设计规范创建 | 前端架构师 | 技术负责人 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：前端架构师  
**审核人**：技术负责人  
**状态**：待审核
