<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend模块依赖关系思维导图</title>
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://unpkg.com/dagre@0.8.5/dist/dagre.min.js"></script>
    <script src="https://unpkg.com/cytoscape-dagre@2.5.0/cytoscape-dagre.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            position: relative;
        }
        
        #cy {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
        }
        
        .title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }
        
        select, button {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            margin-bottom: 8px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .legend {
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .stats {
            background: rgba(52, 73, 94, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .stats h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 300px;
            display: none;
        }
        
        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .info-panel p {
            margin: 5px 0;
            font-size: 13px;
            color: #7f8c8d;
        }
        
        .dependency-list {
            margin-top: 10px;
        }
        
        .dependency-item {
            background: #ecf0f1;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            display: inline-block;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="title">Frontend模块依赖分析</div>
            
            <div class="controls">
                <div class="control-group">
                    <label>布局算法</label>
                    <select id="layoutSelect">
                        <option value="dagre">层次布局 (Dagre)</option>
                        <option value="circle">圆形布局</option>
                        <option value="grid">网格布局</option>
                        <option value="concentric">同心圆布局</option>
                        <option value="breadthfirst">广度优先</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <button onclick="fitToView()">适应视图</button>
                    <button onclick="resetZoom()">重置缩放</button>
                    <button onclick="exportImage()">导出图片</button>
                    <button onclick="toggleEdgeLabels()">切换连线标签</button>
                </div>
            </div>
            
            <div class="legend">
                <h4>图例说明</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>应用入口</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>状态管理</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #F7DC6F;"></div>
                    <span>业务逻辑</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #BB8FCE;"></div>
                    <span>类型定义</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #85C1E9;"></div>
                    <span>工具函数</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #F8C471;"></div>
                    <span>UI组件</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #AED6F1;"></div>
                    <span>页面组件</span>
                </div>
            </div>
            
            <div class="stats">
                <h4>统计信息</h4>
                <div class="stat-item">
                    <span>总模块数:</span>
                    <span id="totalNodes">-</span>
                </div>
                <div class="stat-item">
                    <span>依赖关系:</span>
                    <span id="totalEdges">-</span>
                </div>
                <div class="stat-item">
                    <span>核心模块:</span>
                    <span id="coreModules">-</span>
                </div>
                <div class="stat-item">
                    <span>最大依赖深度:</span>
                    <span id="maxDepth">-</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div id="cy"></div>
            <div class="info-panel" id="infoPanel">
                <h3 id="nodeTitle">模块信息</h3>
                <p id="nodeDescription"></p>
                <p><strong>类型:</strong> <span id="nodeType"></span></p>
                <p><strong>文件数:</strong> <span id="nodeFileCount"></span></p>
                <div class="dependency-list">
                    <strong>依赖模块:</strong>
                    <div id="nodeDependencies"></div>
                </div>
                <div class="dependency-list">
                    <strong>导出内容:</strong>
                    <div id="nodeExports"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cy;
        let showEdgeLabels = true;
        
        // 嵌入的思维导图数据
        // 注意：数据已直接嵌入HTML中，避免CORS问题
        function loadMindMapData() {
            return Promise.resolve({
                "nodes": [
                    {
                        "data": {
                            "id": "main",
                            "label": "main.ts",
                            "type": "entry",
                            "description": "应用入口点",
                            "dependencies": ["vue", "pinia", "vue-router", "element-plus"],
                            "size": "large",
                            "color": "#FF6B6B"
                        }
                    },
                    {
                        "data": {
                            "id": "app",
                            "label": "App.vue",
                            "type": "root-component",
                            "description": "根组件",
                            "dependencies": ["stores/app", "components/layout", "components/common"],
                            "size": "large",
                            "color": "#4ECDC4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores",
                            "label": "Stores (状态管理)",
                            "type": "module-group",
                            "description": "Pinia状态管理模块",
                            "fileCount": 8,
                            "size": "large",
                            "color": "#45B7D1"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-app",
                            "label": "app.ts",
                            "type": "store",
                            "description": "应用全局状态",
                            "exports": ["useAppStore"],
                            "dependencies": ["pinia", "vue"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-user",
                            "label": "user.ts",
                            "type": "store",
                            "description": "用户状态管理",
                            "exports": ["useUserStore"],
                            "dependencies": ["pinia", "vue"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-task",
                            "label": "task.ts",
                            "type": "store",
                            "description": "任务状态管理",
                            "exports": ["useTaskStore"],
                            "dependencies": ["pinia", "vue", "types", "mock/taskData", "utils/errorHandler"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-review",
                            "label": "review.ts",
                            "type": "store",
                            "description": "复习状态管理",
                            "exports": ["useReviewStore"],
                            "dependencies": ["pinia", "vue", "types", "mock/reviewData"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-learning",
                            "label": "learning.ts",
                            "type": "store",
                            "description": "学习数据状态",
                            "exports": ["useLearningStore"],
                            "dependencies": ["pinia", "vue", "services/TimeEstimationService"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-notification",
                            "label": "notification.ts",
                            "type": "store",
                            "description": "通知状态管理",
                            "exports": ["useNotificationStore"],
                            "dependencies": ["pinia", "vue"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-settings",
                            "label": "settings.ts",
                            "type": "store",
                            "description": "设置状态管理",
                            "exports": ["useSettingsStore"],
                            "dependencies": ["pinia", "vue"],
                            "size": "medium",
                            "color": "#96CEB4"
                        }
                    },
                    {
                        "data": {
                            "id": "services",
                            "label": "Services (业务逻辑)",
                            "type": "module-group",
                            "description": "业务逻辑服务层",
                            "fileCount": 3,
                            "size": "large",
                            "color": "#F7DC6F"
                        }
                    },
                    {
                        "data": {
                            "id": "services-ebbinghaus",
                            "label": "EbbinghausAlgorithm.ts",
                            "type": "service",
                            "description": "艾宾浩斯记忆算法",
                            "exports": ["EbbinghausAlgorithm", "ReviewSession", "LearningProgress"],
                            "dependencies": [],
                            "size": "medium",
                            "color": "#F39C12"
                        }
                    },
                    {
                        "data": {
                            "id": "services-time-estimation",
                            "label": "TimeEstimationService.ts",
                            "type": "service",
                            "description": "智能时间预估服务",
                            "exports": ["TimeEstimationService", "TaskFeatures", "TimeEstimationResult"],
                            "dependencies": [],
                            "size": "medium",
                            "color": "#F39C12"
                        }
                    },
                    {
                        "data": {
                            "id": "services-notification",
                            "label": "NotificationService.ts",
                            "type": "service",
                            "description": "通知服务",
                            "exports": ["NotificationService", "NotificationOptions", "InAppMessage"],
                            "dependencies": [],
                            "size": "medium",
                            "color": "#F39C12"
                        }
                    },
                    {
                        "data": {
                            "id": "types",
                            "label": "Types (类型定义)",
                            "type": "module-group",
                            "description": "TypeScript类型定义",
                            "fileCount": 2,
                            "size": "large",
                            "color": "#BB8FCE"
                        }
                    },
                    {
                        "data": {
                            "id": "types-index",
                            "label": "index.ts",
                            "type": "types",
                            "description": "核心业务类型",
                            "exports": ["Task", "ReviewItem", "Subject", "TaskStatus", "ApiResponse"],
                            "dependencies": [],
                            "size": "medium",
                            "color": "#8E44AD"
                        }
                    },
                    {
                        "data": {
                            "id": "types-mindmap",
                            "label": "mindmap.ts",
                            "type": "types",
                            "description": "思维导图类型",
                            "exports": ["MindMapNode", "MindMapEdge", "MindMap", "MindMapSettings"],
                            "dependencies": [],
                            "size": "medium",
                            "color": "#8E44AD"
                        }
                    },
                    {
                        "data": {
                            "id": "utils",
                            "label": "Utils (工具函数)",
                            "type": "module-group",
                            "description": "通用工具函数",
                            "fileCount": 4,
                            "size": "large",
                            "color": "#85C1E9"
                        }
                    },
                    {
                        "data": {
                            "id": "components",
                            "label": "Components (UI组件)",
                            "type": "module-group",
                            "description": "可复用UI组件",
                            "fileCount": 20,
                            "size": "large",
                            "color": "#F8C471"
                        }
                    },
                    {
                        "data": {
                            "id": "views",
                            "label": "Views (页面组件)",
                            "type": "module-group",
                            "description": "页面级组件",
                            "fileCount": 7,
                            "size": "large",
                            "color": "#AED6F1"
                        }
                    },
                    {
                        "data": {
                            "id": "router",
                            "label": "Router (路由配置)",
                            "type": "config",
                            "description": "Vue Router路由配置",
                            "exports": ["routes"],
                            "dependencies": ["vue-router", "views/*"],
                            "size": "medium",
                            "color": "#D7BDE2"
                        }
                    }
                ],
                "edges": [
                    {
                        "data": {
                            "id": "main-app",
                            "source": "main",
                            "target": "app",
                            "type": "import",
                            "label": "imports"
                        }
                    },
                    {
                        "data": {
                            "id": "main-stores",
                            "source": "main",
                            "target": "stores",
                            "type": "import",
                            "label": "creates pinia"
                        }
                    },
                    {
                        "data": {
                            "id": "main-router",
                            "source": "main",
                            "target": "router",
                            "type": "import",
                            "label": "imports routes"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-stores-task",
                            "source": "stores",
                            "target": "stores-task",
                            "type": "contains",
                            "label": "contains"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-types",
                            "source": "stores",
                            "target": "types",
                            "type": "import",
                            "label": "imports types"
                        }
                    },
                    {
                        "data": {
                            "id": "stores-services",
                            "source": "stores",
                            "target": "services",
                            "type": "import",
                            "label": "uses services"
                        }
                    },
                    {
                        "data": {
                            "id": "components-stores",
                            "source": "components",
                            "target": "stores",
                            "type": "uses",
                            "label": "uses stores"
                        }
                    },
                    {
                        "data": {
                            "id": "views-components",
                            "source": "views",
                            "target": "components",
                            "type": "uses",
                            "label": "uses components"
                        }
                    },
                    {
                        "data": {
                            "id": "views-stores",
                            "source": "views",
                            "target": "stores",
                            "type": "uses",
                            "label": "uses stores"
                        }
                    },
                    {
                        "data": {
                            "id": "router-views",
                            "source": "router",
                            "target": "views",
                            "type": "routes-to",
                            "label": "routes to"
                        }
                    }
                ]
            });
        }

        // 初始化Cytoscape
        async function initCytoscape() {
            const data = await loadMindMapData();
            
            cy = cytoscape({
                container: document.getElementById('cy'),
                elements: [...data.nodes, ...data.edges],
                style: [
                    {
                        selector: 'node',
                        style: {
                            'background-color': 'data(color)',
                            'label': 'data(label)',
                            'text-valign': 'center',
                            'text-halign': 'center',
                            'color': '#2c3e50',
                            'font-size': '12px',
                            'font-weight': 'bold',
                            'text-wrap': 'wrap',
                            'text-max-width': '120px',
                            'width': function(ele) {
                                const size = ele.data('size');
                                return size === 'large' ? 80 : size === 'medium' ? 60 : 40;
                            },
                            'height': function(ele) {
                                const size = ele.data('size');
                                return size === 'large' ? 80 : size === 'medium' ? 60 : 40;
                            },
                            'border-width': 2,
                            'border-color': '#34495e',
                            'border-opacity': 0.8,
                            'shadow-blur': 10,
                            'shadow-color': '#000',
                            'shadow-opacity': 0.3,
                            'shadow-offset-x': 2,
                            'shadow-offset-y': 2
                        }
                    },
                    {
                        selector: 'edge',
                        style: {
                            'width': 2,
                            'line-color': '#7f8c8d',
                            'target-arrow-color': '#7f8c8d',
                            'target-arrow-shape': 'triangle',
                            'curve-style': 'bezier',
                            'arrow-scale': 1.2,
                            'label': showEdgeLabels ? 'data(label)' : '',
                            'font-size': '10px',
                            'text-rotation': 'autorotate',
                            'text-margin-y': -10,
                            'color': '#34495e'
                        }
                    },
                    {
                        selector: 'node:selected',
                        style: {
                            'border-width': 4,
                            'border-color': '#e74c3c',
                            'shadow-blur': 20,
                            'shadow-color': '#e74c3c',
                            'shadow-opacity': 0.6
                        }
                    },
                    {
                        selector: 'edge:selected',
                        style: {
                            'width': 4,
                            'line-color': '#e74c3c',
                            'target-arrow-color': '#e74c3c'
                        }
                    }
                ],
                layout: {
                    name: 'dagre',
                    rankDir: 'TB',
                    spacingFactor: 1.5,
                    nodeDimensionsIncludeLabels: true
                }
            });
            
            // 添加事件监听
            setupEventListeners();
            updateStats();
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 节点点击事件
            cy.on('tap', 'node', function(evt) {
                const node = evt.target;
                showNodeInfo(node);
            });
            
            // 点击空白区域隐藏信息面板
            cy.on('tap', function(evt) {
                if (evt.target === cy) {
                    hideNodeInfo();
                }
            });
            
            // 布局选择器事件
            document.getElementById('layoutSelect').addEventListener('change', function() {
                changeLayout(this.value);
            });
        }
        
        // 显示节点信息
        function showNodeInfo(node) {
            const data = node.data();
            const infoPanel = document.getElementById('infoPanel');
            
            document.getElementById('nodeTitle').textContent = data.label;
            document.getElementById('nodeDescription').textContent = data.description || '无描述';
            document.getElementById('nodeType').textContent = data.type || '未知';
            document.getElementById('nodeFileCount').textContent = data.fileCount || '1';
            
            // 显示依赖
            const dependenciesDiv = document.getElementById('nodeDependencies');
            dependenciesDiv.innerHTML = '';
            if (data.dependencies && data.dependencies.length > 0) {
                data.dependencies.forEach(dep => {
                    const span = document.createElement('span');
                    span.className = 'dependency-item';
                    span.textContent = dep;
                    dependenciesDiv.appendChild(span);
                });
            } else {
                dependenciesDiv.textContent = '无外部依赖';
            }
            
            // 显示导出
            const exportsDiv = document.getElementById('nodeExports');
            exportsDiv.innerHTML = '';
            if (data.exports && data.exports.length > 0) {
                data.exports.forEach(exp => {
                    const span = document.createElement('span');
                    span.className = 'dependency-item';
                    span.textContent = exp;
                    exportsDiv.appendChild(span);
                });
            } else {
                exportsDiv.textContent = '无导出内容';
            }
            
            infoPanel.style.display = 'block';
        }
        
        // 隐藏节点信息
        function hideNodeInfo() {
            document.getElementById('infoPanel').style.display = 'none';
        }
        
        // 更改布局
        function changeLayout(layoutName) {
            const layoutOptions = {
                name: layoutName,
                animate: true,
                animationDuration: 1000
            };
            
            switch (layoutName) {
                case 'dagre':
                    layoutOptions.rankDir = 'TB';
                    layoutOptions.spacingFactor = 1.5;
                    break;
                case 'circle':
                    layoutOptions.radius = 300;
                    break;
                case 'grid':
                    layoutOptions.spacingFactor = 1.5;
                    break;
                case 'concentric':
                    layoutOptions.spacingFactor = 1.5;
                    layoutOptions.concentric = function(node) {
                        return node.degree();
                    };
                    break;
                case 'breadthfirst':
                    layoutOptions.directed = true;
                    layoutOptions.spacingFactor = 1.5;
                    break;
            }
            
            cy.layout(layoutOptions).run();
        }
        
        // 适应视图
        function fitToView() {
            cy.fit();
        }
        
        // 重置缩放
        function resetZoom() {
            cy.zoom(1);
            cy.center();
        }
        
        // 导出图片
        function exportImage() {
            const png64 = cy.png({ scale: 2, full: true });
            const link = document.createElement('a');
            link.download = 'frontend-dependency-mindmap.png';
            link.href = png64;
            link.click();
        }
        
        // 切换连线标签
        function toggleEdgeLabels() {
            showEdgeLabels = !showEdgeLabels;
            cy.style()
                .selector('edge')
                .style('label', showEdgeLabels ? 'data(label)' : '')
                .update();
        }
        
        // 更新统计信息
        function updateStats() {
            const nodes = cy.nodes();
            const edges = cy.edges();
            
            document.getElementById('totalNodes').textContent = nodes.length;
            document.getElementById('totalEdges').textContent = edges.length;
            
            // 计算核心模块（连接数最多的模块）
            let maxDegree = 0;
            let coreModules = 0;
            nodes.forEach(node => {
                const degree = node.degree();
                if (degree > maxDegree) {
                    maxDegree = degree;
                }
                if (degree >= 3) {
                    coreModules++;
                }
            });
            
            document.getElementById('coreModules').textContent = coreModules;
            document.getElementById('maxDepth').textContent = maxDegree;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCytoscape();
        });
    </script>
</body>
</html>
