<template>
  <div class="learning-curve-chart">
    <div v-if="reviews.length === 0" class="empty-chart">
      <el-empty description="暂无数据" />
    </div>
    
    <div v-else class="chart-container">
      <svg class="curve-svg" :viewBox="`0 0 ${svgWidth} ${svgHeight}`">
        <!-- 网格线 -->
        <defs>
          <pattern id="curveGrid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#curveGrid)" />

        <!-- Y轴刻度线 -->
        <g class="y-axis">
          <line
            v-for="tick in yAxisTicks"
            :key="tick"
            :x1="40"
            :y1="getYPosition(tick)"
            :x2="svgWidth"
            :y2="getYPosition(tick)"
            stroke="#e0e0e0"
            stroke-width="1"
            stroke-dasharray="2,2"
          />
          <text
            v-for="tick in yAxisTicks"
            :key="`label-${tick}`"
            :x="35"
            :y="getYPosition(tick) + 4"
            font-size="10"
            fill="#999"
            text-anchor="end"
          >
            {{ tick }}
          </text>
        </g>

        <!-- 评分曲线 -->
        <path
          :d="ratingPath"
          fill="none"
          stroke="var(--el-color-primary)"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <!-- 评分区域填充 -->
        <path
          :d="ratingAreaPath"
          fill="url(#ratingGradient)"
          opacity="0.3"
        />

        <!-- 时长曲线 -->
        <path
          :d="durationPath"
          fill="none"
          stroke="var(--el-color-success)"
          stroke-width="2"
          stroke-dasharray="5,5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />

        <!-- 数据点 -->
        <g class="data-points">
          <circle
            v-for="(point, index) in chartPoints"
            :key="`rating-${index}`"
            :cx="point.x"
            :cy="point.ratingY"
            r="4"
            fill="var(--el-color-primary)"
            stroke="white"
            stroke-width="2"
            class="data-point"
            @mouseenter="showTooltip($event, reviews[index], 'rating')"
            @mouseleave="hideTooltip"
          />
          <circle
            v-for="(point, index) in chartPoints"
            :key="`duration-${index}`"
            :cx="point.x"
            :cy="point.durationY"
            r="3"
            fill="var(--el-color-success)"
            stroke="white"
            stroke-width="2"
            class="data-point"
            @mouseenter="showTooltip($event, reviews[index], 'duration')"
            @mouseleave="hideTooltip"
          />
        </g>

        <!-- 趋势线 -->
        <path
          v-if="trendLine"
          :d="trendLine"
          fill="none"
          stroke="var(--el-color-warning)"
          stroke-width="1"
          stroke-dasharray="3,3"
          opacity="0.7"
        />

        <!-- 渐变定义 -->
        <defs>
          <linearGradient id="ratingGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:var(--el-color-primary);stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:var(--el-color-primary);stop-opacity:0.1" />
          </linearGradient>
        </defs>
      </svg>

      <!-- X轴标签 -->
      <div class="x-axis">
        <div
          v-for="(review, index) in reviews"
          :key="index"
          class="x-label"
          :style="{ left: `${(index / (reviews.length - 1)) * 85 + 10}%` }"
        >
          第{{ review.intervalId }}次
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="chart-legend">
      <div class="legend-item">
        <div class="legend-line rating"></div>
        <span>复习效果评分</span>
      </div>
      <div class="legend-item">
        <div class="legend-line duration"></div>
        <span>学习时长(分钟)</span>
      </div>
      <div class="legend-item">
        <div class="legend-line trend"></div>
        <span>趋势线</span>
      </div>
    </div>

    <!-- 分析结果 -->
    <div class="curve-analysis">
      <div class="analysis-item">
        <span class="analysis-label">学习趋势:</span>
        <span class="analysis-value" :class="getTrendClass(learningTrend)">
          {{ getTrendText(learningTrend) }}
        </span>
      </div>
      <div class="analysis-item">
        <span class="analysis-label">效果稳定性:</span>
        <span class="analysis-value" :class="getStabilityClass(ratingStability)">
          {{ getStabilityText(ratingStability) }}
        </span>
      </div>
      <div class="analysis-item">
        <span class="analysis-label">时长变化:</span>
        <span class="analysis-value" :class="getDurationTrendClass(durationTrend)">
          {{ getDurationTrendText(durationTrend) }}
        </span>
      </div>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.visible"
      class="curve-tooltip"
      :style="{ 
        left: `${tooltip.x}px`, 
        top: `${tooltip.y}px` 
      }"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">第{{ tooltip.data?.intervalId }}次复习</div>
        <div class="tooltip-info">
          <div v-if="tooltip.type === 'rating'" class="tooltip-item">
            <span>效果评分: {{ tooltip.data?.rating || 0 }}分</span>
          </div>
          <div v-else class="tooltip-item">
            <span>学习时长: {{ tooltip.data?.duration || 0 }}分钟</span>
          </div>
          <div class="tooltip-item">
            <span>时间: {{ formatDate(tooltip.data?.actualTime || tooltip.data?.scheduledTime || '') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive } from 'vue'
  import type { ReviewRecord } from '@/types'
  import dayjs from 'dayjs'

  interface Props {
    reviews: ReviewRecord[]
  }

  const props = defineProps<Props>()

  // 响应式数据
  const tooltip = reactive({
    visible: false,
    x: 0,
    y: 0,
    data: null as ReviewRecord | null,
    type: 'rating' as 'rating' | 'duration'
  })

  const svgWidth = 400
  const svgHeight = 160

  // 计算属性
  const maxRating = 5
  const maxDuration = computed(() => {
    const max = Math.max(...props.reviews.map(r => r.duration || 0))
    return Math.max(max, 30) // 最小显示30分钟
  })

  const yAxisTicks = computed(() => {
    return [0, 1, 2, 3, 4, 5]
  })

  const chartPoints = computed(() => {
    if (props.reviews.length === 0) {return []}
    
    return props.reviews.map((review, index) => {
      const x = 40 + (index / (props.reviews.length - 1)) * (svgWidth - 60)
      const ratingY = getYPosition((review.rating || 0))
      const durationY = getYPosition((review.duration || 0) / maxDuration.value * maxRating)
      
      return { x, ratingY, durationY }
    })
  })

  const ratingPath = computed(() => {
    if (chartPoints.value.length === 0) {return ''}
    
    let path = `M ${chartPoints.value[0].x} ${chartPoints.value[0].ratingY}`
    
    for (let i = 1; i < chartPoints.value.length; i++) {
      const point = chartPoints.value[i]
      path += ` L ${point.x} ${point.ratingY}`
    }
    
    return path
  })

  const ratingAreaPath = computed(() => {
    if (chartPoints.value.length === 0) {return ''}
    
    let path = `M ${chartPoints.value[0].x} ${svgHeight - 20}`
    path += ` L ${chartPoints.value[0].x} ${chartPoints.value[0].ratingY}`
    
    for (let i = 1; i < chartPoints.value.length; i++) {
      const point = chartPoints.value[i]
      path += ` L ${point.x} ${point.ratingY}`
    }
    
    path += ` L ${chartPoints.value[chartPoints.value.length - 1].x} ${svgHeight - 20}`
    path += ' Z'
    
    return path
  })

  const durationPath = computed(() => {
    if (chartPoints.value.length === 0) {return ''}
    
    let path = `M ${chartPoints.value[0].x} ${chartPoints.value[0].durationY}`
    
    for (let i = 1; i < chartPoints.value.length; i++) {
      const point = chartPoints.value[i]
      path += ` L ${point.x} ${point.durationY}`
    }
    
    return path
  })

  const trendLine = computed(() => {
    if (chartPoints.value.length < 2) {return ''}
    
    // 简单线性回归计算趋势线
    const n = chartPoints.value.length
    const sumX = chartPoints.value.reduce((sum, point) => sum + point.x, 0)
    const sumY = chartPoints.value.reduce((sum, point) => sum + point.ratingY, 0)
    const sumXY = chartPoints.value.reduce((sum, point) => sum + point.x * point.ratingY, 0)
    const sumXX = chartPoints.value.reduce((sum, point) => sum + point.x * point.x, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    const intercept = (sumY - slope * sumX) / n
    
    const startX = chartPoints.value[0].x
    const endX = chartPoints.value[chartPoints.value.length - 1].x
    const startY = slope * startX + intercept
    const endY = slope * endX + intercept
    
    return `M ${startX} ${startY} L ${endX} ${endY}`
  })

  const learningTrend = computed(() => {
    if (props.reviews.length < 2) {return 'stable'}
    
    const firstRating = props.reviews[0].rating || 0
    const lastRating = props.reviews[props.reviews.length - 1].rating || 0
    const diff = lastRating - firstRating
    
    if (diff > 0.5) {return 'improving'}
    if (diff < -0.5) {return 'declining'}
    return 'stable'
  })

  const ratingStability = computed(() => {
    if (props.reviews.length < 3) {return 'stable'}
    
    const ratings = props.reviews.map(r => r.rating || 0)
    const avg = ratings.reduce((sum, r) => sum + r, 0) / ratings.length
    const variance = ratings.reduce((sum, r) => sum + Math.pow(r - avg, 2), 0) / ratings.length
    
    if (variance < 0.5) {return 'stable'}
    if (variance < 1.5) {return 'moderate'}
    return 'unstable'
  })

  const durationTrend = computed(() => {
    if (props.reviews.length < 2) {return 'stable'}
    
    const firstDuration = props.reviews[0].duration || 0
    const lastDuration = props.reviews[props.reviews.length - 1].duration || 0
    const diff = lastDuration - firstDuration
    
    if (diff > 5) {return 'increasing'}
    if (diff < -5) {return 'decreasing'}
    return 'stable'
  })

  // 方法
  const getYPosition = (value: number): number => {
    return svgHeight - 20 - (value / maxRating) * (svgHeight - 40)
  }

  const formatDate = (dateStr: string): string => {
    return dayjs(dateStr).format('MM-DD HH:mm')
  }

  const getTrendClass = (trend: string): string => {
    switch (trend) {
      case 'improving': return 'trend-up'
      case 'declining': return 'trend-down'
      default: return 'trend-stable'
    }
  }

  const getTrendText = (trend: string): string => {
    switch (trend) {
      case 'improving': return '持续改善'
      case 'declining': return '有所下降'
      default: return '保持稳定'
    }
  }

  const getStabilityClass = (stability: string): string => {
    switch (stability) {
      case 'stable': return 'stability-good'
      case 'moderate': return 'stability-moderate'
      default: return 'stability-poor'
    }
  }

  const getStabilityText = (stability: string): string => {
    switch (stability) {
      case 'stable': return '稳定'
      case 'moderate': return '一般'
      default: return '波动较大'
    }
  }

  const getDurationTrendClass = (trend: string): string => {
    switch (trend) {
      case 'increasing': return 'duration-up'
      case 'decreasing': return 'duration-down'
      default: return 'duration-stable'
    }
  }

  const getDurationTrendText = (trend: string): string => {
    switch (trend) {
      case 'increasing': return '时长增加'
      case 'decreasing': return '时长减少'
      default: return '时长稳定'
    }
  }

  const showTooltip = (event: MouseEvent, data: ReviewRecord, type: 'rating' | 'duration') => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    tooltip.visible = true
    tooltip.x = event.clientX - rect.left + 10
    tooltip.y = event.clientY - rect.top - 10
    tooltip.data = data
    tooltip.type = type
  }

  const hideTooltip = () => {
    tooltip.visible = false
    tooltip.data = null
  }
</script>

<style scoped>
  .learning-curve-chart {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .empty-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .chart-container {
    position: relative;
    height: calc(100% - 80px);
  }

  .curve-svg {
    width: 100%;
    height: 160px;
  }

  .data-point {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .data-point:hover {
    r: 6;
  }

  .x-axis {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
  }

  .x-label {
    position: absolute;
    transform: translateX(-50%);
    font-size: 10px;
    color: var(--el-text-color-secondary);
    line-height: 30px;
  }

  .chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 12px 0;
    border-top: 1px solid var(--el-border-color-lighter);
    margin-top: 8px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .legend-line {
    width: 16px;
    height: 2px;
  }

  .legend-line.rating {
    background: var(--el-color-primary);
  }

  .legend-line.duration {
    background: var(--el-color-success);
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 3px,
      var(--el-color-success) 3px,
      var(--el-color-success) 6px
    );
  }

  .legend-line.trend {
    background: var(--el-color-warning);
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 2px,
      var(--el-color-warning) 2px,
      var(--el-color-warning) 4px
    );
  }

  .curve-analysis {
    display: flex;
    justify-content: space-around;
    padding: 12px 0;
    border-top: 1px solid var(--el-border-color-lighter);
    margin-top: 8px;
  }

  .analysis-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .analysis-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .analysis-value {
    font-size: 14px;
    font-weight: 600;
  }

  .trend-up,
  .duration-up {
    color: var(--el-color-success);
  }

  .trend-down,
  .duration-down {
    color: var(--el-color-danger);
  }

  .trend-stable,
  .duration-stable {
    color: var(--el-color-info);
  }

  .stability-good {
    color: var(--el-color-success);
  }

  .stability-moderate {
    color: var(--el-color-warning);
  }

  .stability-poor {
    color: var(--el-color-danger);
  }

  .curve-tooltip {
    position: absolute;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    pointer-events: none;
  }

  .tooltip-content {
    font-size: 12px;
  }

  .tooltip-title {
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .tooltip-item {
    color: var(--el-text-color-secondary);
    margin-bottom: 2px;
  }

  @media (max-width: 768px) {
    .chart-legend {
      flex-direction: column;
      gap: 8px;
    }

    .curve-analysis {
      flex-direction: column;
      gap: 8px;
    }

    .analysis-item {
      flex-direction: row;
      justify-content: space-between;
    }
  }
</style>
