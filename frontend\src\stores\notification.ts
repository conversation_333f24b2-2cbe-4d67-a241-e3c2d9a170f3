/**
 * 通知状态管理
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface NotificationItem {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
    type?: 'primary' | 'default'
  }>
}

export interface NotificationSettings {
  enabled: boolean
  browserNotifications: boolean
  inAppNotifications: boolean
  doNotDisturbEnabled: boolean
  doNotDisturbStart: string
  doNotDisturbEnd: string
  reviewReminders: boolean
  reviewReminderTime: number
  overdueReminders: boolean
  overdueReminderInterval: number
}

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<NotificationItem[]>([])
  const settings = ref<NotificationSettings>({
    enabled: true,
    browserNotifications: false,
    inAppNotifications: true,
    doNotDisturbEnabled: false,
    doNotDisturbStart: '22:00',
    doNotDisturbEnd: '08:00',
    reviewReminders: true,
    reviewReminderTime: 5,
    overdueReminders: true,
    overdueReminderInterval: 30
  })

  // 计算属性
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read)
  )

  const unreadCount = computed(() => unreadNotifications.value.length)

  const recentNotifications = computed(() => 
    notifications.value
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
  )

  const isInDoNotDisturbTime = computed(() => {
    if (!settings.value.doNotDisturbEnabled) {return false}
    
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    const start = settings.value.doNotDisturbStart
    const end = settings.value.doNotDisturbEnd
    
    // 处理跨天的情况
    if (start > end) {
      return currentTime >= start || currentTime <= end
    } else {
      return currentTime >= start && currentTime <= end
    }
  })

  // 方法
  const addNotification = (notification: Omit<NotificationItem, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: NotificationItem = {
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    }
    
    notifications.value.unshift(newNotification)
    
    // 限制通知数量，保留最近100条
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 100)
    }
    
    // 发送浏览器通知
    if (settings.value.browserNotifications && !isInDoNotDisturbTime.value) {
      sendBrowserNotification(newNotification)
    }
    
    return newNotification
  }

  const markAsRead = (id: string) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  const clearReadNotifications = () => {
    notifications.value = notifications.value.filter(n => !n.read)
  }

  // 发送浏览器通知
  const sendBrowserNotification = (notification: NotificationItem) => {
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知')
      return
    }

    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id
      })

      browserNotification.onclick = () => {
        window.focus()
        markAsRead(notification.id)
        browserNotification.close()
      }

      // 自动关闭
      setTimeout(() => {
        browserNotification.close()
      }, 5000)
    }
  }

  // 请求通知权限
  const requestNotificationPermission = async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知')
      return false
    }

    if (Notification.permission === 'granted') {
      return true
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }

    return false
  }

  // 测试通知
  const testNotification = async () => {
    const hasPermission = await requestNotificationPermission()
    
    if (hasPermission) {
      addNotification({
        type: 'info',
        title: '通知测试',
        message: '如果您看到这条消息，说明通知功能正常工作。'
      })
    } else {
      addNotification({
        type: 'warning',
        title: '通知权限',
        message: '请在浏览器设置中允许通知权限以接收提醒。'
      })
    }
  }

  // 发送复习提醒
  const sendReviewReminder = (taskTitle: string, reviewTime: string) => {
    if (!settings.value.reviewReminders || isInDoNotDisturbTime.value) {
      return
    }

    addNotification({
      type: 'info',
      title: '复习提醒',
      message: `任务"${taskTitle}"的复习时间到了`,
      actions: [
        {
          label: '开始复习',
          action: () => {
            // 这里应该跳转到复习页面
            window.location.href = '/review'
          },
          type: 'primary'
        },
        {
          label: '稍后提醒',
          action: () => {
            // 延迟提醒
            setTimeout(() => {
              sendReviewReminder(taskTitle, reviewTime)
            }, settings.value.reviewReminderTime * 60 * 1000)
          }
        }
      ]
    })
  }

  // 发送逾期提醒
  const sendOverdueReminder = (taskTitle: string, _overdueTime: string) => {
    if (!settings.value.overdueReminders || isInDoNotDisturbTime.value) {
      return
    }

    addNotification({
      type: 'warning',
      title: '逾期提醒',
      message: `任务"${taskTitle}"已逾期，请尽快复习`,
      persistent: true,
      actions: [
        {
          label: '立即复习',
          action: () => {
            window.location.href = '/review'
          },
          type: 'primary'
        }
      ]
    })
  }

  // 更新设置
  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
  }

  // 重置设置
  const resetSettings = () => {
    settings.value = {
      enabled: true,
      browserNotifications: false,
      inAppNotifications: true,
      doNotDisturbEnabled: false,
      doNotDisturbStart: '22:00',
      doNotDisturbEnd: '08:00',
      reviewReminders: true,
      reviewReminderTime: 5,
      overdueReminders: true,
      overdueReminderInterval: 30
    }
  }

  // 初始化
  const init = async () => {
    // 如果启用了浏览器通知，请求权限
    if (settings.value.browserNotifications) {
      await requestNotificationPermission()
    }
  }

  return {
    // 状态
    notifications,
    settings,
    
    // 计算属性
    unreadNotifications,
    unreadCount,
    recentNotifications,
    isInDoNotDisturbTime,
    
    // 方法
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearNotifications,
    clearReadNotifications,
    sendBrowserNotification,
    requestNotificationPermission,
    testNotification,
    sendReviewReminder,
    sendOverdueReminder,
    updateSettings,
    resetSettings,
    init
  }
}, {
  persist: {
    key: 'notification-store',
    storage: localStorage,
    paths: ['notifications', 'settings']
  }
})
