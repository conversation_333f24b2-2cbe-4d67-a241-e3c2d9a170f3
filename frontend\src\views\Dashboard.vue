<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学习概览</h2>
      <p class="page-description">欢迎回来！查看您的学习进度和今日任务</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ taskStats.total }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon today">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ taskStats.todayReviews }}</div>
              <div class="stat-label">今日复习</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ taskStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ taskStats.completionRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 今日任务 -->
      <el-col :xs="24" :lg="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>今日任务</span>
              <el-button type="primary" size="small" @click="goToTasks"> 查看全部 </el-button>
            </div>
          </template>

          <div v-if="todayTasks.length === 0" class="empty-state">
            <el-empty description="今天没有复习任务" />
          </div>

          <div v-else class="task-list">
            <div
              v-for="task in todayTasks.slice(0, 5)"
              :key="task.id"
              class="task-item"
              @click="goToTaskDetail(task.id)"
            >
              <div class="task-info">
                <div class="task-title">{{ task.title }}</div>
                <div class="task-meta">
                  <el-tag :type="getSubjectType(task.subject)" size="small">
                    {{ getSubjectLabel(task.subject) }}
                  </el-tag>
                  <span class="task-time">{{ formatTime(task.nextReviewTime) }}</span>
                </div>
              </div>
              <el-button type="primary" size="small" @click.stop="startReview(task)">
                开始复习
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 学习进度 -->
      <el-col :xs="24" :lg="12">
        <el-card class="content-card">
          <template #header>
            <span>学习进度</span>
          </template>

          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-label">
                <span>本周完成进度</span>
                <span class="progress-value">{{ taskStats.completionRate }}%</span>
              </div>
              <el-progress
                :percentage="taskStats.completionRate"
                :stroke-width="8"
                :show-text="false"
              />
            </div>

            <div class="progress-item">
              <div class="progress-label">
                <span>活跃任务</span>
                <span class="progress-value">{{ taskStats.active }}</span>
              </div>
              <el-progress
                :percentage="(taskStats.active / Math.max(taskStats.total, 1)) * 100"
                :stroke-width="8"
                :show-text="false"
                color="#E6A23C"
              />
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions">
            <el-button type="primary" @click="createTask">
              <el-icon><Plus /></el-icon>
              创建任务
            </el-button>
            <el-button @click="goToReview">
              <el-icon><Clock /></el-icon>
              复习计划
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useTaskStore } from '@/stores/task'
  import { subjectOptions } from '@/mock/taskData'
  import type { Task } from '@/types'
  import dayjs from 'dayjs'
  import { Check, Clock, Document, Plus, TrendCharts } from '@element-plus/icons-vue'

  const router = useRouter()
  const taskStore = useTaskStore()

  const taskStats = computed(() => taskStore.taskStats)
  const todayTasks = computed(() => taskStore.todayTasks)

  const getSubjectLabel = (subject: string) => {
    return subjectOptions.find((s) => s.value === subject)?.label || subject
  }

  const getSubjectType = (subject: string) => {
    const types: Record<string, string> = {
      chinese: 'danger',
      math: 'success',
      english: 'primary',
      physics: 'info',
      chemistry: 'warning'
    }
    return types[subject] || 'info'
  }

  const formatTime = (time: string) => {
    return dayjs(time).format('HH:mm')
  }

  const goToTasks = () => {
    router.push('/tasks')
  }

  const goToTaskDetail = (id: string) => {
    router.push(`/tasks/${id}`)
  }

  const goToReview = () => {
    router.push('/review')
  }

  const createTask = () => {
    router.push('/tasks/create')
  }

  const startReview = (task: Task) => {
    // 开始复习任务
    // 这里可以打开复习界面或跳转到复习页面
  }

  onMounted(() => {
    taskStore.loadTasks()
  })
</script>

<style scoped>
  .dashboard {
    max-width: 1200px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .page-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .page-description {
    margin: 0;
    color: var(--el-text-color-secondary);
  }

  .stats-row {
    margin-bottom: 24px;
  }

  .stat-card {
    height: 100px;
  }

  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 24px;
    color: white;
  }

  .stat-icon.total {
    background: var(--el-color-primary);
  }
  .stat-icon.today {
    background: var(--el-color-warning);
  }
  .stat-icon.completed {
    background: var(--el-color-success);
  }
  .stat-icon.rate {
    background: var(--el-color-info);
  }

  .stat-info {
    flex: 1;
  }

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    line-height: 1;
  }

  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }

  .content-row {
    margin-bottom: 24px;
  }

  .content-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .task-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .task-item:hover {
    background-color: var(--el-bg-color-page);
  }

  .task-item:last-child {
    border-bottom: none;
  }

  .task-info {
    flex: 1;
  }

  .task-title {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .task-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .task-time {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .progress-section {
    margin-bottom: 24px;
  }

  .progress-item {
    margin-bottom: 16px;
  }

  .progress-item:last-child {
    margin-bottom: 0;
  }

  .progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .progress-value {
    font-weight: 600;
    color: var(--el-color-primary);
  }

  .quick-actions {
    display: flex;
    gap: 12px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  @media (max-width: 768px) {
    .quick-actions {
      flex-direction: column;
    }

    .task-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
</style>
