<template>
  <div class="tasks-page">
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <div class="header-left">
        <h2>任务管理</h2>
        <p class="page-description">管理您的学习任务和复习计划</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createTask">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="6">
          <el-select
            v-model="filters.subject"
            placeholder="选择学科"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              v-for="subject in subjectOptions"
              :key="subject.value"
              :label="subject.label"
              :value="subject.value"
            />
          </el-select>
        </el-col>

        <el-col :xs="24" :sm="6">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="活跃" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="暂停" value="paused" />
          </el-select>
        </el-col>

        <el-col :xs="24" :sm="6">
          <el-select
            v-model="filters.priority"
            placeholder="最低优先级"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="1星" :value="1" />
            <el-option label="2星" :value="2" />
            <el-option label="3星" :value="3" />
            <el-option label="4星" :value="4" />
            <el-option label="5星" :value="5" />
          </el-select>
        </el-col>

        <el-col :xs="24" :sm="6">
          <el-input
            v-model="filters.searchText"
            placeholder="搜索任务..."
            clearable
            @input="handleFilterChange"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
      </el-row>

      <div class="filter-actions">
        <el-button @click="clearFilters">清除筛选</el-button>
        <span class="result-count">共 {{ filteredTasks.length }} 个任务</span>
      </div>
    </el-card>

    <!-- 视图切换 -->
    <div class="view-controls">
      <div class="view-toggle">
        <el-radio-group v-model="viewMode" @change="handleViewChange">
          <el-radio-button value="grid">网格视图</el-radio-button>
          <el-radio-button value="list">列表视图</el-radio-button>
          <el-radio-button value="calendar">日历视图</el-radio-button>
        </el-radio-group>
      </div>

      <div v-if="viewMode === 'calendar'" class="calendar-controls">
        <el-button :icon="Calendar" @click="showLoadIndicator = !showLoadIndicator">
          {{ showLoadIndicator ? '隐藏' : '显示' }}负载指示器
        </el-button>
        <el-button :icon="DataAnalysis" @click="showLoadDashboard = !showLoadDashboard">
          {{ showLoadDashboard ? '隐藏' : '显示' }}负载分析
        </el-button>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-container">
      <el-loading :loading="loading" element-loading-text="加载中...">
        <div v-if="filteredTasks.length === 0" class="empty-state">
          <el-empty description="暂无任务数据">
            <el-button type="primary" @click="createTask">创建第一个任务</el-button>
          </el-empty>
        </div>

        <!-- 网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="task-grid">
          <!-- 使用虚拟滚动优化大量任务的渲染性能 -->
          <VirtualList
            v-if="filteredTasks.length > 50"
            :items="filteredTasks"
            :item-height="200"
            :container-height="600"
            :buffer="5"
            key-field="id"
          >
            <template #default="{ item: task }">
              <TaskCard
                :task="task"
                @start-review="handleStartReview"
                @view-detail="handleViewDetail"
                @edit="handleEdit"
                @delete="handleDelete"
              />
            </template>
          </VirtualList>

          <!-- 少量任务时直接渲染 -->
          <template v-else>
            <TaskCard
              v-for="task in filteredTasks"
              :key="task.id"
              :task="task"
              @start-review="handleStartReview"
              @view-detail="handleViewDetail"
              @edit="handleEdit"
              @delete="handleDelete"
            />
          </template>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="task-list">
          <el-table :data="filteredTasks" style="width: 100%">
            <el-table-column prop="title" label="任务名称" />
            <el-table-column prop="subject" label="学科" width="80">
              <template #default="{ row }">
                <el-tag size="small">{{ getSubjectName(row.subject) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="estimatedTime" label="预估时间" width="100">
              <template #default="{ row }">
                {{ row.estimatedTime }}分钟
              </template>
            </el-table-column>
            <el-table-column prop="difficulty" label="难度" width="80">
              <template #default="{ row }">
                <el-rate v-model="row.difficulty" disabled size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="handleStartReview(row)">
                  开始复习
                </el-button>
                <el-button size="small" @click="handleEdit(row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 日历视图 -->
        <div v-else-if="viewMode === 'calendar'" class="calendar-container">
          <!-- 负载分析仪表板 -->
          <div v-if="showLoadDashboard" class="dashboard-section">
            <LoadDashboard :tasks="filteredTasks" />
          </div>

          <div class="calendar-layout">
            <div class="calendar-main">
              <CalendarView
                :tasks="filteredTasks"
                @task-click="handleTaskClick"
                @date-click="handleDateClick"
              />
            </div>

            <div v-if="showLoadIndicator" class="calendar-sidebar">
              <LoadIndicator
                :tasks="selectedDateTasks"
                :selected-date="selectedDate"
              />
            </div>
          </div>
        </div>
      </el-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { useTaskStore } from '@/stores/task'
  import { subjectOptions } from '@/mock/taskData'
  import TaskCard from '@/components/task/TaskCard.vue'
  import CalendarView from '@/components/task/CalendarView.vue'
  import LoadIndicator from '@/components/task/LoadIndicator.vue'
  import LoadDashboard from '@/components/task/LoadDashboard.vue'
  import VirtualList from '@/components/common/VirtualList.vue'
  import type { Task, TaskFilter } from '@/types'
  import { Calendar, DataAnalysis, Plus, Search } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'

  const router = useRouter()
  const taskStore = useTaskStore()

  // 响应式数据
  const viewMode = ref<'grid' | 'list' | 'calendar'>('grid')
  const showLoadIndicator = ref(true)
  const showLoadDashboard = ref(false)
  const selectedDate = ref<string>('')
  const selectedDateTasks = ref<Task[]>([])

  const filters = reactive<TaskFilter>({
    subject: '',
    priority: 0,
    status: '',
    searchText: ''
  })

  const loading = computed(() => taskStore.loading)
  const filteredTasks = computed(() => taskStore.filteredTasks)

  const handleFilterChange = () => {
    taskStore.setFilters(filters)
  }

  const clearFilters = () => {
    Object.assign(filters, {
      subject: '',
      priority: 0,
      status: '',
      searchText: ''
    })
    taskStore.clearFilters()
  }

  const createTask = () => {
    router.push('/tasks/create')
  }

  const handleStartReview = (task: Task) => {
    ElMessage.success(`开始复习：${task.title}`)
    // 这里可以打开复习界面
  }

  const handleViewDetail = (task: Task) => {
    router.push(`/tasks/${task.id}`)
  }

  const handleEdit = (task: Task) => {
    ElMessage.info(`编辑任务：${task.title}`)
    // 这里可以打开编辑界面
  }

  const handleDelete = async (task: Task) => {
    try {
      await ElMessageBox.confirm(`确定要删除任务"${task.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await taskStore.deleteTask(task.id)
      ElMessage.success('任务删除成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除任务失败')
      }
    }
  }

  // 新增方法
  const handleViewChange = (mode: 'grid' | 'list' | 'calendar') => {
    viewMode.value = mode
    if (mode === 'calendar') {
      // 日历视图时，默认显示今天的任务
      const today = new Date().toISOString().split('T')[0]
      selectedDate.value = today
      updateSelectedDateTasks(today)
    }
  }

  const handleTaskClick = (task: Task) => {
    // 处理日历中的任务点击
    handleStartReview(task)
  }

  const handleDateClick = (date: any) => {
    selectedDate.value = date.dateStr
    updateSelectedDateTasks(date.dateStr)
  }

  const updateSelectedDateTasks = (dateStr: string) => {
    selectedDateTasks.value = filteredTasks.value.filter(task => {
      const taskDate = new Date(task.nextReviewTime).toISOString().split('T')[0]
      return taskDate === dateStr
    })
  }

  const getSubjectName = (subject: string): string => {
    const nameMap: Record<string, string> = {
      chinese: '语文',
      math: '数学',
      english: '英语',
      physics: '物理',
      chemistry: '化学',
      biology: '生物',
      history: '历史',
      geography: '地理'
    }
    return nameMap[subject] || subject
  }

  const getPriorityType = (priority: number): string => {
    if (priority >= 4) {return 'danger'}
    if (priority >= 3) {return 'warning'}
    return 'success'
  }

  const getPriorityText = (priority: number): string => {
    if (priority >= 4) {return '高'}
    if (priority >= 3) {return '中'}
    return '低'
  }

  onMounted(() => {
    taskStore.loadTasks()
  })
</script>

<style scoped>
  .tasks-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
  }

  .header-left h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .page-description {
    margin: 0;
    color: var(--el-text-color-secondary);
  }

  .filter-card {
    margin-bottom: 24px;
  }

  .filter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .result-count {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .task-list-container {
    min-height: 400px;
  }

  .view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 16px;
    background: var(--el-bg-color);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-light);
  }

  .view-toggle {
    display: flex;
    gap: 12px;
  }

  .calendar-controls {
    display: flex;
    gap: 12px;
  }

  .task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }

  .task-list {
    margin-top: 20px;
  }

  .calendar-container {
    margin-top: 20px;
  }

  .dashboard-section {
    margin-bottom: 24px;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 12px;
    border: 1px solid var(--el-border-color-light);
  }

  .calendar-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
  }

  .calendar-main {
    min-height: 600px;
  }

  .calendar-sidebar {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
    height: fit-content;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .view-controls {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .calendar-layout {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .calendar-sidebar {
      order: -1;
    }

    .task-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .filter-actions {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    /* 移动端专用优化 */
    .tasks-page {
      padding: 12px;
    }

    .page-header h2 {
      font-size: 20px;
    }

    .header-actions .el-button {
      min-height: 44px;
    }

    .filters-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .view-toggle {
      justify-content: center;
    }

    .calendar-controls {
      justify-content: center;
      flex-wrap: wrap;
    }

    .dashboard-section {
      padding: 16px;
    }

    .task-list .el-table {
      font-size: 12px;
    }

    .task-list .el-table th,
    .task-list .el-table td {
      padding: 8px 4px;
    }

    .empty-state {
      padding: 40px 20px;
    }

    /* 移动端触摸优化 */
    .el-button {
      min-height: 44px;
      touch-action: manipulation;
    }

    .el-card {
      touch-action: manipulation;
    }

    /* 移动端表格滚动优化 */
    .el-table__body-wrapper {
      -webkit-overflow-scrolling: touch;
    }
  }
</style>
