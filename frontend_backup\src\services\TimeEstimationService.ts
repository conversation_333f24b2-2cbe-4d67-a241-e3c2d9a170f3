/**
 * 智能时间预估服务
 * 基于历史数据的机器学习时间预估算法
 */

export interface TaskFeatures {
  subject: string           // 学科
  difficulty: number        // 难度等级（1-5）
  estimatedTime: number     // 初始预估时间（分钟）
  tags: string[]           // 标签
  description: string       // 描述
  contentLength: number     // 内容长度
  hasImages: boolean        // 是否包含图片
  hasFormulas: boolean      // 是否包含公式
  taskType: 'study' | 'review' | 'practice' | 'exam'  // 任务类型
}

export interface LearningSession {
  taskId: string
  features: TaskFeatures
  actualTime: number        // 实际用时（分钟）
  completionRate: number    // 完成度（0-1）
  quality: number          // 学习质量（1-5）
  timestamp: string        // 学习时间
  timeOfDay: number        // 一天中的小时（0-23）
  dayOfWeek: number        // 一周中的天（0-6）
  userState: {             // 用户状态
    energy: number         // 精力水平（1-5）
    focus: number          // 专注度（1-5）
    mood: number           // 心情（1-5）
  }
}

export interface UserProfile {
  userId: string
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed'
  preferredTimeSlots: number[]  // 偏好的学习时间段
  averageSpeed: number          // 平均学习速度系数
  subjectProficiency: Record<string, number>  // 各学科熟练度
  totalLearningTime: number     // 总学习时间
  sessionCount: number          // 学习次数
  accuracyHistory: number[]     // 预估准确度历史
}

export interface TimeEstimationResult {
  estimatedTime: number     // 预估时间（分钟）
  confidence: number        // 置信度（0-1）
  factors: {               // 影响因素
    baseTime: number       // 基础时间
    difficultyFactor: number
    subjectFactor: number
    userFactor: number
    timeFactor: number
    contextFactor: number
  }
  suggestions: string[]     // 优化建议
  alternativeEstimates: {   // 不同情况下的预估
    optimistic: number      // 乐观预估
    realistic: number       // 现实预估
    pessimistic: number     // 悲观预估
  }
}

export class TimeEstimationService {
  private static instance: TimeEstimationService
  private sessions: LearningSession[] = []
  private userProfile: UserProfile
  
  // 学科基础时间系数
  private readonly subjectFactors: Record<string, number> = {
    '数学': 1.2,
    '物理': 1.3,
    '化学': 1.1,
    '英语': 0.9,
    '语文': 0.8,
    '历史': 0.7,
    '地理': 0.8,
    '生物': 1.0
  }
  
  // 难度系数
  private readonly difficultyFactors = [0, 0.6, 0.8, 1.0, 1.3, 1.6]
  
  // 任务类型系数
  private readonly taskTypeFactors = {
    'study': 1.0,      // 学习
    'review': 0.7,     // 复习
    'practice': 1.2,   // 练习
    'exam': 1.5        // 考试
  }

  private constructor() {
    this.userProfile = this.loadUserProfile()
    this.sessions = this.loadLearningHistory()
  }

  public static getInstance(): TimeEstimationService {
    if (!TimeEstimationService.instance) {
      TimeEstimationService.instance = new TimeEstimationService()
    }
    return TimeEstimationService.instance
  }

  /**
   * 预估任务时间
   * @param features 任务特征
   * @returns 时间预估结果
   */
  public estimateTaskTime(features: TaskFeatures): TimeEstimationResult {
    // 1. 基础时间计算
    const baseTime = this.calculateBaseTime(features)
    
    // 2. 各种因素调整
    const difficultyFactor = this.difficultyFactors[features.difficulty] || 1.0
    const subjectFactor = this.subjectFactors[features.subject] || 1.0
    const userFactor = this.calculateUserFactor(features)
    const timeFactor = this.calculateTimeFactor()
    const contextFactor = this.calculateContextFactor(features)
    
    // 3. 综合计算
    const estimatedTime = baseTime * difficultyFactor * subjectFactor * 
                         userFactor * timeFactor * contextFactor
    
    // 4. 置信度计算
    const confidence = this.calculateConfidence(features)
    
    // 5. 生成建议
    const suggestions = this.generateSuggestions(features, estimatedTime)
    
    // 6. 不同情况预估
    const alternativeEstimates = {
      optimistic: Math.round(estimatedTime * 0.8),
      realistic: Math.round(estimatedTime),
      pessimistic: Math.round(estimatedTime * 1.3)
    }

    return {
      estimatedTime: Math.round(estimatedTime),
      confidence,
      factors: {
        baseTime,
        difficultyFactor,
        subjectFactor,
        userFactor,
        timeFactor,
        contextFactor
      },
      suggestions,
      alternativeEstimates
    }
  }

  /**
   * 更新学习记录
   * @param session 学习会话数据
   */
  public updateLearningHistory(session: LearningSession): void {
    this.sessions.push(session)
    
    // 更新用户画像
    this.updateUserProfile(session)
    
    // 计算预估准确度
    const accuracy = this.calculateAccuracy(session)
    this.userProfile.accuracyHistory.push(accuracy)
    
    // 保持最近100条记录
    if (this.sessions.length > 100) {
      this.sessions = this.sessions.slice(-100)
    }
    
    // 保存数据
    this.saveLearningHistory()
    this.saveUserProfile()
  }

  /**
   * 获取用户效率因子
   * @param subject 学科
   * @returns 效率因子
   */
  public getUserEfficiencyFactor(subject?: string): number {
    if (!subject) {
      return this.userProfile.averageSpeed
    }
    
    const subjectSessions = this.sessions.filter(s => s.features.subject === subject)
    if (subjectSessions.length === 0) {
      return this.userProfile.averageSpeed
    }
    
    const avgEfficiency = subjectSessions.reduce((sum, session) => {
      const efficiency = session.features.estimatedTime / session.actualTime
      return sum + efficiency
    }, 0) / subjectSessions.length
    
    return avgEfficiency
  }

  /**
   * 获取预估准确度统计
   * @returns 准确度统计
   */
  public getAccuracyStats(): {
    overall: number
    recent: number
    bySubject: Record<string, number>
    trend: 'improving' | 'stable' | 'declining'
  } {
    const accuracyHistory = this.userProfile.accuracyHistory
    
    if (accuracyHistory.length === 0) {
      return {
        overall: 0,
        recent: 0,
        bySubject: {},
        trend: 'stable'
      }
    }
    
    const overall = accuracyHistory.reduce((sum, acc) => sum + acc, 0) / accuracyHistory.length
    const recent = accuracyHistory.slice(-10).reduce((sum, acc) => sum + acc, 0) / Math.min(10, accuracyHistory.length)
    
    // 按学科统计
    const bySubject: Record<string, number> = {}
    const subjectSessions = this.groupSessionsBySubject()
    
    for (const [subject, sessions] of Object.entries(subjectSessions)) {
      const accuracies = sessions.map(s => this.calculateAccuracy(s))
      bySubject[subject] = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length
    }
    
    // 趋势分析
    let trend: 'improving' | 'stable' | 'declining' = 'stable'
    if (accuracyHistory.length >= 10) {
      const firstHalf = accuracyHistory.slice(0, Math.floor(accuracyHistory.length / 2))
      const secondHalf = accuracyHistory.slice(Math.floor(accuracyHistory.length / 2))
      
      const firstAvg = firstHalf.reduce((sum, acc) => sum + acc, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((sum, acc) => sum + acc, 0) / secondHalf.length
      
      if (secondAvg > firstAvg + 0.05) {
        trend = 'improving'
      } else if (secondAvg < firstAvg - 0.05) {
        trend = 'declining'
      }
    }
    
    return { overall, recent, bySubject, trend }
  }

  /**
   * 计算基础时间
   */
  private calculateBaseTime(features: TaskFeatures): number {
    let baseTime = features.estimatedTime
    
    // 内容长度影响
    const lengthFactor = Math.max(0.5, Math.min(2.0, features.contentLength / 1000))
    baseTime *= lengthFactor
    
    // 任务类型影响
    baseTime *= this.taskTypeFactors[features.taskType]
    
    // 内容复杂度影响
    if (features.hasFormulas) {baseTime *= 1.2}
    if (features.hasImages) {baseTime *= 1.1}
    
    return baseTime
  }

  /**
   * 计算用户因子
   */
  private calculateUserFactor(features: TaskFeatures): number {
    const subjectProficiency = this.userProfile.subjectProficiency[features.subject] || 0.5
    const averageSpeed = this.userProfile.averageSpeed
    
    // 熟练度越高，用时越少
    const proficiencyFactor = 2 - subjectProficiency
    
    return proficiencyFactor * (2 - averageSpeed)
  }

  /**
   * 计算时间因子
   */
  private calculateTimeFactor(): number {
    const now = new Date()
    const hour = now.getHours()
    
    // 根据时间段调整（用户的黄金学习时间）
    const preferredHours = this.userProfile.preferredTimeSlots
    
    if (preferredHours.includes(hour)) {
      return 0.9  // 黄金时间，效率更高
    } else if (hour < 6 || hour > 22) {
      return 1.3  // 深夜或凌晨，效率较低
    } else {
      return 1.0  // 正常时间
    }
  }

  /**
   * 计算上下文因子
   */
  private calculateContextFactor(features: TaskFeatures): number {
    // 基于最近的学习会话调整
    const recentSessions = this.sessions.slice(-5)
    
    if (recentSessions.length === 0) {return 1.0}
    
    const avgQuality = recentSessions.reduce((sum, s) => sum + s.quality, 0) / recentSessions.length
    const avgEnergy = recentSessions.reduce((sum, s) => sum + s.userState.energy, 0) / recentSessions.length
    
    // 质量和精力状态影响效率
    const qualityFactor = avgQuality / 3  // 3是中等质量
    const energyFactor = avgEnergy / 3    // 3是中等精力
    
    return Math.max(0.7, Math.min(1.3, (qualityFactor + energyFactor) / 2))
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(features: TaskFeatures): number {
    const similarSessions = this.sessions.filter(s =>
      s.features.subject === features.subject &&
      Math.abs(s.features.difficulty - features.difficulty) <= 1 &&
      s.features.taskType === features.taskType
    )
    
    // 基于相似任务的数量和准确度历史
    const dataPoints = similarSessions.length
    const recentAccuracy = this.userProfile.accuracyHistory.slice(-10).reduce((sum, acc) => sum + acc, 0) / Math.min(10, this.userProfile.accuracyHistory.length)
    
    let confidence = Math.min(0.9, dataPoints * 0.1 + 0.3)
    confidence *= (recentAccuracy || 0.5)
    
    return Math.max(0.1, confidence)
  }

  /**
   * 生成优化建议
   */
  private generateSuggestions(features: TaskFeatures, estimatedTime: number): string[] {
    const suggestions: string[] = []
    
    if (estimatedTime > 60) {
      suggestions.push('建议将任务分解为多个小任务，每次学习30-45分钟')
    }
    
    if (features.difficulty >= 4) {
      suggestions.push('难度较高，建议预留额外时间并准备相关资料')
    }
    
    const hour = new Date().getHours()
    if (!this.userProfile.preferredTimeSlots.includes(hour)) {
      suggestions.push('当前不是您的最佳学习时间，可能需要更多时间')
    }
    
    const subjectProficiency = this.userProfile.subjectProficiency[features.subject] || 0.5
    if (subjectProficiency < 0.3) {
      suggestions.push(`您在${features.subject}方面还需要更多练习，建议适当延长学习时间`)
    }
    
    return suggestions
  }

  /**
   * 计算预估准确度
   */
  private calculateAccuracy(session: LearningSession): number {
    const estimated = session.features.estimatedTime
    const actual = session.actualTime
    
    // 计算相对误差
    const relativeError = Math.abs(estimated - actual) / actual
    
    // 转换为准确度（0-1）
    return Math.max(0, 1 - relativeError)
  }

  /**
   * 更新用户画像
   */
  private updateUserProfile(session: LearningSession): void {
    this.userProfile.totalLearningTime += session.actualTime
    this.userProfile.sessionCount += 1
    
    // 更新平均速度
    const efficiency = session.features.estimatedTime / session.actualTime
    this.userProfile.averageSpeed = (this.userProfile.averageSpeed * (this.userProfile.sessionCount - 1) + efficiency) / this.userProfile.sessionCount
    
    // 更新学科熟练度
    const subject = session.features.subject
    const currentProficiency = this.userProfile.subjectProficiency[subject] || 0.5
    const qualityFactor = session.quality / 5
    const newProficiency = currentProficiency * 0.9 + qualityFactor * 0.1
    this.userProfile.subjectProficiency[subject] = Math.min(1, newProficiency)
  }

  /**
   * 按学科分组会话
   */
  private groupSessionsBySubject(): Record<string, LearningSession[]> {
    const groups: Record<string, LearningSession[]> = {}
    
    for (const session of this.sessions) {
      const subject = session.features.subject
      if (!groups[subject]) {
        groups[subject] = []
      }
      groups[subject].push(session)
    }
    
    return groups
  }

  /**
   * 加载用户画像
   */
  private loadUserProfile(): UserProfile {
    const defaultProfile: UserProfile = {
      userId: 'default',
      learningStyle: 'mixed',
      preferredTimeSlots: [9, 10, 14, 15, 19, 20],
      averageSpeed: 1.0,
      subjectProficiency: {},
      totalLearningTime: 0,
      sessionCount: 0,
      accuracyHistory: []
    }

    try {
      const saved = localStorage.getItem('user-profile')
      return saved ? { ...defaultProfile, ...JSON.parse(saved) } : defaultProfile
    } catch {
      return defaultProfile
    }
  }

  /**
   * 保存用户画像
   */
  private saveUserProfile(): void {
    localStorage.setItem('user-profile', JSON.stringify(this.userProfile))
  }

  /**
   * 加载学习历史
   */
  private loadLearningHistory(): LearningSession[] {
    try {
      const saved = localStorage.getItem('learning-history')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  }

  /**
   * 保存学习历史
   */
  private saveLearningHistory(): void {
    localStorage.setItem('learning-history', JSON.stringify(this.sessions))
  }
}

export default TimeEstimationService
