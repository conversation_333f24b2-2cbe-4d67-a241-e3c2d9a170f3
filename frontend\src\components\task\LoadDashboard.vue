<template>
  <div class="load-dashboard">
    <div class="dashboard-header">
      <h3>学习负载分析</h3>
      <div class="header-controls">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          @change="handleDateChange"
        />
        <el-button :icon="Refresh" :loading="loading" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <div class="dashboard-grid">
      <!-- 负载概览卡片 -->
      <div class="overview-cards">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon load-light">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-info">
              <h4>今日负载</h4>
              <p class="load-value" :class="currentLoadClass">{{ currentLoadText }}</p>
              <span class="load-time">{{ totalTime }}分钟</span>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="card-info">
              <h4>任务数量</h4>
              <p class="task-count">{{ taskCount }}个</p>
              <span class="task-breakdown">{{ completedCount }}已完成 / {{ pendingCount }}待完成</span>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="card-info">
              <h4>平均时长</h4>
              <p class="avg-time">{{ averageTime }}分钟</p>
              <span class="time-range">{{ minTime }}-{{ maxTime }}分钟</span>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="card-info">
              <h4>负载预警</h4>
              <p class="warning-level" :class="warningClass">{{ warningText }}</p>
              <span class="warning-desc">{{ warningDescription }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 负载图表 -->
      <div class="chart-section">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>每日负载分布</span>
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button value="bar">柱状图</el-radio-button>
                <el-radio-button value="line">折线图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <LoadChart
              :data="chartData"
              :type="chartType"
              :height="300"
            />
          </div>
        </el-card>
      </div>

      <!-- 负载热力图 -->
      <div class="heatmap-section">
        <el-card class="heatmap-card">
          <template #header>
            <span>月度负载热力图</span>
          </template>
          <div class="heatmap-container">
            <LoadHeatmap
              :data="heatmapData"
              :month="selectedMonth"
            />
          </div>
        </el-card>
      </div>

      <!-- 负载趋势 -->
      <div class="trend-section">
        <el-card class="trend-card">
          <template #header>
            <span>未来负载趋势预测</span>
          </template>
          <div class="trend-container">
            <LoadTrend
              :data="trendData"
              :predictions="predictions"
            />
          </div>
        </el-card>
      </div>

      <!-- 负载建议 -->
      <div class="suggestions-section">
        <el-card class="suggestions-card">
          <template #header>
            <span>负载优化建议</span>
          </template>
          <div class="suggestions-content">
            <div
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="suggestion-item"
              :class="suggestion.type"
            >
              <el-icon class="suggestion-icon">
                <InfoFilled v-if="suggestion.type === 'info'" />
                <Warning v-else-if="suggestion.type === 'warning'" />
                <SuccessFilled v-else />
              </el-icon>
              <div class="suggestion-content">
                <h5>{{ suggestion.title }}</h5>
                <p>{{ suggestion.description }}</p>
                <div v-if="suggestion.actions" class="suggestion-actions">
                  <el-button
                    v-for="action in suggestion.actions"
                    :key="action.text"
                    size="small"
                    :type="action.type"
                    @click="handleSuggestionAction(action)"
                  >
                    {{ action.text }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue'
  import { 
    DataAnalysis, 
    InfoFilled, 
    Refresh, 
    SuccessFilled, 
    Timer,
    TrendCharts,
    Warning
  } from '@element-plus/icons-vue'
  import LoadChart from './LoadChart.vue'
  import LoadHeatmap from './LoadHeatmap.vue'
  import LoadTrend from './LoadTrend.vue'
  import type { Task } from '@/types'
  import dayjs from 'dayjs'

  interface Props {
    tasks: Task[]
  }

  interface LoadSuggestion {
    type: 'info' | 'warning' | 'success'
    title: string
    description: string
    actions?: Array<{
      text: string
      type: string
      action: string
    }>
  }

  const props = defineProps<Props>()

  // 响应式数据
  const selectedDate = ref(new Date())
  const chartType = ref<'bar' | 'line'>('bar')
  const loading = ref(false)

  // 计算属性
  const selectedMonth = computed(() => {
    return dayjs(selectedDate.value).format('YYYY-MM')
  })

  const todayTasks = computed(() => {
    const today = dayjs(selectedDate.value).format('YYYY-MM-DD')
    return props.tasks.filter(task => {
      const taskDate = dayjs(task.nextReviewTime).format('YYYY-MM-DD')
      return taskDate === today
    })
  })

  const totalTime = computed(() => {
    return todayTasks.value.reduce((sum, task) => sum + task.estimatedTime, 0)
  })

  const taskCount = computed(() => {
    return todayTasks.value.length
  })

  const completedCount = computed(() => {
    return todayTasks.value.filter(task => task.status === 'completed').length
  })

  const pendingCount = computed(() => {
    return taskCount.value - completedCount.value
  })

  const averageTime = computed(() => {
    if (taskCount.value === 0) {return 0}
    return Math.round(totalTime.value / taskCount.value)
  })

  const minTime = computed(() => {
    if (taskCount.value === 0) {return 0}
    return Math.min(...todayTasks.value.map(task => task.estimatedTime))
  })

  const maxTime = computed(() => {
    if (taskCount.value === 0) {return 0}
    return Math.max(...todayTasks.value.map(task => task.estimatedTime))
  })

  const currentLoadClass = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return 'load-light'}
    if (time <= 120) {return 'load-medium'}
    return 'load-heavy'
  })

  const currentLoadText = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return '轻度负载'}
    if (time <= 120) {return '中度负载'}
    return '重度负载'
  })

  const warningClass = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return 'warning-safe'}
    if (time <= 120) {return 'warning-caution'}
    return 'warning-danger'
  })

  const warningText = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return '安全'}
    if (time <= 120) {return '注意'}
    return '警告'
  })

  const warningDescription = computed(() => {
    const time = totalTime.value
    if (time <= 60) {return '负载适中，保持良好节奏'}
    if (time <= 120) {return '负载较重，注意休息'}
    return '负载过重，建议调整'
  })

  const chartData = computed(() => {
    // 生成最近7天的负载数据
    const data = []
    for (let i = 6; i >= 0; i--) {
      const date = dayjs().subtract(i, 'day')
      const dayTasks = props.tasks.filter(task => {
        const taskDate = dayjs(task.nextReviewTime).format('YYYY-MM-DD')
        return taskDate === date.format('YYYY-MM-DD')
      })
      
      data.push({
        date: date.format('MM-DD'),
        value: dayTasks.reduce((sum, task) => sum + task.estimatedTime, 0),
        count: dayTasks.length
      })
    }
    return data
  })

  const heatmapData = computed(() => {
    // 生成当月的热力图数据
    const month = dayjs(selectedDate.value)
    const daysInMonth = month.daysInMonth()
    const data = []

    for (let day = 1; day <= daysInMonth; day++) {
      const date = month.date(day)
      const dayTasks = props.tasks.filter(task => {
        const taskDate = dayjs(task.nextReviewTime).format('YYYY-MM-DD')
        return taskDate === date.format('YYYY-MM-DD')
      })

      data.push({
        date: date.format('YYYY-MM-DD'),
        value: dayTasks.reduce((sum, task) => sum + task.estimatedTime, 0),
        level: calculateLoadLevel(dayTasks.reduce((sum, task) => sum + task.estimatedTime, 0))
      })
    }
    return data
  })

  const trendData = computed(() => {
    // 生成未来7天的趋势数据
    const data = []
    for (let i = 0; i < 7; i++) {
      const date = dayjs().add(i, 'day')
      const dayTasks = props.tasks.filter(task => {
        const taskDate = dayjs(task.nextReviewTime).format('YYYY-MM-DD')
        return taskDate === date.format('YYYY-MM-DD')
      })
      
      data.push({
        date: date.format('MM-DD'),
        value: dayTasks.reduce((sum, task) => sum + task.estimatedTime, 0),
        predicted: i > 0 // 今天之后的都是预测数据
      })
    }
    return data
  })

  const predictions = computed((): { averageLoad: number; trend: 'increasing' | 'decreasing' | 'stable'; confidence: number } => {
    // 基于历史数据生成预测
    const avgLoad = chartData.value.reduce((sum, item) => sum + item.value, 0) / chartData.value.length
    return {
      averageLoad: Math.round(avgLoad),
      trend: avgLoad > 90 ? 'increasing' : avgLoad < 60 ? 'decreasing' : 'stable',
      confidence: 0.75
    }
  })

  const suggestions = computed((): LoadSuggestion[] => {
    const suggestions: LoadSuggestion[] = []
    const time = totalTime.value
    const count = taskCount.value

    if (time === 0) {
      suggestions.push({
        type: 'info',
        title: '今日无任务',
        description: '今天没有安排学习任务，可以考虑复习之前的内容或预习新知识。',
        actions: [
          { text: '查看复习计划', type: 'primary', action: 'review' },
          { text: '创建新任务', type: 'default', action: 'create' }
        ]
      })
    } else if (time <= 60) {
      suggestions.push({
        type: 'success',
        title: '负载适中',
        description: '当前学习负载处于理想范围，保持这种良好的学习节奏。',
        actions: [
          { text: '查看详细计划', type: 'primary', action: 'detail' }
        ]
      })
    } else if (time <= 120) {
      suggestions.push({
        type: 'warning',
        title: '负载较重',
        description: '今日学习任务较多，建议合理安排时间，注意劳逸结合。',
        actions: [
          { text: '调整任务时间', type: 'warning', action: 'adjust' },
          { text: '分散到其他日期', type: 'default', action: 'reschedule' }
        ]
      })
    } else {
      suggestions.push({
        type: 'warning',
        title: '负载过重',
        description: '今日学习负载过重，强烈建议重新安排部分任务，避免学习效果下降。',
        actions: [
          { text: '紧急调整', type: 'danger', action: 'emergency' },
          { text: '延后部分任务', type: 'warning', action: 'postpone' }
        ]
      })
    }

    if (count > 5) {
      suggestions.push({
        type: 'info',
        title: '任务数量较多',
        description: '今日任务数量较多，建议按优先级排序，重点完成高优先级任务。',
        actions: [
          { text: '重新排序', type: 'primary', action: 'reorder' }
        ]
      })
    }

    return suggestions
  })

  // 方法
  const calculateLoadLevel = (time: number): number => {
    if (time <= 30) {return 1}
    if (time <= 60) {return 2}
    if (time <= 90) {return 3}
    if (time <= 120) {return 4}
    return 5
  }

  const handleDateChange = () => {
    // 日期变化时重新计算数据
  }

  const refreshData = async () => {
    loading.value = true
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    loading.value = false
  }

  const handleSuggestionAction = (action: any) => {
    // 处理建议操作
    console.log('执行建议操作:', action)
  }

  // 监听
  watch(selectedDate, () => {
    // 日期变化时的处理
  })

  onMounted(() => {
    // 组件挂载时的初始化
  })
</script>

<style scoped>
  .load-dashboard {
    padding: 20px;
  }

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .dashboard-header h3 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .header-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .overview-cards {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  .overview-card {
    border-radius: 12px;
    transition: all 0.3s ease;
  }

  .overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .card-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: var(--el-color-primary);
  }

  .card-icon.load-light {
    background: #67C23A;
  }

  .card-info h4 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    font-weight: normal;
  }

  .load-value,
  .task-count,
  .avg-time,
  .warning-level {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .load-value.load-light {
    color: #67C23A;
  }

  .load-value.load-medium {
    color: #E6A23C;
  }

  .load-value.load-heavy {
    color: #F56C6C;
  }

  .warning-level.warning-safe {
    color: #67C23A;
  }

  .warning-level.warning-caution {
    color: #E6A23C;
  }

  .warning-level.warning-danger {
    color: #F56C6C;
  }

  .load-time,
  .task-breakdown,
  .time-range,
  .warning-desc {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .chart-section,
  .heatmap-section,
  .trend-section,
  .suggestions-section {
    grid-column: span 1;
  }

  .chart-card,
  .heatmap-card,
  .trend-card,
  .suggestions-card {
    height: 400px;
    border-radius: 12px;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-container,
  .heatmap-container,
  .trend-container {
    height: 300px;
  }

  .suggestions-content {
    max-height: 320px;
    overflow-y: auto;
  }

  .suggestion-item {
    display: flex;
    gap: 12px;
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    border-left: 4px solid;
  }

  .suggestion-item.info {
    background: var(--el-color-info-light-9);
    border-left-color: var(--el-color-info);
  }

  .suggestion-item.warning {
    background: var(--el-color-warning-light-9);
    border-left-color: var(--el-color-warning);
  }

  .suggestion-item.success {
    background: var(--el-color-success-light-9);
    border-left-color: var(--el-color-success);
  }

  .suggestion-icon {
    margin-top: 2px;
    flex-shrink: 0;
  }

  .suggestion-content h5 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
  }

  .suggestion-content p {
    margin: 0 0 8px 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1.5;
  }

  .suggestion-actions {
    display: flex;
    gap: 8px;
  }

  @media (max-width: 1200px) {
    .dashboard-grid {
      grid-template-columns: 1fr;
    }

    .overview-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .load-dashboard {
      padding: 12px;
    }

    .dashboard-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .overview-cards {
      grid-template-columns: 1fr;
    }

    .chart-card,
    .heatmap-card,
    .trend-card,
    .suggestions-card {
      height: auto;
    }
  }
</style>
