import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { ReviewPlan, ReviewRating, ReviewRecord } from '@/types'
import {
  getReviewPlans,
  getTodayReviews,
  updateReviewRecord
} from '@/mock/reviewData'

export const useReviewStore = defineStore('review', () => {
  // 状态
  const reviewPlans = ref<ReviewPlan[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const todayReviews = computed(() => {
    return getTodayReviews()
  })

  const totalReviewsCount = computed(() => {
    return reviewPlans.value.reduce((total, plan) => total + plan.totalCount, 0)
  })

  const completedReviewsCount = computed(() => {
    return reviewPlans.value.reduce((total, plan) => total + plan.completedCount, 0)
  })

  const completionRate = computed(() => {
    if (totalReviewsCount.value === 0) {return 0}
    return Math.round((completedReviewsCount.value / totalReviewsCount.value) * 100)
  })

  const activeReviewPlans = computed(() => {
    return reviewPlans.value.filter(plan => !plan.isCompleted)
  })

  const overdueReviews = computed(() => {
    const now = new Date()
    const overdue: ReviewRecord[] = []
    
    reviewPlans.value.forEach(plan => {
      plan.reviews.forEach(review => {
        if (review.status === 'scheduled' && new Date(review.scheduledTime) < now) {
          overdue.push(review)
        }
      })
    })
    
    return overdue.sort((a, b) => 
      new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
    )
  })

  // 获取复习统计
  const reviewStats = computed(() => {
    const stats = {
      totalPlans: reviewPlans.value.length,
      activePlans: activeReviewPlans.value.length,
      completedPlans: reviewPlans.value.filter(p => p.isCompleted).length,
      todayReviewsCount: todayReviews.value.length,
      overdueCount: overdueReviews.value.length,
      averageRating: 0,
      totalStudyTime: 0
    }

    // 计算平均评分和总学习时间
    let totalRating = 0
    let ratingCount = 0
    let totalTime = 0

    reviewPlans.value.forEach(plan => {
      plan.reviews.forEach(review => {
        if (review.rating) {
          totalRating += review.rating
          ratingCount++
        }
        if (review.duration) {
          totalTime += review.duration
        }
      })
    })

    stats.averageRating = ratingCount > 0 ? Math.round((totalRating / ratingCount) * 10) / 10 : 0
    stats.totalStudyTime = totalTime

    return stats
  })

  // 方法
  const loadReviewPlans = async () => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      reviewPlans.value = getReviewPlans()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载复习计划失败'
    } finally {
      loading.value = false
    }
  }

  const getReviewPlan = (taskId: string): ReviewPlan | undefined => {
    return reviewPlans.value.find(plan => plan.taskId === taskId)
  }

  const executeReview = async (
    taskId: string,
    intervalId: number,
    rating: ReviewRating,
    notes?: string,
    duration?: number
  ): Promise<boolean> => {
    try {
      const success = updateReviewRecord(taskId, intervalId, {
        status: 'completed',
        rating,
        notes,
        duration,
        actualTime: new Date().toISOString()
      })

      if (success) {
        // 重新加载数据以更新状态
        await loadReviewPlans()
      }

      return success
    } catch (err) {
      error.value = err instanceof Error ? err.message : '执行复习失败'
      return false
    }
  }

  const skipReview = async (taskId: string, intervalId: number): Promise<boolean> => {
    try {
      const success = updateReviewRecord(taskId, intervalId, {
        status: 'skipped',
        actualTime: new Date().toISOString()
      })

      if (success) {
        await loadReviewPlans()
      }

      return success
    } catch (err) {
      error.value = err instanceof Error ? err.message : '跳过复习失败'
      return false
    }
  }

  const markOverdue = async (taskId: string, intervalId: number): Promise<boolean> => {
    try {
      const success = updateReviewRecord(taskId, intervalId, {
        status: 'overdue'
      })

      if (success) {
        await loadReviewPlans()
      }

      return success
    } catch (err) {
      error.value = err instanceof Error ? err.message : '标记逾期失败'
      return false
    }
  }

  // 获取复习历史
  const getReviewHistory = (taskId: string): ReviewRecord[] => {
    const plan = getReviewPlan(taskId)
    return plan ? plan.reviews.filter(r => r.status === 'completed') : []
  }

  // 获取下一个复习任务
  const getNextReview = (): ReviewRecord | null => {
    const upcoming = todayReviews.value
    return upcoming.length > 0 ? upcoming[0] : null
  }

  return {
    // 状态
    reviewPlans,
    loading,
    error,
    
    // 计算属性
    todayReviews,
    totalReviewsCount,
    completedReviewsCount,
    completionRate,
    activeReviewPlans,
    overdueReviews,
    reviewStats,
    
    // 方法
    loadReviewPlans,
    getReviewPlan,
    executeReview,
    skipReview,
    markOverdue,
    getReviewHistory,
    getNextReview
  }
}, {
  persist: {
    key: 'review-store',
    storage: localStorage,
    paths: ['reviewPlans']
  }
})
