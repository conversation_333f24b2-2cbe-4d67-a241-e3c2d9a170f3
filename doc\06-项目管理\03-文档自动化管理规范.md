# 文档自动化管理规范

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的文档自动化管理机制，包括自动化更新、版本控制流程和文档与代码同步机制，确保文档的持续维护和高质量。

## 🤖 自动化文档更新机制

### [AUTO-DOC-001] 文档自动检查机制
**机制ID**：AUTO-DOC-001  
**触发条件**：代码提交、文档修改、定时任务  
**检查内容**：

```yaml
# .github/workflows/doc-check.yml
name: 文档质量检查
on:
  push:
    paths:
      - 'doc/**'
      - 'src/**'
  pull_request:
    paths:
      - 'doc/**'
      - 'src/**'
  schedule:
    - cron: '0 2 * * 1'  # 每周一凌晨2点

jobs:
  doc-quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 检查文档格式
        run: |
          # Markdown格式检查
          npx markdownlint doc/**/*.md
          
      - name: 检查链接有效性
        run: |
          # 检查文档内部链接
          npx markdown-link-check doc/**/*.md
          
      - name: 检查术语一致性
        run: |
          # 自定义脚本检查术语使用
          node scripts/check-terminology.js
          
      - name: 检查ID唯一性
        run: |
          # 检查文档ID重复
          node scripts/check-unique-ids.js
          
      - name: 生成质量报告
        run: |
          # 生成文档质量报告
          node scripts/generate-quality-report.js
```

### [AUTO-DOC-002] API文档自动生成
**机制ID**：AUTO-DOC-002  
**触发条件**：API代码变更  
**生成内容**：

```javascript
// scripts/generate-api-docs.js
const swaggerJSDoc = require('swagger-jsdoc');
const fs = require('fs');
const path = require('path');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '艾宾浩斯学习管理系统 API',
      version: '1.0.0',
      description: '基于艾宾浩斯记忆曲线的学习管理系统API文档'
    },
    servers: [
      {
        url: 'http://localhost:3000/api',
        description: '开发环境'
      },
      {
        url: 'https://api.ebbinghaus.com',
        description: '生产环境'
      }
    ]
  },
  apis: ['./src/routes/*.js', './src/controllers/*.js']
};

// 生成API文档
const specs = swaggerJSDoc(options);

// 更新API设计文档
const apiDocTemplate = `# [DES-API-001] API接口设计（自动生成）

## 📋 概述
本文档由代码自动生成，包含最新的API接口定义。

**生成时间**：${new Date().toISOString()}  
**代码版本**：${process.env.GIT_COMMIT || 'latest'}

## 🔧 API接口列表

${generateApiDocumentation(specs)}

---
**注意**：此文档由自动化脚本生成，请勿手动编辑。如需修改，请更新代码中的注释。
`;

fs.writeFileSync('doc/02-系统设计/08-API接口设计-自动生成.md', apiDocTemplate);

function generateApiDocumentation(specs) {
  // 解析OpenAPI规范并生成Markdown文档
  let markdown = '';
  
  for (const [path, methods] of Object.entries(specs.paths || {})) {
    for (const [method, spec] of Object.entries(methods)) {
      markdown += `### ${method.toUpperCase()} ${path}\n`;
      markdown += `**描述**：${spec.summary || spec.description || ''}\n\n`;
      
      if (spec.requestBody) {
        markdown += `**请求格式**：\n\`\`\`json\n${JSON.stringify(spec.requestBody.content['application/json'].example || {}, null, 2)}\n\`\`\`\n\n`;
      }
      
      if (spec.responses) {
        markdown += `**响应格式**：\n\`\`\`json\n${JSON.stringify(spec.responses['200']?.content?.['application/json']?.example || {}, null, 2)}\n\`\`\`\n\n`;
      }
    }
  }
  
  return markdown;
}
```

### [AUTO-DOC-003] 数据模型文档自动更新
**机制ID**：AUTO-DOC-003  
**触发条件**：数据模型代码变更  
**更新内容**：

```javascript
// scripts/sync-data-models.js
const fs = require('fs');
const path = require('path');

// 扫描数据模型文件
const modelFiles = fs.readdirSync('src/models')
  .filter(file => file.endsWith('.js'))
  .map(file => require(path.join('../src/models', file)));

// 生成数据模型文档
let modelDoc = `# [DES-MODEL-001] 数据模型设计（自动同步）

## 📋 概述
本文档与代码中的数据模型定义保持同步。

**同步时间**：${new Date().toISOString()}  
**代码版本**：${process.env.GIT_COMMIT || 'latest'}

`;

modelFiles.forEach((model, index) => {
  if (model.schema) {
    modelDoc += `## [DES-MODEL-${String(index + 1).padStart(3, '0')}] ${model.name}模型\n\n`;
    modelDoc += `**集合名称**：${model.collection || model.name.toLowerCase()}\n\n`;
    modelDoc += `**数据结构**：\n\`\`\`javascript\n${generateSchemaDoc(model.schema)}\n\`\`\`\n\n`;
  }
});

fs.writeFileSync('doc/02-系统设计/09-数据模型设计-自动同步.md', modelDoc);

function generateSchemaDoc(schema) {
  // 将Mongoose Schema转换为文档格式
  const schemaObj = {};
  
  for (const [field, definition] of Object.entries(schema.paths)) {
    if (field !== '_id' && field !== '__v') {
      schemaObj[field] = {
        type: definition.instance,
        required: definition.isRequired,
        description: definition.options.description || ''
      };
    }
  }
  
  return JSON.stringify(schemaObj, null, 2);
}
```

## 📝 文档版本控制流程

### [VERSION-CONTROL-001] 文档版本管理规范
**规范ID**：VERSION-CONTROL-001  
**版本格式**：`v{major}.{minor}.{patch}`

```yaml
# 版本控制配置文件 .docversion.yml
version_scheme: semantic
current_version: "1.0.0"

# 版本变更规则
version_rules:
  major:  # 主版本号 (1.0.0 -> 2.0.0)
    - 文档结构重大调整
    - 需求规格重大变更
    - 不兼容的API变更
    
  minor:  # 次版本号 (1.0.0 -> 1.1.0)
    - 新增功能需求
    - 新增API接口
    - 新增设计文档
    
  patch:  # 修订版本号 (1.0.0 -> 1.0.1)
    - 错误修正
    - 内容完善
    - 格式调整

# 自动版本更新触发条件
auto_version_triggers:
  - path: "doc/01-需求分析/**"
    change_type: "major"
  - path: "doc/02-系统设计/**"
    change_type: "minor"
  - path: "doc/**/*.md"
    change_type: "patch"
```

### [VERSION-CONTROL-002] 文档变更审批流程
**流程ID**：VERSION-CONTROL-002  
**审批矩阵**：

```yaml
# .github/CODEOWNERS
# 文档变更审批矩阵

# 需求分析文档 - 需要产品经理审批
doc/01-需求分析/** @product-manager @system-analyst

# 系统设计文档 - 需要架构师审批  
doc/02-系统设计/** @system-architect @tech-lead

# 测试部署文档 - 需要测试负责人审批
doc/05-测试部署/** @test-lead @devops-engineer

# 基础规范文档 - 需要项目经理审批
doc/00-*.md @project-manager

# 自动生成文档 - 无需人工审批
doc/**/*-自动生成.md
doc/**/*-自动同步.md
```

### [VERSION-CONTROL-003] 文档发布流程
**流程ID**：VERSION-CONTROL-003  
**发布步骤**：

```yaml
# .github/workflows/doc-release.yml
name: 文档发布流程
on:
  push:
    tags:
      - 'doc-v*'

jobs:
  doc-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 验证文档质量
        run: |
          # 运行完整的文档质量检查
          npm run doc:check
          
      - name: 生成文档包
        run: |
          # 创建文档发布包
          mkdir -p release/docs
          cp -r doc/* release/docs/
          
          # 生成文档索引
          node scripts/generate-doc-index.js
          
          # 创建压缩包
          cd release && zip -r "docs-${GITHUB_REF#refs/tags/}.zip" docs/
          
      - name: 发布到GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./release/docs
          
      - name: 创建Release
        uses: actions/create-release@v1
        with:
          tag_name: ${{ github.ref }}
          release_name: 文档版本 ${{ github.ref }}
          body: |
            ## 文档变更说明
            
            此版本包含以下主要变更：
            - 自动生成的变更日志
            - 文档质量评分：自动计算
            
            ## 下载
            - [完整文档包](./docs-${{ github.ref }}.zip)
            - [在线查看](https://your-org.github.io/ebbinghaus-docs/)
```

## 🔄 文档与代码同步机制

### [SYNC-MECHANISM-001] 代码注释同步
**机制ID**：SYNC-MECHANISM-001  
**同步规则**：

```javascript
// 代码注释规范示例
/**
 * @api {post} /api/tasks 创建学习任务
 * @apiName CreateTask
 * @apiGroup Task
 * @apiVersion 1.0.0
 * 
 * @apiDescription 
 * 创建新的学习任务，自动生成艾宾浩斯复习计划
 * 对应需求：[REQ-FUNC-001] 学习任务创建功能
 * 
 * @apiParam {String{1-100}} title 任务标题
 * @apiParam {String{1-5000}} content 任务内容  
 * @apiParam {String} subject 学科分类，参考[TERM-025]学科枚举
 * @apiParam {Number{1-300}} [estimatedTime] 预估时间（分钟）
 * 
 * @apiSuccess {String} taskId 任务唯一标识
 * @apiSuccess {Object[]} reviewSchedule 复习计划数组
 * @apiSuccess {Object} [loadWarning] 负载预警信息
 * 
 * @apiError (400) InvalidInput 输入数据验证失败
 * @apiError (409) TitleDuplicate 任务标题重复
 * 
 * @apiExample {curl} 请求示例:
 *   curl -X POST http://localhost:3000/api/tasks \
 *     -H "Content-Type: application/json" \
 *     -d '{"title":"英语单词Unit3","subject":"english","estimatedTime":30}'
 */
async function createTask(req, res) {
  // 实现代码...
}
```

### [SYNC-MECHANISM-002] 数据模型同步
**机制ID**：SYNC-MECHANISM-002  
**同步实现**：

```javascript
// src/models/Task.js - 数据模型定义
const mongoose = require('mongoose');

/**
 * @model Task
 * @description 学习任务数据模型
 * @implements [DES-MODEL-001] 学习任务数据模型
 * @version 1.0.0
 */
const taskSchema = new mongoose.Schema({
  /**
   * @field title
   * @type String
   * @required true
   * @constraint 1-100字符，不能重复
   * @description 任务标题
   * @validation [REQ-CONST-001] 任务标题验证规则
   */
  title: {
    type: String,
    required: true,
    minlength: 1,
    maxlength: 100,
    unique: true,
    description: '任务标题，对应[REQ-CONST-001]验证规则'
  },
  
  /**
   * @field subject
   * @type String
   * @required true
   * @enum 参考[TERM-025]学科枚举
   * @description 学科分类
   */
  subject: {
    type: String,
    required: true,
    enum: ['math', 'chinese', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'politics'],
    description: '学科分类，对应[TERM-025]学科枚举'
  }
  
  // 其他字段定义...
}, {
  timestamps: true,
  collection: 'tasks'
});

// 自动同步到文档的元数据
taskSchema.statics.getDocumentationMeta = function() {
  return {
    modelId: 'DES-MODEL-001',
    modelName: 'Task',
    description: '学习任务数据模型',
    implements: ['REQ-FUNC-001'],
    version: '1.0.0',
    lastSync: new Date().toISOString()
  };
};

module.exports = mongoose.model('Task', taskSchema);
```

### [SYNC-MECHANISM-003] 配置文件同步
**机制ID**：SYNC-MECHANISM-003  
**同步配置**：

```javascript
// scripts/sync-config-docs.js
const fs = require('fs');
const path = require('path');

// 配置文件到文档的映射
const configMappings = [
  {
    source: 'config/database.js',
    target: 'doc/02-系统设计/06-数据存储与同步设计.md',
    section: '数据库配置',
    extractor: extractDatabaseConfig
  },
  {
    source: 'config/server.js', 
    target: 'doc/05-测试部署/04-环境配置.md',
    section: '服务器配置',
    extractor: extractServerConfig
  }
];

// 自动同步配置到文档
function syncConfigToDoc() {
  configMappings.forEach(mapping => {
    const config = require(path.join('..', mapping.source));
    const configDoc = mapping.extractor(config);
    
    // 更新文档中的配置部分
    updateDocumentSection(mapping.target, mapping.section, configDoc);
  });
}

function extractDatabaseConfig(config) {
  return `
### 数据库配置（自动同步）
\`\`\`javascript
// 同步时间：${new Date().toISOString()}
// 配置来源：${config.__filename || 'config/database.js'}

const databaseConfig = {
  mongodb: {
    uri: '${config.mongodb?.uri || 'mongodb://localhost:27017/ebbinghaus'}',
    options: ${JSON.stringify(config.mongodb?.options || {}, null, 2)}
  },
  redis: {
    host: '${config.redis?.host || 'localhost'}',
    port: ${config.redis?.port || 6379}
  }
};
\`\`\`
`;
}

// 定时同步任务
setInterval(syncConfigToDoc, 24 * 60 * 60 * 1000); // 每24小时同步一次
```

## 📊 自动化监控和报告

### [MONITORING-001] 文档健康度监控
**监控指标**：

```javascript
// scripts/doc-health-monitor.js
const metrics = {
  // 完整性指标
  completeness: {
    totalDocs: 0,
    missingDocs: [],
    brokenLinks: [],
    score: 0
  },
  
  // 一致性指标  
  consistency: {
    termUsage: {},
    formatIssues: [],
    score: 0
  },
  
  // 时效性指标
  freshness: {
    outdatedDocs: [],
    lastUpdateDays: {},
    score: 0
  },
  
  // 同步状态
  syncStatus: {
    codeDocSync: 'synced', // synced | outdated | error
    apiDocSync: 'synced',
    modelDocSync: 'synced'
  }
};

// 生成健康度报告
function generateHealthReport() {
  const report = `
# 文档健康度报告

**生成时间**：${new Date().toISOString()}
**整体评分**：${calculateOverallScore()}/5.0

## 📊 关键指标

| 维度 | 评分 | 状态 | 问题数 |
|------|------|------|--------|
| 完整性 | ${metrics.completeness.score}/100 | ${getStatus(metrics.completeness.score)} | ${metrics.completeness.missingDocs.length} |
| 一致性 | ${metrics.consistency.score}/100 | ${getStatus(metrics.consistency.score)} | ${metrics.consistency.formatIssues.length} |
| 时效性 | ${metrics.freshness.score}/100 | ${getStatus(metrics.freshness.score)} | ${metrics.freshness.outdatedDocs.length} |

## 🔄 同步状态

- **代码文档同步**：${metrics.syncStatus.codeDocSync}
- **API文档同步**：${metrics.syncStatus.apiDocSync}  
- **数据模型同步**：${metrics.syncStatus.modelDocSync}

## 📋 待处理问题

${generateIssueList()}

---
*此报告由自动化系统生成*
`;

  fs.writeFileSync('doc/00-文档健康度报告.md', report);
}

// 每日生成报告
schedule.scheduleJob('0 8 * * *', generateHealthReport);
```

## 🔗 集成配置

### [INTEGRATION-001] 开发环境集成
**配置文件**：

```json
// package.json - 文档相关脚本
{
  "scripts": {
    "doc:check": "node scripts/check-doc-quality.js",
    "doc:sync": "node scripts/sync-all-docs.js", 
    "doc:generate": "node scripts/generate-api-docs.js",
    "doc:serve": "docsify serve doc",
    "doc:build": "node scripts/build-doc-site.js",
    "doc:health": "node scripts/doc-health-monitor.js"
  },
  "husky": {
    "hooks": {
      "pre-commit": "npm run doc:check",
      "post-commit": "npm run doc:sync"
    }
  }
}
```

---

**文档版本**：v1.1.0  
**创建时间**：2025-01-31  
**负责人**：文档工程师  
**审核人**：项目经理  
**状态**：实施中
