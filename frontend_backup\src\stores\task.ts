import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { CreateTaskRequest, Subject, Task, TaskFilter } from '@/types'
import { mockApi } from '@/mock/taskData'
import { ErrorLevel, ErrorType, handleError, handleNetworkError } from '@/utils/errorHandler'
import dayjs from 'dayjs'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)
  const filters = ref<TaskFilter>({
    subject: '',
    priority: 0,
    status: '',
    searchText: ''
  })

  // 计算属性
  const filteredTasks = computed(() => {
    let result = tasks.value

    // 按学科筛选
    if (filters.value.subject) {
      result = result.filter((task) => task.subject === filters.value.subject)
    }

    // 按优先级筛选
    if (filters.value.priority > 0) {
      result = result.filter((task) => task.priority >= filters.value.priority)
    }

    // 按状态筛选
    if (filters.value.status) {
      result = result.filter((task) => task.status === filters.value.status)
    }

    // 按搜索文本筛选
    if (filters.value.searchText) {
      const searchText = filters.value.searchText.toLowerCase()
      result = result.filter(
        (task) =>
          task.title.toLowerCase().includes(searchText) ||
          task.content.toLowerCase().includes(searchText) ||
          task.tags.some((tag) => tag.toLowerCase().includes(searchText))
      )
    }

    return result
  })

  const todayTasks = computed(() => {
    const today = dayjs().format('YYYY-MM-DD')
    return tasks.value.filter((task) => dayjs(task.nextReviewTime).format('YYYY-MM-DD') === today)
  })

  const urgentTasks = computed(() => {
    const now = dayjs()
    return tasks.value.filter((task) => {
      const reviewTime = dayjs(task.nextReviewTime)
      return reviewTime.isBefore(now.add(2, 'hour')) && task.status === 'active'
    })
  })

  const tasksBySubject = computed(() => {
    return (subject: Subject) => tasks.value.filter((task) => task.subject === subject)
  })

  const taskStats = computed(() => {
    const total = tasks.value.length
    const completed = tasks.value.filter((task) => task.status === 'completed').length
    const active = tasks.value.filter((task) => task.status === 'active').length
    const todayReviews = todayTasks.value.length

    return {
      total,
      completed,
      active,
      todayReviews,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
    }
  })

  // 操作方法
  const loadTasks = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApi.getTasks()
      tasks.value = response.data
    } catch (err) {
      error.value = '加载任务失败'
      handleNetworkError(err, 'loadTasks')
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: CreateTaskRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await mockApi.createTask(taskData)
      await loadTasks() // 重新加载任务列表
      return response.data
    } catch (err) {
      error.value = '创建任务失败'
      handleError(err, ErrorType.CLIENT, ErrorLevel.MEDIUM)
      throw err
    } finally {
      loading.value = false
    }
  }

  const getTask = async (id: string) => {
    loading.value = true
    try {
      const response = await mockApi.getTask(id)
      currentTask.value = response.data
      return response.data
    } catch (error) {
      console.error('获取任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateTask = (id: string, updates: Partial<Task>) => {
    const taskIndex = tasks.value.findIndex((task) => task.id === id)
    if (taskIndex !== -1) {
      tasks.value[taskIndex] = { ...tasks.value[taskIndex], ...updates }
    }
  }

  const deleteTask = (id: string) => {
    const taskIndex = tasks.value.findIndex((task) => task.id === id)
    if (taskIndex !== -1) {
      tasks.value.splice(taskIndex, 1)
    }
  }

  const setFilters = (newFilters: Partial<TaskFilter>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      subject: '',
      priority: 0,
      status: '',
      searchText: ''
    }
  }

  const markReviewCompleted = (taskId: string, reviewIndex: number) => {
    const task = tasks.value.find((t) => t.id === taskId)
    if (task) {
      const reviewItem = task.reviewSchedule.find((r) => r.reviewIndex === reviewIndex)
      if (reviewItem) {
        reviewItem.status = 'completed'

        // 更新下次复习时间
        const nextReview = task.reviewSchedule.find((r) => r.status === 'scheduled')
        if (nextReview) {
          task.nextReviewTime = nextReview.reviewTime
        } else {
          // 所有复习都完成了
          task.status = 'completed'
          task.nextReviewTime = ''
        }
      }
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    filters,

    // 计算属性
    filteredTasks,
    todayTasks,
    urgentTasks,
    tasksBySubject,
    taskStats,

    // 方法
    loadTasks,
    createTask,
    getTask,
    updateTask,
    deleteTask,
    setFilters,
    clearFilters,
    markReviewCompleted
  }
}, {
  persist: {
    key: 'task-store',
    storage: localStorage,
    paths: ['tasks', 'filters']
  }
})
