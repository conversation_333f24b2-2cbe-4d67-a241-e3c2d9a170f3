<template>
  <div class="learning-stats">
    <div class="stats-grid">
      <!-- 基础统计 -->
      <div class="stats-section">
        <h5>基础统计</h5>
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <span class="stat-label">总学习时长</span>
              <span class="stat-value">{{ totalDuration }}分钟</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <span class="stat-label">复习次数</span>
              <span class="stat-value">{{ reviewCount }}次</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <span class="stat-label">平均评分</span>
              <span class="stat-value">{{ averageRating.toFixed(1) }}</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <span class="stat-label">完成进度</span>
              <span class="stat-value">{{ completionRate }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习效率 -->
      <div class="stats-section">
        <h5>学习效率</h5>
        <div class="efficiency-chart">
          <div class="efficiency-item">
            <span class="efficiency-label">时间效率</span>
            <div class="efficiency-bar">
              <div 
                class="efficiency-fill"
                :style="{ width: `${timeEfficiency}%` }"
              ></div>
            </div>
            <span class="efficiency-value">{{ timeEfficiency }}%</span>
          </div>

          <div class="efficiency-item">
            <span class="efficiency-label">记忆保持</span>
            <div class="efficiency-bar">
              <div 
                class="efficiency-fill retention"
                :style="{ width: `${retentionRate}%` }"
              ></div>
            </div>
            <span class="efficiency-value">{{ retentionRate }}%</span>
          </div>

          <div class="efficiency-item">
            <span class="efficiency-label">学习稳定性</span>
            <div class="efficiency-bar">
              <div 
                class="efficiency-fill stability"
                :style="{ width: `${stabilityScore}%` }"
              ></div>
            </div>
            <span class="efficiency-value">{{ stabilityScore }}%</span>
          </div>
        </div>
      </div>

      <!-- 复习间隔分析 -->
      <div class="stats-section">
        <h5>复习间隔分析</h5>
        <div class="interval-analysis">
          <div
            v-for="interval in intervalStats"
            :key="interval.id"
            class="interval-item"
          >
            <div class="interval-header">
              <span class="interval-name">{{ interval.name }}</span>
              <el-tag 
                :type="getIntervalStatusType(interval.status)"
                size="small"
              >
                {{ getIntervalStatusText(interval.status) }}
              </el-tag>
            </div>
            <div class="interval-details">
              <div class="interval-detail">
                <span class="detail-label">计划时间:</span>
                <span class="detail-value">{{ formatDate(interval.scheduledTime) }}</span>
              </div>
              <div v-if="interval.actualTime" class="interval-detail">
                <span class="detail-label">实际时间:</span>
                <span class="detail-value">{{ formatDate(interval.actualTime) }}</span>
              </div>
              <div v-if="interval.rating" class="interval-detail">
                <span class="detail-label">效果评分:</span>
                <el-rate
                  :model-value="interval.rating"
                  disabled
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习建议 -->
      <div class="stats-section">
        <h5>学习建议</h5>
        <div class="suggestions">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            :class="suggestion.type"
          >
            <el-icon class="suggestion-icon">
              <InfoFilled v-if="suggestion.type === 'info'" />
              <Warning v-else-if="suggestion.type === 'warning'" />
              <SuccessFilled v-else />
            </el-icon>
            <div class="suggestion-content">
              <h6>{{ suggestion.title }}</h6>
              <p>{{ suggestion.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细分析 -->
    <div class="detailed-analysis">
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="详细学习分析" name="analysis">
          <div class="analysis-content">
            <div class="analysis-section">
              <h6>时间分布分析</h6>
              <p>{{ timeAnalysis }}</p>
            </div>
            
            <div class="analysis-section">
              <h6>效果评分趋势</h6>
              <p>{{ ratingTrend }}</p>
            </div>
            
            <div class="analysis-section">
              <h6>记忆曲线匹配度</h6>
              <p>{{ memoryMatchAnalysis }}</p>
            </div>
            
            <div class="analysis-section">
              <h6>优化建议</h6>
              <ul class="optimization-list">
                <li v-for="tip in optimizationTips" :key="tip">{{ tip }}</li>
              </ul>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { 
    Calendar, 
    Clock, 
    InfoFilled, 
    SuccessFilled,
    TrendCharts,
    Warning
  } from '@element-plus/icons-vue'
  import type { ReviewPlan, Task } from '@/types'
  import dayjs from 'dayjs'

  interface Props {
    task: Task
    reviewPlan: ReviewPlan | null
  }

  const props = defineProps<Props>()

  // 响应式数据
  const activeCollapse = ref<string[]>([])

  // 计算属性
  const completedReviews = computed(() => {
    if (!props.reviewPlan) {return []}
    return props.reviewPlan.reviews.filter(r => r.status === 'completed')
  })

  const totalDuration = computed(() => {
    return completedReviews.value.reduce((sum, r) => sum + (r.duration || 0), 0)
  })

  const reviewCount = computed(() => {
    return completedReviews.value.length
  })

  const averageRating = computed(() => {
    const validRatings = completedReviews.value.filter(r => r.rating && r.rating > 0)
    if (validRatings.length === 0) {return 0}
    return validRatings.reduce((sum, r) => sum + (r.rating || 0), 0) / validRatings.length
  })

  const completionRate = computed(() => {
    if (!props.reviewPlan) {return 0}
    const total = props.reviewPlan.reviews.length
    const completed = completedReviews.value.length
    return Math.round((completed / total) * 100)
  })

  const timeEfficiency = computed(() => {
    if (completedReviews.value.length === 0) {return 0}
    const avgDuration = totalDuration.value / completedReviews.value.length
    const estimatedTime = props.task.estimatedTime
    
    // 效率 = (预估时间 / 实际平均时间) * 100，但最高100%
    const efficiency = Math.min((estimatedTime / avgDuration) * 100, 100)
    return Math.round(efficiency)
  })

  const retentionRate = computed(() => {
    if (completedReviews.value.length < 2) {return 0}
    
    // 基于评分计算记忆保持率
    const ratings = completedReviews.value.map(r => r.rating || 0)
    const avgRating = ratings.reduce((sum, r) => sum + r, 0) / ratings.length
    
    return Math.round((avgRating / 5) * 100)
  })

  const stabilityScore = computed(() => {
    if (completedReviews.value.length < 3) {return 0}
    
    // 基于评分方差计算稳定性
    const ratings = completedReviews.value.map(r => r.rating || 0)
    const avg = ratings.reduce((sum, r) => sum + r, 0) / ratings.length
    const variance = ratings.reduce((sum, r) => sum + Math.pow(r - avg, 2), 0) / ratings.length
    
    // 方差越小，稳定性越高
    const stability = Math.max(0, 100 - variance * 20)
    return Math.round(stability)
  })

  const intervalStats = computed(() => {
    if (!props.reviewPlan) {return []}
    
    return props.reviewPlan.reviews.map(review => ({
      id: review.intervalId,
      name: `第${review.intervalId}次复习`,
      status: review.status,
      scheduledTime: review.scheduledTime,
      actualTime: review.actualTime,
      rating: review.rating,
      duration: review.duration
    }))
  })

  const suggestions = computed(() => {
    const suggestions = []
    
    if (averageRating.value < 3) {
      suggestions.push({
        type: 'warning',
        title: '学习效果需要改善',
        description: '平均评分较低，建议调整学习方法或增加学习时间。'
      })
    } else if (averageRating.value >= 4) {
      suggestions.push({
        type: 'success',
        title: '学习效果优秀',
        description: '保持当前的学习方法和节奏。'
      })
    }
    
    if (timeEfficiency.value < 60) {
      suggestions.push({
        type: 'warning',
        title: '时间效率偏低',
        description: '实际学习时间超出预期，建议优化学习方法。'
      })
    }
    
    if (stabilityScore.value < 70) {
      suggestions.push({
        type: 'info',
        title: '学习稳定性有待提高',
        description: '学习效果波动较大，建议保持规律的学习习惯。'
      })
    }
    
    if (suggestions.length === 0) {
      suggestions.push({
        type: 'success',
        title: '学习状态良好',
        description: '继续保持当前的学习节奏和方法。'
      })
    }
    
    return suggestions
  })

  const timeAnalysis = computed(() => {
    if (completedReviews.value.length === 0) {
      return '暂无足够数据进行分析。'
    }
    
    const avgDuration = totalDuration.value / completedReviews.value.length
    const estimatedTime = props.task.estimatedTime
    
    if (avgDuration > estimatedTime * 1.2) {
      return `平均学习时间(${avgDuration.toFixed(1)}分钟)超出预估时间较多，建议优化学习方法或调整预估时间。`
    } else if (avgDuration < estimatedTime * 0.8) {
      return `平均学习时间(${avgDuration.toFixed(1)}分钟)少于预估时间，学习效率较高。`
    } else {
      return `平均学习时间(${avgDuration.toFixed(1)}分钟)与预估时间基本匹配，时间控制良好。`
    }
  })

  const ratingTrend = computed(() => {
    if (completedReviews.value.length < 2) {
      return '需要更多复习数据来分析趋势。'
    }
    
    const firstRating = completedReviews.value[0].rating || 0
    const lastRating = completedReviews.value[completedReviews.value.length - 1].rating || 0
    const diff = lastRating - firstRating
    
    if (diff > 0.5) {
      return '学习效果呈上升趋势，说明掌握程度在不断提高。'
    } else if (diff < -0.5) {
      return '学习效果有所下降，建议重新审视学习方法。'
    } else {
      return '学习效果保持稳定，继续保持当前学习状态。'
    }
  })

  const memoryMatchAnalysis = computed(() => {
    if (!props.reviewPlan) {
      return '暂无复习计划数据。'
    }
    
    const onTimeReviews = props.reviewPlan.reviews.filter(r => {
      if (!r.actualTime) {return false}
      const scheduled = dayjs(r.scheduledTime)
      const actual = dayjs(r.actualTime)
      return Math.abs(actual.diff(scheduled, 'hour')) <= 24
    })
    
    const onTimeRate = (onTimeReviews.length / completedReviews.value.length) * 100
    
    if (onTimeRate >= 80) {
      return '复习时间与艾宾浩斯记忆曲线匹配度很高，有利于长期记忆。'
    } else if (onTimeRate >= 60) {
      return '复习时间基本符合记忆曲线，建议尽量按时复习。'
    } else {
      return '复习时间偏离记忆曲线较多，建议调整复习计划。'
    }
  })

  const optimizationTips = computed(() => {
    const tips = []
    
    if (timeEfficiency.value < 70) {
      tips.push('尝试使用番茄工作法提高专注度')
      tips.push('减少学习过程中的干扰因素')
    }
    
    if (averageRating.value < 3.5) {
      tips.push('增加主动回忆练习')
      tips.push('使用费曼学习法加深理解')
    }
    
    if (stabilityScore.value < 70) {
      tips.push('建立固定的学习时间和环境')
      tips.push('保持规律的作息时间')
    }
    
    tips.push('定期回顾和调整学习计划')
    tips.push('记录学习心得和难点')
    
    return tips
  })

  // 方法
  const formatDate = (dateStr: string): string => {
    return dayjs(dateStr).format('MM-DD HH:mm')
  }

  const getIntervalStatusType = (status: string): string => {
    switch (status) {
      case 'completed': return 'success'
      case 'scheduled': return 'primary'
      case 'overdue': return 'danger'
      default: return 'info'
    }
  }

  const getIntervalStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return '已完成'
      case 'scheduled': return '待复习'
      case 'overdue': return '已逾期'
      default: return '未知'
    }
  }
</script>

<style scoped>
  .learning-stats {
    padding: 16px 0;
  }

  .stats-grid {
    display: grid;
    gap: 24px;
    margin-bottom: 24px;
  }

  .stats-section h5 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
    font-size: 16px;
    font-weight: 600;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    background: var(--el-fill-color);
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--el-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }

  .stat-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .stat-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .efficiency-chart {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .efficiency-item {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .efficiency-label {
    min-width: 80px;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }

  .efficiency-bar {
    flex: 1;
    height: 8px;
    background: var(--el-fill-color);
    border-radius: 4px;
    overflow: hidden;
  }

  .efficiency-fill {
    height: 100%;
    background: var(--el-color-primary);
    transition: width 0.3s ease;
  }

  .efficiency-fill.retention {
    background: var(--el-color-success);
  }

  .efficiency-fill.stability {
    background: var(--el-color-warning);
  }

  .efficiency-value {
    min-width: 40px;
    text-align: right;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .interval-analysis {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .interval-item {
    padding: 12px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
  }

  .interval-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .interval-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .interval-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .interval-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
  }

  .detail-label {
    color: var(--el-text-color-secondary);
    min-width: 60px;
  }

  .detail-value {
    color: var(--el-text-color-primary);
  }

  .suggestions {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .suggestion-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid;
  }

  .suggestion-item.info {
    background: var(--el-color-info-light-9);
    border-left-color: var(--el-color-info);
  }

  .suggestion-item.warning {
    background: var(--el-color-warning-light-9);
    border-left-color: var(--el-color-warning);
  }

  .suggestion-item.success {
    background: var(--el-color-success-light-9);
    border-left-color: var(--el-color-success);
  }

  .suggestion-icon {
    margin-top: 2px;
    flex-shrink: 0;
  }

  .suggestion-content h6 {
    margin: 0 0 4px 0;
    color: var(--el-text-color-primary);
    font-size: 14px;
  }

  .suggestion-content p {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 12px;
    line-height: 1.5;
  }

  .detailed-analysis {
    border-top: 1px solid var(--el-border-color-light);
    padding-top: 16px;
  }

  .analysis-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .analysis-section h6 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 14px;
    font-weight: 600;
  }

  .analysis-section p {
    margin: 0;
    color: var(--el-text-color-regular);
    line-height: 1.6;
  }

  .optimization-list {
    margin: 0;
    padding-left: 16px;
    color: var(--el-text-color-regular);
  }

  .optimization-list li {
    margin-bottom: 4px;
    line-height: 1.5;
  }

  @media (max-width: 768px) {
    .stats-cards {
      grid-template-columns: 1fr;
    }

    .stat-card {
      padding: 12px;
    }

    .stat-icon {
      width: 32px;
      height: 32px;
      font-size: 16px;
    }

    .stat-value {
      font-size: 16px;
    }

    .efficiency-item {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .efficiency-label {
      min-width: auto;
    }

    .efficiency-value {
      text-align: left;
    }

    .interval-header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }

    .interval-detail {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }

    .detail-label {
      min-width: auto;
    }
  }
</style>
