/**
 * 全局错误处理工具
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown'
}

// 错误级别枚举
export enum ErrorLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  level: ErrorLevel
  message: string
  code?: string | number
  details?: any
  timestamp: number
  url?: string
  userAgent?: string
  userId?: string
  sessionId?: string
  stack?: string
}

// 错误处理配置
export interface ErrorHandlerConfig {
  enableConsoleLog: boolean
  enableRemoteLogging: boolean
  enableUserNotification: boolean
  maxRetries: number
  retryDelay: number
  remoteLogEndpoint?: string
}

// 默认配置
const defaultConfig: ErrorHandlerConfig = {
  enableConsoleLog: true,
  enableRemoteLogging: false,
  enableUserNotification: true,
  maxRetries: 3,
  retryDelay: 1000
}

class ErrorHandler {
  private config: ErrorHandlerConfig
  private errorQueue: ErrorInfo[] = []
  private retryCount: Map<string, number> = new Map()

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
    this.setupGlobalHandlers()
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalHandlers() {
    // 处理未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN,
        level: ErrorLevel.HIGH,
        message: event.reason?.message || 'Unhandled Promise Rejection',
        details: event.reason,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      })
      
      // 阻止默认的控制台错误输出
      event.preventDefault()
    })

    // 处理JavaScript运行时错误
    window.addEventListener('error', (event) => {
      this.handleError({
        type: ErrorType.CLIENT,
        level: ErrorLevel.HIGH,
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        },
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        stack: event.error?.stack
      })
    })

    // 处理资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError({
          type: ErrorType.NETWORK,
          level: ErrorLevel.MEDIUM,
          message: `Resource failed to load: ${(event.target as any)?.src || (event.target as any)?.href}`,
          details: {
            tagName: (event.target as any)?.tagName,
            src: (event.target as any)?.src,
            href: (event.target as any)?.href
          },
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      }
    }, true)
  }

  /**
   * 处理错误
   */
  public handleError(errorInfo: Partial<ErrorInfo>) {
    const fullErrorInfo: ErrorInfo = {
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.MEDIUM,
      message: 'Unknown error occurred',
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...errorInfo
    }

    // 添加到错误队列
    this.errorQueue.push(fullErrorInfo)

    // 控制台日志
    if (this.config.enableConsoleLog) {
      this.logToConsole(fullErrorInfo)
    }

    // 用户通知
    if (this.config.enableUserNotification) {
      this.notifyUser(fullErrorInfo)
    }

    // 远程日志
    if (this.config.enableRemoteLogging && this.config.remoteLogEndpoint) {
      this.logToRemote(fullErrorInfo)
    }

    // 清理旧错误
    this.cleanupErrorQueue()
  }

  /**
   * 控制台日志
   */
  private logToConsole(errorInfo: ErrorInfo) {
    const logMethod = this.getConsoleMethod(errorInfo.level)
    logMethod(`[${errorInfo.type.toUpperCase()}] ${errorInfo.message}`, {
      level: errorInfo.level,
      timestamp: new Date(errorInfo.timestamp).toISOString(),
      details: errorInfo.details,
      stack: errorInfo.stack
    })
  }

  /**
   * 获取控制台方法
   */
  private getConsoleMethod(level: ErrorLevel) {
    switch (level) {
      case ErrorLevel.LOW:
        return console.info
      case ErrorLevel.MEDIUM:
        return console.warn
      case ErrorLevel.HIGH:
      case ErrorLevel.CRITICAL:
        return console.error
      default:
        return console.log
    }
  }

  /**
   * 用户通知
   */
  private notifyUser(errorInfo: ErrorInfo) {
    const userMessage = this.getUserFriendlyMessage(errorInfo)

    switch (errorInfo.level) {
      case ErrorLevel.LOW:
        // 不显示通知
        break
      case ErrorLevel.MEDIUM:
        ElMessage.warning(userMessage)
        break
      case ErrorLevel.HIGH:
        ElMessage.error(userMessage)
        break
      case ErrorLevel.CRITICAL:
        ElNotification.error({
          title: '严重错误',
          message: userMessage,
          duration: 0, // 不自动关闭
          showClose: true
        })
        break
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    const messageMap: Record<ErrorType, string> = {
      [ErrorType.NETWORK]: '网络连接异常，请检查网络设置',
      [ErrorType.VALIDATION]: '输入数据有误，请检查后重试',
      [ErrorType.PERMISSION]: '权限不足，请联系管理员',
      [ErrorType.NOT_FOUND]: '请求的资源不存在',
      [ErrorType.SERVER]: '服务器异常，请稍后重试',
      [ErrorType.CLIENT]: '应用程序错误，请刷新页面重试',
      [ErrorType.UNKNOWN]: '未知错误，请稍后重试'
    }

    return messageMap[errorInfo.type] || errorInfo.message
  }

  /**
   * 远程日志
   */
  private async logToRemote(errorInfo: ErrorInfo) {
    if (!this.config.remoteLogEndpoint) {return}

    const errorKey = `${errorInfo.type}-${errorInfo.message}`
    const currentRetries = this.retryCount.get(errorKey) || 0

    if (currentRetries >= this.config.maxRetries) {
      return
    }

    try {
      await fetch(this.config.remoteLogEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorInfo)
      })

      // 成功后清除重试计数
      this.retryCount.delete(errorKey)
    } catch (_error) {
      // 增加重试计数
      this.retryCount.set(errorKey, currentRetries + 1)

      // 延迟重试
      setTimeout(() => {
        this.logToRemote(errorInfo)
      }, this.config.retryDelay * Math.pow(2, currentRetries))
    }
  }

  /**
   * 清理错误队列
   */
  private cleanupErrorQueue() {
    const maxQueueSize = 100
    const maxAge = 24 * 60 * 60 * 1000 // 24小时

    const now = Date.now()
    this.errorQueue = this.errorQueue
      .filter(error => now - error.timestamp < maxAge)
      .slice(-maxQueueSize)
  }

  /**
   * 获取错误统计
   */
  public getErrorStats() {
    const now = Date.now()
    const last24Hours = this.errorQueue.filter(error => now - error.timestamp < 24 * 60 * 60 * 1000)
    const lastHour = this.errorQueue.filter(error => now - error.timestamp < 60 * 60 * 1000)

    const typeStats = last24Hours.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<ErrorType, number>)

    const levelStats = last24Hours.reduce((acc, error) => {
      acc[error.level] = (acc[error.level] || 0) + 1
      return acc
    }, {} as Record<ErrorLevel, number>)

    return {
      total: this.errorQueue.length,
      last24Hours: last24Hours.length,
      lastHour: lastHour.length,
      typeStats,
      levelStats,
      recentErrors: this.errorQueue.slice(-10)
    }
  }

  /**
   * 清除错误队列
   */
  public clearErrors() {
    this.errorQueue = []
    this.retryCount.clear()
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ErrorHandlerConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
}

// 创建全局错误处理器实例
export const globalErrorHandler = new ErrorHandler()

// 便捷方法
export const handleError = (error: any, type: ErrorType = ErrorType.UNKNOWN, level: ErrorLevel = ErrorLevel.MEDIUM) => {
  globalErrorHandler.handleError({
    type,
    level,
    message: error?.message || String(error),
    details: error,
    stack: error?.stack
  })
}

// 网络错误处理
export const handleNetworkError = (error: any, url?: string) => {
  globalErrorHandler.handleError({
    type: ErrorType.NETWORK,
    level: ErrorLevel.HIGH,
    message: `Network request failed: ${error?.message || 'Unknown network error'}`,
    details: { error, url },
    url
  })
}

// 验证错误处理
export const handleValidationError = (message: string, details?: any) => {
  globalErrorHandler.handleError({
    type: ErrorType.VALIDATION,
    level: ErrorLevel.MEDIUM,
    message,
    details
  })
}

// 权限错误处理
export const handlePermissionError = (message: string = '权限不足') => {
  globalErrorHandler.handleError({
    type: ErrorType.PERMISSION,
    level: ErrorLevel.HIGH,
    message
  })
}

// 服务器错误处理
export const handleServerError = (error: any, code?: string | number) => {
  globalErrorHandler.handleError({
    type: ErrorType.SERVER,
    level: ErrorLevel.HIGH,
    message: error?.message || 'Server error occurred',
    code,
    details: error
  })
}
