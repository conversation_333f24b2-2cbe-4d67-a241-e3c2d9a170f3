# 开发工具配置指南

本文档介绍了项目的开发工具配置和使用方法。

## 📋 目录

- [开发环境要求](#开发环境要求)
- [VS Code 配置](#vs-code-配置)
- [代码质量工具](#代码质量工具)
- [开发脚本](#开发脚本)
- [调试配置](#调试配置)
- [Git 工作流](#git-工作流)
- [故障排除](#故障排除)

## 🛠️ 开发环境要求

### 必需软件

- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0
- **Git**: >= 2.30.0
- **VS Code**: >= 1.80.0 (推荐)

### 推荐浏览器

- Chrome >= 100
- Firefox >= 100
- Edge >= 100
- Safari >= 15

## 🎯 VS Code 配置

### 必装扩展

项目已配置了推荐扩展列表，首次打开项目时 VS Code 会提示安装：

- **Vue.volar**: Vue 3 语言支持
- **esbenp.prettier-vscode**: 代码格式化
- **dbaeumer.vscode-eslint**: 代码检查
- **ms-playwright.playwright**: E2E 测试支持
- **vitest.explorer**: 单元测试支持

### 自动配置

项目包含以下 VS Code 配置文件：

- `.vscode/settings.json`: 编辑器设置
- `.vscode/extensions.json`: 推荐扩展
- `.vscode/launch.json`: 调试配置
- `.vscode/tasks.json`: 任务配置

### 快捷键

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 格式化代码 | `Shift + Alt + F` | 使用 Prettier 格式化 |
| 修复 ESLint | `Ctrl + Shift + P` → `ESLint: Fix all` | 自动修复 ESLint 问题 |
| 运行任务 | `Ctrl + Shift + P` → `Tasks: Run Task` | 运行预定义任务 |
| 调试 | `F5` | 启动调试 |

## 🔍 代码质量工具

### ESLint

ESLint 配置文件：`eslint.config.js`

```bash
# 检查代码
npm run lint:check

# 自动修复
npm run lint
```

### Prettier

Prettier 配置文件：`.prettierrc`

```bash
# 检查格式
npm run format:check

# 格式化代码
npm run format
```

### TypeScript

TypeScript 配置文件：`tsconfig.app.json`

```bash
# 类型检查
npm run type-check
```

### 代码质量检查

```bash
# 运行所有检查
npm run check

# 运行检查并自动修复
npm run check:fix
```

## 📜 开发脚本

### 基础命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 测试命令

```bash
# 单元测试
npm run test
npm run test:ui
npm run test:coverage

# E2E 测试
npm run test:e2e
npm run test:e2e:ui
npm run test:e2e:debug
```

### 维护命令

```bash
# 清理缓存
npm run clean

# 重新安装依赖
npm run reinstall

# 检查过期依赖
npm run outdated

# 更新依赖
npm run update:deps

# 安全审计
npm audit
npm run audit:fix
```

## 🐛 调试配置

### 浏览器调试

1. 启动开发服务器：`npm run dev`
2. 在 VS Code 中按 `F5` 选择 "Launch Chrome"
3. 在代码中设置断点
4. 在浏览器中操作触发断点

### 测试调试

#### 单元测试调试

1. 在测试文件中设置断点
2. 按 `F5` 选择 "Debug Vitest Tests"
3. 或者调试当前文件："Debug Vitest Current File"

#### E2E 测试调试

1. 在测试文件中设置断点
2. 按 `F5` 选择 "Debug Playwright Tests"
3. 或者使用 `npm run test:e2e:debug`

### 构建调试

```bash
# 调试构建过程
npm run build -- --mode development
```

## 🔄 Git 工作流

### 提交前检查

项目配置了 Git hooks，每次提交前会自动运行：

1. ESLint 检查
2. Prettier 格式检查
3. TypeScript 类型检查
4. 单元测试

### 启用 Git hooks

```bash
# 设置 Git hooks 路径
git config core.hooksPath .githooks

# 给 hooks 文件执行权限（Linux/macOS）
chmod +x .githooks/pre-commit
```

### 提交规范

使用语义化提交信息：

```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 重构代码
test: 添加测试
chore: 构建工具或依赖更新
```

## 🚨 故障排除

### 常见问题

#### 1. ESLint 错误

```bash
# 清除 ESLint 缓存
rm -rf .eslintcache
npm run lint
```

#### 2. TypeScript 错误

```bash
# 清除 TypeScript 缓存
rm -rf node_modules/.tmp
npm run type-check
```

#### 3. 依赖问题

```bash
# 重新安装依赖
npm run reinstall
```

#### 4. 端口占用

```bash
# 查看端口占用
netstat -ano | findstr :5173  # Windows
lsof -i :5173                 # macOS/Linux

# 杀死进程
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # macOS/Linux
```

#### 5. 构建失败

```bash
# 清理构建缓存
npm run clean
npm run build
```

### 性能优化

#### 1. 开发服务器慢

```bash
# 清理 Vite 缓存
rm -rf node_modules/.vite
npm run dev
```

#### 2. 测试运行慢

```bash
# 并行运行测试
npm run test -- --reporter=dot
```

#### 3. 构建体积大

```bash
# 分析构建体积
npm run build -- --mode=analyze
```

### 获取帮助

1. 查看项目文档：`docs/` 目录
2. 查看 package.json 中的脚本
3. 查看各工具的配置文件
4. 在项目仓库中提交 Issue

## 📚 相关文档

- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [ESLint 文档](https://eslint.org/)
- [Prettier 文档](https://prettier.io/)
- [Vitest 文档](https://vitest.dev/)
- [Playwright 文档](https://playwright.dev/)

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
