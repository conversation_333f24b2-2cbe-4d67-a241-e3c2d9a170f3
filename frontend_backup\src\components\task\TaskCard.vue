<template>
  <el-card class="task-card" :class="cardClass">
    <template #header>
      <div class="task-header">
        <div class="header-left">
          <h3 class="task-title">{{ task.title }}</h3>
          <div class="task-tags">
            <el-tag :type="subjectType" size="small" class="subject-tag">
              {{ subjectLabel }}
            </el-tag>
            <el-tag v-if="task.priority >= 4" type="warning" size="small"> 高优先级 </el-tag>
          </div>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <el-button type="text" class="more-btn">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="view">查看详情</el-dropdown-item>
                <el-dropdown-item command="edit">编辑任务</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>

    <div class="task-content">
      <p class="task-description">{{ task.content }}</p>

      <div class="task-meta">
        <div class="meta-item">
          <el-icon><Clock /></el-icon>
          <span>{{ task.estimatedTime }}分钟</span>
        </div>
        <div class="meta-item">
          <el-icon><Star /></el-icon>
          <span>难度 {{ task.difficulty }}/5</span>
        </div>
        <div class="meta-item">
          <el-icon><Calendar /></el-icon>
          <span>{{ nextReviewText }}</span>
        </div>
      </div>

      <!-- 复习进度 -->
      <div class="review-progress">
        <div class="progress-label">
          <span>复习进度</span>
          <span class="progress-text">{{ completedReviews }}/{{ totalReviews }}</span>
        </div>
        <el-progress
          :percentage="progressPercentage"
          :stroke-width="6"
          :show-text="false"
          :color="progressColor"
        />
      </div>

      <!-- 标签 -->
      <div v-if="task.tags.length > 0" class="task-tags-section">
        <el-tag v-for="tag in task.tags" :key="tag" size="small" class="task-tag">
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="task-actions">
        <el-button v-if="canReview" type="primary" @click="$emit('start-review', task)">
          <el-icon><VideoPlay /></el-icon>
          开始复习
        </el-button>
        <el-button v-else-if="task.status === 'completed'" type="success" disabled>
          <el-icon><Check /></el-icon>
          已完成
        </el-button>
        <el-button v-else @click="$emit('view-detail', task)"> 查看详情 </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import type { Task } from '@/types'
  import { subjectOptions } from '@/mock/taskData'
  import dayjs from 'dayjs'
  import { Calendar, Check, Clock, MoreFilled, Star, VideoPlay } from '@element-plus/icons-vue'

  interface Props {
    task: Task
  }

  const props = defineProps<Props>()

  const emit = defineEmits<{
    'start-review': [task: Task]
    'view-detail': [task: Task]
    edit: [task: Task]
    delete: [task: Task]
  }>()

  const subjectLabel = computed(() => {
    return subjectOptions.find((s) => s.value === props.task.subject)?.label || props.task.subject
  })

  const subjectType = computed(() => {
    const types: Record<string, string> = {
      chinese: 'danger',
      math: 'success',
      english: 'primary',
      physics: 'info',
      chemistry: 'warning',
      biology: '',
      history: 'warning',
      geography: 'success'
    }
    return types[props.task.subject] || 'info'
  })

  const cardClass = computed(() => {
    return {
      'high-priority': props.task.priority >= 4,
      overdue: isOverdue.value,
      completed: props.task.status === 'completed'
    }
  })

  const nextReviewText = computed(() => {
    if (!props.task.nextReviewTime) {
      return '无复习计划'
    }

    const reviewTime = dayjs(props.task.nextReviewTime)
    const now = dayjs()

    if (reviewTime.isSame(now, 'day')) {
      return `今天 ${reviewTime.format('HH:mm')}`
    } else if (reviewTime.isSame(now.add(1, 'day'), 'day')) {
      return `明天 ${reviewTime.format('HH:mm')}`
    } else {
      return reviewTime.format('MM-DD HH:mm')
    }
  })

  const isOverdue = computed(() => {
    if (!props.task.nextReviewTime || props.task.status === 'completed') {
      return false
    }
    return dayjs(props.task.nextReviewTime).isBefore(dayjs())
  })

  const canReview = computed(() => {
    return (
      props.task.status === 'active' &&
      props.task.nextReviewTime &&
      dayjs(props.task.nextReviewTime).isBefore(dayjs().add(30, 'minute'))
    )
  })

  const completedReviews = computed(() => {
    return props.task.reviewSchedule.filter((r) => r.status === 'completed').length
  })

  const totalReviews = computed(() => {
    return props.task.reviewSchedule.length
  })

  const progressPercentage = computed(() => {
    if (totalReviews.value === 0) {return 0}
    return Math.round((completedReviews.value / totalReviews.value) * 100)
  })

  const progressColor = computed(() => {
    const percentage = progressPercentage.value
    if (percentage >= 80) {return '#67C23A'}
    if (percentage >= 50) {return '#E6A23C'}
    return '#F56C6C'
  })

  const handleCommand = (command: string) => {
    switch (command) {
      case 'view':
        emit('view-detail', props.task)
        break
      case 'edit':
        emit('edit', props.task)
        break
      case 'delete':
        emit('delete', props.task)
        break
    }
  }
</script>

<style scoped>
  .task-card {
    transition: all 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .task-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .task-card.high-priority {
    border-left: 4px solid var(--el-color-warning);
  }

  .task-card.overdue {
    border-left: 4px solid var(--el-color-danger);
  }

  .task-card.completed {
    opacity: 0.8;
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .header-left {
    flex: 1;
  }

  .task-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    line-height: 1.4;
  }

  .task-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }

  .subject-tag {
    font-weight: 500;
  }

  .more-btn {
    padding: 4px;
    font-size: 16px;
  }

  .task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .task-description {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    margin: 0 0 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .task-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }

  .meta-item .el-icon {
    font-size: 14px;
  }

  .review-progress {
    margin-bottom: 16px;
  }

  .progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
  }

  .progress-text {
    font-weight: 600;
    color: var(--el-color-primary);
  }

  .task-tags-section {
    margin-top: auto;
    padding-top: 12px;
  }

  .task-tag {
    margin-right: 6px;
    margin-bottom: 4px;
  }

  .task-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  @media (max-width: 768px) {
    .task-meta {
      flex-direction: column;
    }

    .task-actions {
      justify-content: stretch;
    }

    .task-actions .el-button {
      flex: 1;
    }
  }
</style>
