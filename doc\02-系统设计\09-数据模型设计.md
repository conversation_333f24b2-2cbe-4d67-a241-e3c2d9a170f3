# [DES-MODEL-001] 数据模型设计

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的数据模型设计，包括实体关系、数据结构、索引设计和数据约束。

## 🏗️ 数据库设计原则

### [DES-MODEL-PRINCIPLE-001] 设计原则
- **规范化设计**：遵循第三范式，减少数据冗余
- **性能优化**：合理设计索引，优化查询性能
- **扩展性**：支持未来功能扩展和数据增长
- **一致性**：保证数据完整性和一致性

### [DES-MODEL-PRINCIPLE-002] 技术选型
- **主数据库**：MongoDB（文档型数据库）
- **缓存数据库**：Redis（键值存储）
- **本地存储**：IndexedDB（浏览器本地存储）

## 📊 核心实体模型

### [DES-MODEL-001] 学习任务模型
**模型ID**：DES-MODEL-001  
**集合名称**：tasks  
**实现需求**：[REQ-FUNC-001], [REQ-FUNC-005]

```javascript
{
  _id: ObjectId,
  taskId: String,           // 任务唯一标识
  userId: String,           // 用户ID
  title: String,            // 任务标题
  content: {
    text: String,           // 文本内容
    images: [String],       // 图片URL数组
    audio: String,          // 音频URL
    attachments: [String]   // 附件URL数组
  },
  metadata: {
    subject: String,        // 学科分类
    estimatedTime: Number,  // 预估时间（分钟）
    actualTime: Number,     // 实际时间（分钟）
    priority: Number,       // 优先级（1-5）
    difficulty: Number,     // 难度级别（1-5）
    tags: [String]         // 标签数组
  },
  status: String,          // 任务状态：pending|active|completed|cancelled
  progress: {
    completedAt: Date,     // 完成时间
    startedAt: Date,       // 开始时间
    notes: String          // 学习笔记
  },
  timestamps: {
    createdAt: Date,       // 创建时间
    updatedAt: Date,       // 更新时间
    deletedAt: Date        // 删除时间（软删除）
  },
  // 索引字段
  searchText: String       // 搜索文本（title + content.text）
}
```

**索引设计**：
```javascript
// 复合索引
{ userId: 1, status: 1, createdAt: -1 }
{ userId: 1, subject: 1, createdAt: -1 }
{ userId: 1, priority: 1, createdAt: -1 }

// 文本索引
{ searchText: "text" }

// 单字段索引
{ taskId: 1 }
{ createdAt: -1 }
```

### [DES-MODEL-002] 复习计划模型
**模型ID**：DES-MODEL-002  
**集合名称**：review_schedules  
**实现需求**：[REQ-FUNC-002], [REQ-FUNC-004]

```javascript
{
  _id: ObjectId,
  scheduleId: String,      // 复习计划唯一标识
  taskId: String,          // 关联任务ID
  userId: String,          // 用户ID
  reviewItems: [
    {
      reviewIndex: Number,  // 复习序号（1-9）
      scheduledTime: Date,  // 计划复习时间
      actualTime: Date,     // 实际复习时间
      status: String,       // 状态：scheduled|in_progress|completed|skipped
      effectiveness: Number, // 复习效果评分（1-5）
      duration: Number,     // 复习时长（分钟）
      notes: String,        // 复习笔记
      adjustedTime: Date    // 调整后的时间
    }
  ],
  algorithm: {
    intervals: [Number],    // 时间间隔数组（分钟）
    baseTime: Date,         // 基准时间（任务创建时间）
    adjustmentFactor: Number // 调整系数
  },
  statistics: {
    totalReviews: Number,   // 总复习次数
    completedReviews: Number, // 已完成复习次数
    averageEffectiveness: Number, // 平均效果评分
    totalDuration: Number   // 总复习时长
  },
  timestamps: {
    createdAt: Date,
    updatedAt: Date
  }
}
```

**索引设计**：
```javascript
// 复合索引
{ userId: 1, "reviewItems.scheduledTime": 1 }
{ taskId: 1, "reviewItems.status": 1 }

// 单字段索引
{ scheduleId: 1 }
{ userId: 1 }
```

### [DES-MODEL-003] 思维导图模型
**模型ID**：DES-MODEL-003  
**集合名称**：mindmaps  
**实现需求**：[REQ-FUNC-009], [REQ-FUNC-010]

```javascript
{
  _id: ObjectId,
  mindMapId: String,       // 思维导图唯一标识
  userId: String,          // 用户ID
  title: String,           // 导图标题
  description: String,     // 导图描述
  nodes: [
    {
      nodeId: String,      // 节点唯一标识
      label: String,       // 节点标签
      content: String,     // 节点内容
      position: {
        x: Number,         // X坐标
        y: Number          // Y坐标
      },
      style: {
        color: String,     // 节点颜色
        shape: String,     // 节点形状
        size: Number       // 节点大小
      },
      parentId: String,    // 父节点ID
      level: Number,       // 节点层级
      taskAssociations: [String] // 关联的任务ID数组
    }
  ],
  edges: [
    {
      edgeId: String,      // 连接唯一标识
      source: String,      // 源节点ID
      target: String,      // 目标节点ID
      label: String,       // 连接标签
      style: {
        color: String,     // 连接颜色
        width: Number,     // 连接宽度
        type: String       // 连接类型
      }
    }
  ],
  layout: {
    type: String,          // 布局类型
    settings: Object       // 布局设置
  },
  statistics: {
    nodeCount: Number,     // 节点数量
    edgeCount: Number,     // 连接数量
    maxLevel: Number,      // 最大层级
    associatedTasks: Number // 关联任务数量
  },
  timestamps: {
    createdAt: Date,
    updatedAt: Date,
    lastViewedAt: Date
  }
}
```

### [DES-MODEL-004] 用户模型
**模型ID**：DES-MODEL-004  
**集合名称**：users  
**实现需求**：用户管理相关需求

```javascript
{
  _id: ObjectId,
  userId: String,          // 用户唯一标识
  authentication: {
    username: String,      // 用户名
    email: String,         // 邮箱
    passwordHash: String,  // 密码哈希
    salt: String,          // 密码盐值
    lastLoginAt: Date      // 最后登录时间
  },
  profile: {
    nickname: String,      // 昵称
    avatar: String,        // 头像URL
    grade: String,         // 年级
    school: String,        // 学校
    subjects: [String]     // 关注学科
  },
  preferences: {
    dailyStudyLimit: Number, // 每日学习时间限制（分钟）
    restTimes: [
      {
        start: String,     // 休息开始时间 "22:00"
        end: String        // 休息结束时间 "08:00"
      }
    ],
    notifications: {
      browser: Boolean,    // 浏览器通知
      email: Boolean,      // 邮件通知
      advanceMinutes: Number // 提前提醒时间（分钟）
    },
    theme: String,         // 主题设置
    language: String       // 语言设置
  },
  statistics: {
    totalTasks: Number,    // 总任务数
    completedTasks: Number, // 已完成任务数
    totalStudyTime: Number, // 总学习时间（分钟）
    averageEfficiency: Number, // 平均学习效率
    streakDays: Number,    // 连续学习天数
    subjectStats: [
      {
        subject: String,   // 学科
        efficiency: Number, // 学科效率
        totalTime: Number  // 学科总时间
      }
    ]
  },
  timestamps: {
    createdAt: Date,
    updatedAt: Date,
    lastActiveAt: Date
  }
}
```

### [DES-MODEL-005] 学习记录模型
**模型ID**：DES-MODEL-005  
**集合名称**：learning_records  
**实现需求**：[REQ-FUNC-008] 学习效率分析

```javascript
{
  _id: ObjectId,
  recordId: String,        // 记录唯一标识
  userId: String,          // 用户ID
  taskId: String,          // 任务ID
  type: String,            // 记录类型：study|review
  session: {
    startTime: Date,       // 开始时间
    endTime: Date,         // 结束时间
    duration: Number,      // 持续时间（分钟）
    interruptions: Number, // 中断次数
    effectiveness: Number  // 效果评分（1-5）
  },
  content: {
    subject: String,       // 学科
    difficulty: Number,    // 难度
    contentLength: Number, // 内容长度
    completionRate: Number // 完成率
  },
  environment: {
    device: String,        // 设备类型
    browser: String,       // 浏览器
    timeOfDay: String,     // 时间段：morning|afternoon|evening|night
    dayOfWeek: Number      // 星期几（1-7）
  },
  performance: {
    efficiency: Number,    // 学习效率
    focus: Number,         // 专注度
    retention: Number      // 记忆保持率
  },
  timestamps: {
    createdAt: Date
  }
}
```

## 🔄 数据关系设计

### [DES-MODEL-RELATION-001] 实体关系图
```
User (1) -----> (N) Task
User (1) -----> (N) MindMap
User (1) -----> (N) LearningRecord
Task (1) -----> (1) ReviewSchedule
Task (N) <-----> (N) MindMapNode (通过taskAssociations)
Task (1) -----> (N) LearningRecord
```

### [DES-MODEL-RELATION-002] 数据一致性约束
- 删除用户时，级联删除相关的任务、思维导图和学习记录
- 删除任务时，级联删除相关的复习计划和学习记录
- 思维导图节点的任务关联必须存在对应的任务
- 复习计划的任务ID必须存在对应的任务

## 📊 缓存设计

### [DES-MODEL-CACHE-001] Redis缓存策略
```javascript
// 用户会话缓存
"session:{userId}" -> {
  token: String,
  expiresAt: Number,
  lastActivity: Number
}

// 任务列表缓存
"tasks:{userId}:{filters_hash}" -> {
  tasks: Array,
  totalCount: Number,
  cachedAt: Number
}

// 复习提醒缓存
"reminders:{userId}:{date}" -> {
  reminders: Array,
  cachedAt: Number
}

// 学习统计缓存
"stats:{userId}" -> {
  statistics: Object,
  cachedAt: Number
}
```

**缓存过期策略**：
- 会话缓存：24小时
- 任务列表缓存：5分钟
- 复习提醒缓存：1小时
- 学习统计缓存：30分钟

## 🔗 相关文档

- [API接口设计](./08-API接口设计.md)
- [功能需求规格](../01-需求分析/02-功能需求规格.md)
- [系统架构设计](./01-系统整体架构设计.md)

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：数据库工程师  
**审核人**：系统架构师  
**状态**：草稿
