/**
 * 用户状态管理
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  nickname?: string
  bio?: string
  
  // 学习偏好
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed'
  preferredSubjects: string[]
  studyGoals: string[]
  
  // 时间偏好
  preferredStudyTimes: number[] // 小时数组，如 [9, 10, 14, 15]
  timezone: string
  
  // 统计信息
  totalStudyTime: number
  totalTasks: number
  joinDate: string
  lastActiveDate: string
  
  // 成就和等级
  level: number
  experience: number
  badges: string[]
  
  // 设置
  settings: {
    notifications: boolean
    privacy: 'public' | 'private' | 'friends'
    dataSharing: boolean
  }
}

export interface AuthState {
  isAuthenticated: boolean
  token: string | null
  refreshToken: string | null
  tokenExpiry: string | null
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const authState = ref<AuthState>({
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    tokenExpiry: null
  })
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => authState.value.isAuthenticated && user.value !== null)
  
  const isTokenExpired = computed(() => {
    if (!authState.value.tokenExpiry) {return true}
    return new Date(authState.value.tokenExpiry) <= new Date()
  })
  
  const userLevel = computed(() => {
    if (!user.value) {return 1}
    // 简单的等级计算：每1000经验值升一级
    return Math.floor(user.value.experience / 1000) + 1
  })
  
  const experienceToNextLevel = computed(() => {
    if (!user.value) {return 1000}
    const currentLevel = userLevel.value
    const nextLevelExp = currentLevel * 1000
    return nextLevelExp - user.value.experience
  })
  
  const studyStreak = computed(() => {
    // 这里应该根据学习记录计算连续学习天数
    // 暂时返回模拟数据
    return 7
  })

  // 方法
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  // 认证相关
  const login = async (credentials: { username: string; password: string }) => {
    setLoading(true)
    clearError()
    
    try {
      // 这里应该调用实际的登录API
      // 暂时使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockUser: User = {
        id: 'user-1',
        username: credentials.username,
        email: `${credentials.username}@example.com`,
        nickname: credentials.username,
        learningStyle: 'mixed',
        preferredSubjects: ['数学', '英语'],
        studyGoals: ['提高成绩', '培养兴趣'],
        preferredStudyTimes: [9, 10, 14, 15, 19, 20],
        timezone: 'Asia/Shanghai',
        totalStudyTime: 0,
        totalTasks: 0,
        joinDate: new Date().toISOString(),
        lastActiveDate: new Date().toISOString(),
        level: 1,
        experience: 0,
        badges: [],
        settings: {
          notifications: true,
          privacy: 'private',
          dataSharing: false
        }
      }
      
      const mockAuth: AuthState = {
        isAuthenticated: true,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        tokenExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
      }
      
      user.value = mockUser
      authState.value = mockAuth
      
      return { success: true }
    } catch (_err) {
      setError('登录失败，请检查用户名和密码')
      return { success: false, error: error.value }
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    confirmPassword: string
  }) => {
    setLoading(true)
    clearError()
    
    try {
      // 验证密码
      if (userData.password !== userData.confirmPassword) {
        throw new Error('密码确认不匹配')
      }
      
      // 这里应该调用实际的注册API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newUser: User = {
        id: `user-${Date.now()}`,
        username: userData.username,
        email: userData.email,
        learningStyle: 'mixed',
        preferredSubjects: [],
        studyGoals: [],
        preferredStudyTimes: [9, 10, 14, 15, 19, 20],
        timezone: 'Asia/Shanghai',
        totalStudyTime: 0,
        totalTasks: 0,
        joinDate: new Date().toISOString(),
        lastActiveDate: new Date().toISOString(),
        level: 1,
        experience: 0,
        badges: [],
        settings: {
          notifications: true,
          privacy: 'private',
          dataSharing: false
        }
      }
      
      user.value = newUser
      authState.value = {
        isAuthenticated: true,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        tokenExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }
      
      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    user.value = null
    authState.value = {
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      tokenExpiry: null
    }
    clearError()
  }

  const refreshAuthToken = async () => {
    if (!authState.value.refreshToken) {
      logout()
      return false
    }
    
    try {
      // 这里应该调用刷新token的API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      authState.value.token = 'new-mock-jwt-token'
      authState.value.tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      
      return true
    } catch (_err) {
      logout()
      return false
    }
  }

  // 用户信息更新
  const updateProfile = async (updates: Partial<User>) => {
    if (!user.value) {return { success: false, error: '用户未登录' }}
    
    setLoading(true)
    clearError()
    
    try {
      // 这里应该调用更新用户信息的API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      user.value = { ...user.value, ...updates }
      user.value.lastActiveDate = new Date().toISOString()
      
      return { success: true }
    } catch (_err) {
      setError('更新用户信息失败')
      return { success: false, error: error.value }
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = async (settings: Partial<User['settings']>) => {
    if (!user.value) {return { success: false, error: '用户未登录' }}

    user.value.settings = { ...user.value.settings, ...settings }
    return await updateProfile({ settings: user.value.settings })
  }

  const addExperience = (amount: number) => {
    if (!user.value) {return}
    
    user.value.experience += amount
    user.value.level = userLevel.value
  }

  const addBadge = (badgeId: string) => {
    if (!user.value || user.value.badges.includes(badgeId)) {return}
    
    user.value.badges.push(badgeId)
  }

  const updateStudyStats = (studyTime: number, tasksCompleted: number) => {
    if (!user.value) {return}
    
    user.value.totalStudyTime += studyTime
    user.value.totalTasks += tasksCompleted
    user.value.lastActiveDate = new Date().toISOString()
  }

  // 初始化
  const init = async () => {
    // 检查是否有保存的认证信息
    if (authState.value.isAuthenticated && authState.value.token) {
      // 检查token是否过期
      if (isTokenExpired.value) {
        const refreshed = await refreshAuthToken()
        if (!refreshed) {
          logout()
        }
      }
    }
  }

  // 重置状态
  const reset = () => {
    user.value = null
    authState.value = {
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      tokenExpiry: null
    }
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    user,
    authState,
    loading,
    error,
    
    // 计算属性
    isLoggedIn,
    isTokenExpired,
    userLevel,
    experienceToNextLevel,
    studyStreak,
    
    // 方法
    setLoading,
    setError,
    clearError,
    login,
    register,
    logout,
    refreshAuthToken,
    updateProfile,
    updateSettings,
    addExperience,
    addBadge,
    updateStudyStats,
    init,
    reset
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['user', 'authState']
  }
})
