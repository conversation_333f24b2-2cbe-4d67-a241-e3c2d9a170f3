{"version": "2.0.0", "tasks": [{"label": "npm: dev", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^\\s*Local:", "endsPattern": "ready in"}}}, {"label": "npm: build", "type": "npm", "script": "build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc", "$eslint-stylish"]}, {"label": "npm: preview", "type": "npm", "script": "preview", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "dependsOn": "npm: build"}, {"label": "npm: lint", "type": "npm", "script": "lint", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": "$eslint-stylish"}, {"label": "npm: format", "type": "npm", "script": "format", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "npm: type-check", "type": "npm", "script": "type-check", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": "$tsc"}, {"label": "npm: test", "type": "npm", "script": "test", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "npm: test:ui", "type": "npm", "script": "test:ui", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true}, {"label": "npm: test:coverage", "type": "npm", "script": "test:coverage", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "npm: test:e2e", "type": "npm", "script": "test:e2e", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "npm: test:e2e:ui", "type": "npm", "script": "test:e2e:ui", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true}, {"label": "Clean Install", "type": "shell", "command": "rm -rf node_modules package-lock.json && npm install", "windows": {"command": "rmdir /s /q node_modules & del package-lock.json & npm install"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "Update Dependencies", "type": "shell", "command": "npm update", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "Check Outdated", "type": "shell", "command": "npm outdated", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "Security Audit", "type": "shell", "command": "npm audit", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "Fix Security Issues", "type": "shell", "command": "npm audit fix", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "Full Check", "dependsOrder": "sequence", "dependsOn": ["npm: lint", "npm: type-check", "npm: test:coverage", "npm: build"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}]}