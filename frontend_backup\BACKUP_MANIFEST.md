# Frontend 项目备份清单

## 📅 备份信息
- **备份时间**: 2025-08-02 14:15
- **原项目路径**: `E:\JYZS\frontend`
- **备份路径**: `E:\JYZS\frontend_backup`
- **备份类型**: 业务代码全量备份

## 📁 已备份的业务模块

### 1. 核心源代码 (`src/`)
- ✅ **App.vue** - 应用根组件
- ✅ **main.ts** - 应用入口文件
- ✅ **components/** - 所有UI组件
  - common/ - 通用组件
  - layout/ - 布局组件
  - mindmap/ - 思维导图组件
  - review/ - 复习相关组件
  - task/ - 任务相关组件
- ✅ **views/** - 页面组件
  - Dashboard.vue - 仪表板
  - Tasks.vue, TaskCreate.vue, TaskDetail.vue - 任务管理
  - Review.vue - 复习功能
  - MindMap.vue - 思维导图
  - EbbinghausTest.vue - 艾宾浩斯测试
- ✅ **stores/** - Pinia状态管理
  - app.ts - 应用状态
  - user.ts - 用户状态
  - task.ts - 任务状态
  - review.ts - 复习状态
  - learning.ts - 学习状态
  - notification.ts - 通知状态
  - settings.ts - 设置状态
- ✅ **services/** - 业务逻辑服务
  - EbbinghausAlgorithm.ts - 艾宾浩斯算法
  - NotificationService.ts - 通知服务
  - TimeEstimationService.ts - 时间估算服务
- ✅ **utils/** - 工具函数
  - errorHandler.ts - 错误处理
  - httpClient.ts - HTTP客户端
  - performance.ts - 性能监控
  - validation.ts - 表单验证
- ✅ **types/** - TypeScript类型定义
  - index.ts - 核心类型
  - mindmap.ts - 思维导图类型
- ✅ **styles/** - 样式文件
  - main.scss - 主样式
  - variables.scss - 变量
  - mixins.scss - 混入
  - mobile.scss - 移动端样式
  - tailwind.css - Tailwind CSS
- ✅ **mock/** - 模拟数据
  - reviewData.ts - 复习数据
  - taskData.ts - 任务数据
- ✅ **router/** - 路由配置
  - index.ts - 路由定义
- ✅ **test/** - 测试工具
  - setup.ts - 测试设置
  - test-utils.ts - 测试工具

### 2. 测试代码 (`e2e/`)
- ✅ **dashboard.spec.ts** - 仪表板E2E测试
- ✅ **global-setup.ts** - 全局测试设置
- ✅ **global-teardown.ts** - 全局测试清理

### 3. 文档 (`docs/`)
- ✅ **DEVELOPMENT.md** - 开发文档

### 4. 项目说明
- ✅ **README.md** - 项目说明文档

## 🚫 未备份的非业务文件

### 依赖和构建文件
- ❌ **node_modules/** - 依赖包（保留在原位置）
- ❌ **dist/** - 构建输出（保留在原位置）
- ❌ **package-lock.json** - 依赖锁定文件（保留在原位置）

### 配置文件
- ❌ **package.json** - 项目配置（保留在原位置）
- ❌ **vite.config.ts** - Vite配置（保留在原位置）
- ❌ **vitest.config.ts** - Vitest配置（保留在原位置）
- ❌ **playwright.config.ts** - Playwright配置（保留在原位置）
- ❌ **tsconfig.*.json** - TypeScript配置（保留在原位置）
- ❌ **eslint.config.js** - ESLint配置（保留在原位置）
- ❌ **tailwind.config.js** - Tailwind配置（保留在原位置）
- ❌ **postcss.config.js** - PostCSS配置（保留在原位置）
- ❌ **env.d.ts** - 环境类型定义（保留在原位置）
- ❌ **index.html** - HTML模板（保留在原位置）

### 开发工具配置
- ❌ **.vscode/** - VSCode配置（保留在原位置）

## 📊 备份统计
- **总文件数**: 约 50+ 个业务文件
- **总大小**: 约 2MB（不含依赖）
- **模块数**: 35个核心模块
- **依赖关系**: 42条

## 🎯 重构计划概览

### 技术栈保持
- Vue 3 + TypeScript + Vite
- Element Plus UI 框架
- Pinia 状态管理
- Vue Router 路由
- Tailwind CSS + SCSS

### 重构优先级
1. **核心服务模块** - EbbinghausAlgorithm, NotificationService, TimeEstimationService
2. **状态管理** - 优化 Pinia stores 结构
3. **组件模块** - 提升组件质量和可维护性
4. **页面模块** - 重构页面组件
5. **工具函数** - 优化工具函数
6. **测试完善** - 编写完整测试

## 📝 备份验证
- ✅ 所有业务代码已成功备份
- ✅ 目录结构完整保留
- ✅ 文件内容完整无损
- ✅ 非业务文件保留在原位置
- ✅ 可以开始安全重构

---
**备份完成时间**: 2025-08-02 14:15  
**下一步**: 开始制定详细重构计划
