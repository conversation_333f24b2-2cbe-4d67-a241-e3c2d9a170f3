<template>
  <div class="time-estimation">
    <ElCard class="estimation-card">
      <template #header>
        <div class="card-header">
          <ElIcon><Timer /></ElIcon>
          <span>智能时间预估</span>
          <ElTag v-if="estimation" :type="getConfidenceTagType(estimation.confidence)" size="small">
            置信度 {{ Math.round(estimation.confidence * 100) }}%
          </ElTag>
        </div>
      </template>
      
      <div v-if="loading" class="loading-state">
        <ElSkeleton :rows="3" animated />
      </div>
      
      <div v-else-if="estimation" class="estimation-content">
        <!-- 主要预估结果 -->
        <div class="main-estimation">
          <div class="time-display">
            <span class="time-value">{{ formatTime(estimation.estimatedTime) }}</span>
            <span class="time-label">预估用时</span>
          </div>
          
          <div class="confidence-indicator">
            <ElProgress
              :percentage="Math.round(estimation.confidence * 100)"
              :color="getConfidenceColor(estimation.confidence)"
              :stroke-width="8"
              :show-text="false"
            />
            <span class="confidence-text">
              {{ getConfidenceText(estimation.confidence) }}
            </span>
          </div>
        </div>
        
        <!-- 不同情况预估 -->
        <div class="alternative-estimates">
          <h4>不同情况预估</h4>
          <div class="estimates-grid">
            <div class="estimate-item optimistic">
              <div class="estimate-label">乐观预估</div>
              <div class="estimate-value">{{ formatTime(estimation.alternativeEstimates.optimistic) }}</div>
              <div class="estimate-desc">状态良好时</div>
            </div>
            <div class="estimate-item realistic">
              <div class="estimate-label">现实预估</div>
              <div class="estimate-value">{{ formatTime(estimation.alternativeEstimates.realistic) }}</div>
              <div class="estimate-desc">正常情况下</div>
            </div>
            <div class="estimate-item pessimistic">
              <div class="estimate-label">保守预估</div>
              <div class="estimate-value">{{ formatTime(estimation.alternativeEstimates.pessimistic) }}</div>
              <div class="estimate-desc">遇到困难时</div>
            </div>
          </div>
        </div>
        
        <!-- 影响因素分析 -->
        <div v-if="showDetails" class="factors-analysis">
          <h4>影响因素分析</h4>
          <div class="factors-list">
            <div class="factor-item">
              <span class="factor-label">基础时间</span>
              <span class="factor-value">{{ formatTime(estimation.factors.baseTime) }}</span>
            </div>
            <div class="factor-item">
              <span class="factor-label">难度调整</span>
              <span class="factor-value">×{{ estimation.factors.difficultyFactor.toFixed(2) }}</span>
            </div>
            <div class="factor-item">
              <span class="factor-label">学科调整</span>
              <span class="factor-value">×{{ estimation.factors.subjectFactor.toFixed(2) }}</span>
            </div>
            <div class="factor-item">
              <span class="factor-label">个人效率</span>
              <span class="factor-value">×{{ estimation.factors.userFactor.toFixed(2) }}</span>
            </div>
            <div class="factor-item">
              <span class="factor-label">时间段</span>
              <span class="factor-value">×{{ estimation.factors.timeFactor.toFixed(2) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 优化建议 -->
        <div v-if="estimation.suggestions.length > 0" class="suggestions">
          <h4>优化建议</h4>
          <ul class="suggestions-list">
            <li v-for="(suggestion, index) in estimation.suggestions" :key="index">
              <ElIcon><InfoFilled /></ElIcon>
              <span>{{ suggestion }}</span>
            </li>
          </ul>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
          <ElButton
            size="small"
            @click="showDetails = !showDetails"
          >
            {{ showDetails ? '隐藏详情' : '查看详情' }}
          </ElButton>
          <ElButton
            size="small"
            type="primary"
            @click="acceptEstimation"
          >
            采用预估
          </ElButton>
          <ElButton
            size="small"
            :loading="loading"
            @click="refreshEstimation"
          >
            重新预估
          </ElButton>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <ElEmpty description="暂无预估数据">
          <ElButton type="primary" @click="refreshEstimation">
            开始预估
          </ElButton>
        </ElEmpty>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElButton, ElCard, ElEmpty, ElIcon, ElMessage, ElProgress, ElSkeleton, ElTag } from 'element-plus'
import { InfoFilled, Timer } from '@element-plus/icons-vue'
import TimeEstimationService, { type TaskFeatures, type TimeEstimationResult } from '@/services/TimeEstimationService'

interface Props {
  taskFeatures: TaskFeatures | null
  autoEstimate?: boolean
}

interface Emits {
  (_e: 'estimation-ready', _result: TimeEstimationResult): void
  (_e: 'estimation-accepted', _result: TimeEstimationResult): void
}

const props = withDefaults(defineProps<Props>(), {
  autoEstimate: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const estimation = ref<TimeEstimationResult | null>(null)
const showDetails = ref(false)

// 服务实例
const estimationService = TimeEstimationService.getInstance()

// 计算属性
const hasValidFeatures = computed(() => {
  return props.taskFeatures && 
         props.taskFeatures.subject && 
         props.taskFeatures.difficulty > 0 &&
         props.taskFeatures.estimatedTime > 0
})

// 方法
const getConfidenceTagType = (confidence: number) => {
  if (confidence >= 0.8) {return 'success'}
  if (confidence >= 0.6) {return 'warning'}
  return 'danger'
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) {return '#67c23a'}
  if (confidence >= 0.6) {return '#e6a23c'}
  return '#f56c6c'
}

const getConfidenceText = (confidence: number) => {
  if (confidence >= 0.8) {return '高置信度'}
  if (confidence >= 0.6) {return '中等置信度'}
  return '低置信度'
}

const formatTime = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`
}

const refreshEstimation = async () => {
  if (!hasValidFeatures.value) {
    ElMessage.warning('任务特征信息不完整，无法进行预估')
    return
  }

  loading.value = true
  
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    if (!props.taskFeatures) {
      throw new Error('任务特征信息缺失')
    }
    const result = estimationService.estimateTaskTime(props.taskFeatures)
    estimation.value = result
    
    emit('estimation-ready', result)
    ElMessage.success('时间预估完成')
  } catch (error) {
    ElMessage.error('预估失败，请重试')
    console.error('Time estimation error:', error)
  } finally {
    loading.value = false
  }
}

const acceptEstimation = () => {
  if (estimation.value) {
    emit('estimation-accepted', estimation.value)
    ElMessage.success('已采用智能预估时间')
  }
}

// 生命周期
onMounted(() => {
  if (props.autoEstimate && hasValidFeatures.value) {
    refreshEstimation()
  }
})

// 监听任务特征变化
watch(() => props.taskFeatures, (newFeatures) => {
  if (newFeatures && props.autoEstimate) {
    refreshEstimation()
  }
}, { deep: true })
</script>

<style scoped lang="scss">
.time-estimation {
  .estimation-card {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.estimation-content {
  .main-estimation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    
    .time-display {
      text-align: center;
      
      .time-value {
        display: block;
        font-size: 32px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 4px;
      }
      
      .time-label {
        font-size: 14px;
        color: #666;
      }
    }
    
    .confidence-indicator {
      text-align: center;
      min-width: 120px;
      
      .confidence-text {
        display: block;
        margin-top: 8px;
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .alternative-estimates {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .estimates-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
    }
    
    .estimate-item {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      
      &.optimistic {
        background: #f0f9ff;
        border-color: #409eff;
      }
      
      &.realistic {
        background: #f5f7fa;
        border-color: #909399;
      }
      
      &.pessimistic {
        background: #fef0f0;
        border-color: #f56c6c;
      }
      
      .estimate-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .estimate-value {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .estimate-desc {
        font-size: 11px;
        color: #999;
      }
    }
  }
  
  .factors-analysis {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .factors-list {
      .factor-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .factor-label {
          color: #606266;
          font-size: 14px;
        }
        
        .factor-value {
          color: #303133;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }
  }
  
  .suggestions {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .suggestions-list {
      margin: 0;
      padding: 0;
      list-style: none;
      
      li {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 8px;
        padding: 8px;
        background: #fff7e6;
        border-radius: 6px;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        
        .el-icon {
          color: #e6a23c;
          margin-top: 2px;
          flex-shrink: 0;
        }
      }
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

.loading-state {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

// 移动端适配
@media (max-width: 768px) {
  .estimation-content {
    .main-estimation {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
    
    .alternative-estimates {
      .estimates-grid {
        grid-template-columns: 1fr;
        gap: 8px;
      }
    }
    
    .actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
