<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'
  import { RouterView } from 'vue-router'
  import AppLayout from '@/components/layout/AppLayout.vue'
  import MobileLayout from '@/components/layout/MobileLayout.vue'
  import InAppNotification from '@/components/common/InAppNotification.vue'
  import ErrorBoundary from '@/components/common/ErrorBoundary.vue'
  import NetworkStatus from '@/components/common/NetworkStatus.vue'
  import { useAppStore } from '@/stores/app'

  // Stores
  const appStore = useAppStore()
  // const userStore = useUserStore()
  // const notificationStore = useNotificationStore()
  // const settingsStore = useSettingsStore()
  // const learningStore = useLearningStore()

  // 响应式数据
  const isMobile = ref(false)

  // 检测是否为移动端
  const checkMobile = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
    const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
    const isSmallScreen = window.innerWidth <= 768

    isMobile.value = isMobileDevice || isSmallScreen
  }

  // 监听窗口大小变化
  const handleResize = () => {
    checkMobile()
  }

  onMounted(() => {
    checkMobile()
    window.addEventListener('resize', handleResize)

    // 初始化stores
    try {
      // 初始化应用store
      appStore.init()

      console.log('AppStore初始化完成')
    } catch (error) {
      console.error('Stores初始化失败:', error)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
</script>

<template>
  <div class="app-container">
    <ErrorBoundary>
      <!-- 移动端布局 -->
      <MobileLayout v-if="isMobile">
        <ErrorBoundary>
          <RouterView />
        </ErrorBoundary>
      </MobileLayout>

      <!-- 桌面端布局 -->
      <AppLayout v-else>
        <ErrorBoundary>
          <RouterView />
        </ErrorBoundary>
      </AppLayout>

      <!-- 应用内通知组件 -->
      <InAppNotification />

      <!-- 网络状态监控 -->
      <NetworkStatus />
    </ErrorBoundary>
  </div>
</template>

<style scoped>
  .app-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }

  /* 确保移动端布局占满全屏 */
  @media (max-width: 768px) {
    .app-container {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
</style>
