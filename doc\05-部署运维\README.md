# 部署运维文档

## 📋 概述

本目录包含艾宾浩斯记忆曲线学习管理系统的部署运维相关文档，为项目的生产部署、环境配置和运维管理提供完整指导。

## 📚 文档结构

### 部署相关文档
- **[部署指南](./03-部署指南.md)** - 生产环境部署完整指南
- **[环境配置](./04-环境配置.md)** - 各环境的配置说明和要求

### 测试相关文档（已移至04-测试验证）
- 测试计划和测试用例已移动到 [04-测试验证](../04-测试验证/README.md) 文件夹

## 🎯 文档使用指南

### 对于运维工程师
1. 参考 [环境配置](./04-环境配置.md) 准备部署环境
2. 按照 [部署指南](./03-部署指南.md) 执行部署操作
3. 配置监控和日志系统

### 对于开发工程师
1. 参考部署文档了解环境要求
2. 配置本地开发环境
3. 了解生产环境部署流程

### 对于测试工程师
1. 测试相关文档已移至 [04-测试验证](../04-测试验证/README.md)
2. 参考测试验证文档进行测试工作

## 🔧 快速开始

### 开发环境搭建
```bash
# 1. 克隆项目
git clone <repository-url>
cd ebbinghaus-learning-system

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.development

# 4. 启动开发服务器
npm run dev
```

### 测试环境部署
```bash
# 1. 构建测试版本
npm run build:test

# 2. 启动测试服务
npm run start:test

# 3. 运行测试套件
npm run test
```

### 生产环境部署
```bash
# 使用Docker部署（推荐）
docker-compose up -d

# 或传统部署方式
npm run build:prod
pm2 start ecosystem.config.js --env production
```

## 📊 测试覆盖范围

### P0核心功能（必须100%通过）
- ✅ 学习任务创建功能
- ✅ 艾宾浩斯复习计划生成
- ✅ 负载均衡检查功能
- ✅ 复习提醒功能
- ✅ 任务列表管理功能
- ✅ 复习执行功能

### P1重要功能（必须90%通过）
- ✅ 智能时间预估功能
- ✅ 学习效率分析功能

### P2有用功能（必须80%通过）
- ✅ 思维导图创建功能
- ✅ 思维导图任务关联功能

## 🚀 部署环境

### 开发环境
- **用途**：本地开发和调试
- **配置**：简化配置，详细日志
- **数据库**：本地MongoDB/MySQL

### 测试环境
- **用途**：功能测试和集成测试
- **配置**：模拟生产环境
- **数据库**：独立测试数据库

### 生产环境
- **用途**：正式运行环境
- **配置**：高可用、高性能
- **数据库**：生产数据库集群

## 📋 质量标准

### 功能质量标准
- 功能完整性：100%
- 业务逻辑正确性：100%
- 用户体验满意度：>4.0/5.0

### 性能质量标准
- 页面加载时间：<2秒
- API响应时间：<500ms
- 系统可用性：>99.5%

### 安全质量标准
- 数据安全：100%
- 访问控制：100%
- 输入验证：100%

## 🔗 相关文档

### 需求和设计文档
- [项目背景与目标](../01-需求分析/01-项目背景与目标.md)
- [功能需求规格](../01-需求分析/02-功能需求规格.md)
- [系统架构设计](../02-系统设计/01-系统整体架构设计.md)

### 基础文档
- [文档规范](../00-文档规范.md)
- [术语表](../00-术语表.md)

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2025-01-31 | 初始测试部署文档创建 | 测试工程师 |

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**维护团队**：测试部署团队  
**适用范围**：测试和部署相关工作
