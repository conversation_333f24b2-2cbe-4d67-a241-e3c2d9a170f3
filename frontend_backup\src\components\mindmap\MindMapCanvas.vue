<template>
  <div ref="canvasContainer" class="mindmap-canvas">
    <svg
      ref="svgElement"
      class="mindmap-svg"
      :width="canvasWidth"
      :height="canvasHeight"
      @click="handleCanvasClick"
      @contextmenu="handleCanvasContextMenu"
    >
      <!-- 网格背景 -->
      <defs>
        <pattern
          id="grid"
          width="20"
          height="20"
          patternUnits="userSpaceOnUse"
        >
          <path
            d="M 20 0 L 0 0 0 20"
            fill="none"
            stroke="#f0f0f0"
            stroke-width="1"
          />
        </pattern>
      </defs>
      <rect
        v-if="showGrid"
        width="100%"
        height="100%"
        fill="url(#grid)"
      />

      <!-- 连接线 -->
      <g class="edges-group">
        <path
          v-for="edge in computedEdges"
          :key="edge.id"
          :d="edge.path"
          :stroke="edge.color || '#999'"
          :stroke-width="edge.width || 2"
          :stroke-dasharray="getStrokeDashArray(edge.style)"
          fill="none"
          class="mindmap-edge"
          @click="handleEdgeClick(edge, $event)"
        />
      </g>

      <!-- 节点 -->
      <g class="nodes-group">
        <g
          v-for="node in mindmap.nodes"
          :key="node.id"
          :transform="`translate(${node.x}, ${node.y})`"
          class="mindmap-node"
          :class="{ selected: selectedNode?.id === node.id }"
          @click="handleNodeClick(node, $event)"
          @dblclick="handleNodeDoubleClick(node, $event)"
          @contextmenu="handleNodeContextMenu(node, $event)"
          @mousedown="handleNodeMouseDown(node, $event)"
        >
          <!-- 节点形状 -->
          <rect
            v-if="node.shape === 'rectangle' || !node.shape"
            :width="node.width || 120"
            :height="node.height || 40"
            :x="-(node.width || 120) / 2"
            :y="-(node.height || 40) / 2"
            :fill="node.backgroundColor || node.color"
            :stroke="node.borderColor || '#ccc'"
            :stroke-width="node.borderWidth || 1"
            :rx="4"
            class="node-shape"
          />
          
          <circle
            v-else-if="node.shape === 'circle'"
            :r="(node.width || 80) / 2"
            :fill="node.backgroundColor || node.color"
            :stroke="node.borderColor || '#ccc'"
            :stroke-width="node.borderWidth || 1"
            class="node-shape"
          />
          
          <ellipse
            v-else-if="node.shape === 'ellipse'"
            :rx="(node.width || 120) / 2"
            :ry="(node.height || 60) / 2"
            :fill="node.backgroundColor || node.color"
            :stroke="node.borderColor || '#ccc'"
            :stroke-width="node.borderWidth || 1"
            class="node-shape"
          />

          <!-- 节点图标 -->
          <text
            v-if="node.icon"
            :x="-(node.width || 120) / 2 + 10"
            :y="0"
            :font-size="16"
            dominant-baseline="middle"
            class="node-icon"
          >
            {{ getIconText(node.icon) }}
          </text>

          <!-- 节点文本 -->
          <text
            :x="node.icon ? -(node.width || 120) / 2 + 30 : 0"
            :y="0"
            :font-size="node.fontSize || 14"
            :font-weight="node.fontWeight || 'normal'"
            :fill="getTextColor(node.backgroundColor || node.color)"
            text-anchor="middle"
            dominant-baseline="middle"
            class="node-text"
          >
            {{ node.text }}
          </text>

          <!-- 展开/折叠按钮 -->
          <circle
            v-if="hasChildren(node)"
            :cx="(node.width || 120) / 2 + 10"
            :cy="0"
            r="8"
            fill="#409EFF"
            stroke="white"
            stroke-width="2"
            class="expand-button"
            @click="toggleNodeCollapse(node, $event)"
          />
          <text
            v-if="hasChildren(node)"
            :x="(node.width || 120) / 2 + 10"
            :y="0"
            font-size="10"
            fill="white"
            text-anchor="middle"
            dominant-baseline="middle"
            class="expand-icon"
            @click="toggleNodeCollapse(node, $event)"
          >
            {{ node.collapsed ? '+' : '-' }}
          </text>

          <!-- 任务状态指示器 -->
          <g v-if="node.taskId" class="task-status-group">
            <!-- 任务状态徽章 -->
            <rect
              :x="-(node.width || 120) / 2"
              :y="-(node.height || 40) / 2 - 15"
              width="60"
              height="12"
              rx="6"
              :fill="getTaskStatusColor(node.taskStatus)"
              class="task-status-badge"
            />
            <text
              :x="-(node.width || 120) / 2 + 30"
              :y="-(node.height || 40) / 2 - 9"
              font-size="8"
              fill="white"
              text-anchor="middle"
              dominant-baseline="middle"
              class="task-status-text"
            >
              {{ getTaskStatusText(node.taskStatus) }}
            </text>

            <!-- 进度环 -->
            <circle
              v-if="node.taskProgress !== undefined"
              :cx="(node.width || 120) / 2 - 15"
              :cy="-(node.height || 40) / 2 + 15"
              r="8"
              fill="none"
              stroke="#e4e7ed"
              stroke-width="2"
              class="progress-background"
            />
            <circle
              v-if="node.taskProgress !== undefined"
              :cx="(node.width || 120) / 2 - 15"
              :cy="-(node.height || 40) / 2 + 15"
              r="8"
              fill="none"
              :stroke="getProgressColor(node.taskProgress)"
              stroke-width="2"
              :stroke-dasharray="getProgressDashArray(node.taskProgress)"
              stroke-linecap="round"
              transform="rotate(-90)"
              :transform-origin="`${(node.width || 120) / 2 - 15} ${-(node.height || 40) / 2 + 15}`"
              class="progress-foreground"
            />
            <text
              v-if="node.taskProgress !== undefined"
              :x="(node.width || 120) / 2 - 15"
              :y="-(node.height || 40) / 2 + 15"
              font-size="6"
              :fill="getProgressColor(node.taskProgress)"
              text-anchor="middle"
              dominant-baseline="middle"
              class="progress-text"
            >
              {{ Math.round(node.taskProgress) }}%
            </text>

            <!-- 优先级指示器 -->
            <circle
              v-if="node.taskPriority && node.taskPriority > 3"
              :cx="(node.width || 120) / 2 - 5"
              :cy="-(node.height || 40) / 2 + 5"
              r="4"
              fill="#f56c6c"
              class="priority-indicator"
            />
            <text
              v-if="node.taskPriority && node.taskPriority > 3"
              :x="(node.width || 120) / 2 - 5"
              :y="-(node.height || 40) / 2 + 5"
              font-size="6"
              fill="white"
              text-anchor="middle"
              dominant-baseline="middle"
              class="priority-text"
            >
              !
            </text>

            <!-- 复习提醒 -->
            <rect
              v-if="node.reviewStatus === 'overdue'"
              :x="-(node.width || 120) / 2 - 5"
              :y="(node.height || 40) / 2 + 2"
              width="40"
              height="10"
              rx="5"
              fill="#f56c6c"
              class="review-reminder"
            />
            <text
              v-if="node.reviewStatus === 'overdue'"
              :x="-(node.width || 120) / 2 + 15"
              :y="(node.height || 40) / 2 + 7"
              font-size="6"
              fill="white"
              text-anchor="middle"
              dominant-baseline="middle"
              class="review-text"
            >
              逾期
            </text>
          </g>
        </g>
      </g>

      <!-- 选择框 -->
      <rect
        v-if="selectionBox.visible"
        :x="selectionBox.x"
        :y="selectionBox.y"
        :width="selectionBox.width"
        :height="selectionBox.height"
        fill="rgba(64, 158, 255, 0.1)"
        stroke="#409EFF"
        stroke-width="1"
        stroke-dasharray="5,5"
        class="selection-box"
      />
    </svg>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{
        left: `${contextMenu.x}px`,
        top: `${contextMenu.y}px`
      }"
    >
      <div
        v-for="item in contextMenu.items"
        :key="item.key"
        class="context-menu-item"
        :class="{ disabled: item.disabled }"
        @click="handleContextMenuClick(item)"
      >
        <el-icon v-if="item.icon">
          <component :is="item.icon" />
        </el-icon>
        <span>{{ item.label }}</span>
      </div>
    </div>

    <!-- 缩放控制 -->
    <div class="zoom-controls">
      <el-button-group>
        <el-button size="small" :icon="ZoomIn" @click="zoomIn" />
        <el-button size="small" @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
        <el-button size="small" :icon="ZoomOut" @click="zoomOut" />
      </el-button-group>
    </div>

    <!-- 小地图 -->
    <div v-if="showMinimap" class="minimap">
      <svg
        class="minimap-svg"
        :width="minimapWidth"
        :height="minimapHeight"
        @click="handleMinimapClick"
      >
        <rect
          width="100%"
          height="100%"
          fill="rgba(255, 255, 255, 0.9)"
          stroke="#ccc"
          stroke-width="1"
        />
        <!-- 小地图节点 -->
        <circle
          v-for="node in mindmap.nodes"
          :key="`mini-${node.id}`"
          :cx="node.x * minimapScale"
          :cy="node.y * minimapScale"
          :r="2"
          :fill="node.color"
        />
        <!-- 视口框 -->
        <rect
          :x="viewportBox.x"
          :y="viewportBox.y"
          :width="viewportBox.width"
          :height="viewportBox.height"
          fill="rgba(64, 158, 255, 0.2)"
          stroke="#409EFF"
          stroke-width="1"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
  import { CopyDocument, Delete, Edit, Plus, ZoomIn, ZoomOut } from '@element-plus/icons-vue'
  import type { MindMap, MindMapNode } from '@/types/mindmap'

  interface Props {
    mindmap: MindMap
    layout: 'tree' | 'radial' | 'force' | 'hierarchical'
    selectedNode: MindMapNode | null
    showGrid?: boolean
    showMinimap?: boolean
  }

  interface Emits {
    (e: 'select-node', node: MindMapNode): void
    (e: 'update-node', node: MindMapNode): void
    (e: 'add-node', parentNode: MindMapNode): void
    (e: 'delete-node', node: MindMapNode): void
    (e: 'canvas-click', event: MouseEvent): void
  }

  const props = withDefaults(defineProps<Props>(), {
    showGrid: true,
    showMinimap: true
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const canvasContainer = ref<HTMLElement>()
  const svgElement = ref<SVGElement>()
  const canvasWidth = ref(1200)
  const canvasHeight = ref(800)
  const scale = ref(1)
  const panX = ref(0)
  const panY = ref(0)

  // 交互状态
  const isDragging = ref(false)
  const dragNode = ref<MindMapNode | null>(null)
  const dragStartPos = ref({ x: 0, y: 0 })
  const isPanning = ref(false)

  // 选择框
  const selectionBox = ref({
    visible: false,
    x: 0,
    y: 0,
    width: 0,
    height: 0
  })

  // 右键菜单
  const contextMenu = ref({
    visible: false,
    x: 0,
    y: 0,
    items: [] as Array<{
      key: string
      label: string
      icon?: any
      disabled?: boolean
    }>
  })

  // 小地图
  const minimapWidth = 150
  const minimapHeight = 100
  const minimapScale = computed(() => {
    const bounds = getNodesBounds()
    const scaleX = minimapWidth / (bounds.width + 100)
    const scaleY = minimapHeight / (bounds.height + 100)
    return Math.min(scaleX, scaleY, 0.1)
  })

  const viewportBox = computed(() => {
    return {
      x: -panX.value * minimapScale.value,
      y: -panY.value * minimapScale.value,
      width: canvasWidth.value * minimapScale.value / scale.value,
      height: canvasHeight.value * minimapScale.value / scale.value
    }
  })

  // 计算连接线
  const computedEdges = computed(() => {
    return props.mindmap.nodes.map(node => {
      if (!node.parentId) {return null}
      
      const parent = props.mindmap.nodes.find(n => n.id === node.parentId)
      if (!parent || node.collapsed) {return null}

      const path = calculateEdgePath(parent, node)
      return {
        id: `${parent.id}-${node.id}`,
        path,
        color: '#999',
        width: 2,
        style: 'solid' as const
      }
    }).filter(Boolean) as Array<{ id: string; path: string; color: string; width: number; style: 'solid' }>
  })

  // 方法
  const getNodesBounds = () => {
    if (props.mindmap.nodes.length === 0) {
      return { x: 0, y: 0, width: 100, height: 100 }
    }

    const xs = props.mindmap.nodes.map(n => n.x)
    const ys = props.mindmap.nodes.map(n => n.y)
    
    return {
      x: Math.min(...xs),
      y: Math.min(...ys),
      width: Math.max(...xs) - Math.min(...xs),
      height: Math.max(...ys) - Math.min(...ys)
    }
  }

  const calculateEdgePath = (parent: MindMapNode, child: MindMapNode): string => {
    const parentX = parent.x
    const parentY = parent.y
    const childX = child.x
    const childY = child.y

    // 简单的直线连接
    return `M ${parentX} ${parentY} L ${childX} ${childY}`
  }

  const getStrokeDashArray = (style?: string): string => {
    switch (style) {
      case 'dashed': return '5,5'
      case 'dotted': return '2,2'
      default: return ''
    }
  }

  const getIconText = (icon: string): string => {
    const iconMap: Record<string, string> = {
      document: '📄',
      folder: '📁',
      star: '⭐',
      lightbulb: '💡'
    }
    return iconMap[icon] || '●'
  }

  const getTextColor = (backgroundColor: string): string => {
    // 简单的颜色对比度计算
    const hex = backgroundColor.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000
    return brightness > 128 ? '#000' : '#fff'
  }

  const hasChildren = (node: MindMapNode): boolean => {
    return props.mindmap.nodes.some(n => n.parentId === node.id)
  }

  const handleCanvasClick = (event: MouseEvent) => {
    if (event.target === svgElement.value) {
      emit('canvas-click', event)
      hideContextMenu()
    }
  }

  const handleCanvasContextMenu = (event: MouseEvent) => {
    event.preventDefault()
    showContextMenu(event, [
      { key: 'add', label: '添加节点', icon: Plus },
      { key: 'paste', label: '粘贴', icon: CopyDocument, disabled: true }
    ])
  }

  const handleNodeClick = (node: MindMapNode, event: MouseEvent) => {
    event.stopPropagation()
    emit('select-node', node)
    hideContextMenu()
  }

  const handleNodeDoubleClick = (_node: MindMapNode, event: MouseEvent) => {
    event.stopPropagation()
    // 触发编辑
  }

  const handleNodeContextMenu = (node: MindMapNode, event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    emit('select-node', node)
    
    showContextMenu(event, [
      { key: 'edit', label: '编辑', icon: Edit },
      { key: 'add-child', label: '添加子节点', icon: Plus },
      { key: 'copy', label: '复制', icon: CopyDocument },
      { key: 'delete', label: '删除', icon: Delete }
    ])
  }

  const handleNodeMouseDown = (node: MindMapNode, event: MouseEvent) => {
    if (event.button === 0) { // 左键
      isDragging.value = true
      dragNode.value = node
      dragStartPos.value = { x: event.clientX, y: event.clientY }
    }
  }

  const handleEdgeClick = (_edge: any, event: MouseEvent) => {
    event.stopPropagation()
    // 处理连线点击
  }

  const toggleNodeCollapse = (node: MindMapNode, event: MouseEvent) => {
    event.stopPropagation()
    node.collapsed = !node.collapsed
    emit('update-node', node)
  }

  const showContextMenu = (event: MouseEvent, items: any[]) => {
    contextMenu.value = {
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items
    }
  }

  const hideContextMenu = () => {
    contextMenu.value.visible = false
  }

  const handleContextMenuClick = (item: any) => {
    if (item.disabled) {return}
    
    switch (item.key) {
      case 'add':
        // 添加节点逻辑
        break
      case 'edit':
        // 编辑节点逻辑
        break
      case 'add-child':
        if (props.selectedNode) {
          emit('add-node', props.selectedNode)
        }
        break
      case 'delete':
        if (props.selectedNode) {
          emit('delete-node', props.selectedNode)
        }
        break
    }
    
    hideContextMenu()
  }

  const zoomIn = () => {
    scale.value = Math.min(scale.value * 1.2, 3)
  }

  const zoomOut = () => {
    scale.value = Math.max(scale.value / 1.2, 0.1)
  }

  const resetZoom = () => {
    scale.value = 1
    panX.value = 0
    panY.value = 0
  }

  const handleMinimapClick = (event: MouseEvent) => {
    const rect = (event.currentTarget as SVGElement).getBoundingClientRect()
    const x = (event.clientX - rect.left) / minimapScale.value
    const y = (event.clientY - rect.top) / minimapScale.value
    
    panX.value = -x + canvasWidth.value / 2
    panY.value = -y + canvasHeight.value / 2
  }

  const handleMouseMove = (event: MouseEvent) => {
    if (isDragging.value && dragNode.value) {
      const deltaX = event.clientX - dragStartPos.value.x
      const deltaY = event.clientY - dragStartPos.value.y
      
      dragNode.value.x += deltaX / scale.value
      dragNode.value.y += deltaY / scale.value
      
      dragStartPos.value = { x: event.clientX, y: event.clientY }
      emit('update-node', dragNode.value)
    }
  }

  const handleMouseUp = () => {
    isDragging.value = false
    dragNode.value = null
    isPanning.value = false
  }

  const updateCanvasSize = () => {
    if (canvasContainer.value) {
      canvasWidth.value = canvasContainer.value.clientWidth
      canvasHeight.value = canvasContainer.value.clientHeight
    }
  }

  // 任务状态相关方法
  const getTaskStatusColor = (status?: string) => {
    const colorMap: Record<string, string> = {
      'pending': '#909399',
      'in-progress': '#e6a23c',
      'completed': '#67c23a',
      'cancelled': '#f56c6c'
    }
    return colorMap[status || ''] || '#909399'
  }

  const getTaskStatusText = (status?: string) => {
    const textMap: Record<string, string> = {
      'pending': '待开始',
      'in-progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return textMap[status || ''] || '未知'
  }

  const getProgressColor = (progress?: number) => {
    if (!progress) {return '#e4e7ed'}
    if (progress >= 80) {return '#67c23a'}
    if (progress >= 50) {return '#e6a23c'}
    return '#f56c6c'
  }

  const getProgressDashArray = (progress?: number) => {
    if (!progress) {return '0 50.27'}
    const circumference = 2 * Math.PI * 8 // r=8
    const offset = circumference - (progress / 100) * circumference
    return `${circumference - offset} ${offset}`
  }

  // 生命周期
  onMounted(() => {
    updateCanvasSize()
    window.addEventListener('resize', updateCanvasSize)
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('mouseup', handleMouseUp)
    window.addEventListener('click', hideContextMenu)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateCanvasSize)
    window.removeEventListener('mousemove', handleMouseMove)
    window.removeEventListener('mouseup', handleMouseUp)
    window.removeEventListener('click', hideContextMenu)
  })

  // 监听布局变化
  watch(() => props.layout, () => {
    // 重新计算布局
    nextTick(() => {
      // 布局算法实现
    })
  })
</script>

<style scoped>
  .mindmap-canvas {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #fafafa;
  }

  .mindmap-svg {
    width: 100%;
    height: 100%;
    cursor: grab;
  }

  .mindmap-svg:active {
    cursor: grabbing;
  }

  .mindmap-node {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mindmap-node:hover {
    filter: brightness(1.1);
  }

  .mindmap-node.selected .node-shape {
    stroke: #409EFF;
    stroke-width: 3;
  }

  .node-text {
    pointer-events: none;
    user-select: none;
  }

  .node-icon {
    pointer-events: none;
    user-select: none;
  }

  .expand-button {
    cursor: pointer;
  }

  .expand-icon {
    cursor: pointer;
    pointer-events: none;
  }

  .mindmap-edge {
    cursor: pointer;
    transition: stroke-width 0.2s ease;
  }

  .mindmap-edge:hover {
    stroke-width: 3;
  }

  .selection-box {
    pointer-events: none;
  }

  .context-menu {
    position: fixed;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 120px;
  }

  .context-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
  }

  .context-menu-item:hover:not(.disabled) {
    background: #f5f5f5;
  }

  .context-menu-item.disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  .zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 100;
  }

  .minimap {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .minimap-svg {
    cursor: pointer;
  }

  /* 任务状态指示器样式 */
  .task-status-group {
    pointer-events: none;
  }

  .task-status-badge {
    opacity: 0.9;
  }

  .task-status-text {
    font-weight: 500;
  }

  .progress-background {
    opacity: 0.3;
  }

  .progress-foreground {
    transition: stroke-dasharray 0.3s ease;
  }

  .progress-text {
    font-weight: 600;
  }

  .priority-indicator {
    animation: pulse 2s infinite;
  }

  .review-reminder {
    animation: blink 1s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0.5;
    }
  }
</style>
