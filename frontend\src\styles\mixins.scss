// SCSS Mixins

// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 文本省略号
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// Flex 居中
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Flex 两端对齐
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 按钮样式混入
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover {
    color: $color;
    background-color: darken($background, 7.5%);
    border-color: darken($border, 10%);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($border, 0.5);
  }

  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12.5%);
  }

  &:disabled {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.65;
  }
}

// 卡片阴影
@mixin card-shadow($level: 'base') {
  @if map-has-key($box-shadows, $level) {
    box-shadow: map-get($box-shadows, $level);
  }
}

// 过渡动画
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 学科颜色混入
@mixin subject-color($subject) {
  @if map-has-key($subject-colors, $subject) {
    background-color: map-get($subject-colors, $subject);
  }
}

// 优先级颜色混入
@mixin priority-color($priority) {
  @if map-has-key($priority-colors, $priority) {
    color: map-get($priority-colors, $priority);
  }
}

// 滚动条样式
@mixin scrollbar($width: 8px, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;

    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: var(--el-color-primary)) {
  width: $size;
  height: $size;
  border: 2px solid transparent;
  border-top: 2px solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 渐变背景
@mixin gradient-background($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.8, $blur: 10px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
}

// 动画延迟
@mixin animation-delay($delay) {
  animation-delay: $delay;
}

// 网格布局
@mixin grid($columns: 1, $gap: 16px) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

// 隐藏元素（保持可访问性）
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
