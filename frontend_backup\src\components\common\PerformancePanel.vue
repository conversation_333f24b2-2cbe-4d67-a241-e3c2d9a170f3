<template>
  <div class="performance-panel">
    <ElCard>
      <template #header>
        <div class="panel-header">
          <span>性能监控</span>
          <div class="header-actions">
            <ElSwitch
              v-model="isEnabled"
              active-text="启用"
              inactive-text="禁用"
              @change="toggleMonitoring"
            />
            <ElButton size="small" @click="clearData">清除数据</ElButton>
            <ElButton size="small" @click="exportData">导出数据</ElButton>
          </div>
        </div>
      </template>

      <div v-if="!isEnabled" class="disabled-state">
        <ElEmpty description="性能监控已禁用">
          <ElButton type="primary" @click="isEnabled = true">启用监控</ElButton>
        </ElEmpty>
      </div>

      <div v-else class="monitoring-content">
        <!-- 实时性能指标 -->
        <div class="metrics-section">
          <h4>实时指标</h4>
          <ElRow :gutter="16">
            <ElCol :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ memoryUsage.toFixed(1) }}MB</div>
                <div class="metric-label">内存使用</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ fps }}</div>
                <div class="metric-label">FPS</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ networkInfo.effectiveType }}</div>
                <div class="metric-label">网络类型</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ deviceInfo.type }}</div>
                <div class="metric-label">设备类型</div>
              </div>
            </ElCol>
          </ElRow>
        </div>

        <!-- 性能测量结果 -->
        <div class="measures-section">
          <h4>性能测量</h4>
          <ElTable :data="measuresList" size="small" max-height="300">
            <ElTableColumn prop="name" label="操作" width="200" />
            <ElTableColumn prop="duration" label="耗时(ms)" width="100">
              <template #default="{ row }">
                <span :class="getDurationClass(row.duration)">
                  {{ row.duration.toFixed(2) }}
                </span>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="timestamp" label="时间" width="150">
              <template #default="{ row }">
                {{ formatTime(row.timestamp) }}
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" width="80">
              <template #default="{ row }">
                <ElTag :type="getStatusType(row.duration)" size="small">
                  {{ getStatusText(row.duration) }}
                </ElTag>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>

        <!-- 性能图表 -->
        <div class="charts-section">
          <h4>性能趋势</h4>
          <div ref="chartContainer" class="chart-container"></div>
        </div>

        <!-- 优化建议 -->
        <div class="suggestions-section">
          <h4>优化建议</h4>
          <div class="suggestions-list">
            <div
              v-for="suggestion in suggestions"
              :key="suggestion.id"
              class="suggestion-item"
              :class="suggestion.priority"
            >
              <ElIcon class="suggestion-icon">
                <component :is="suggestion.icon" />
              </ElIcon>
              <div class="suggestion-content">
                <div class="suggestion-title">{{ suggestion.title }}</div>
                <div class="suggestion-description">{{ suggestion.description }}</div>
              </div>
              <div class="suggestion-action">
                <ElButton size="small" @click="applySuggestion(suggestion)">
                  应用
                </ElButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import {
  ElButton,
  ElCard,
  ElCol,
  ElEmpty,
  ElIcon,
  ElMessage,
  ElRow,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElTag
} from 'element-plus'
import {
  InfoFilled,
  Warning
} from '@element-plus/icons-vue'
import {
  getDevicePixelRatio,
  getNetworkInfo,
  isMobileDevice,
  performanceMonitor
} from '@/utils/performance'
import * as echarts from 'echarts'

// 响应式数据
const isEnabled = ref(true)
const memoryUsage = ref(0)
const fps = ref(0)
const measuresList = ref<Array<{
  name: string
  duration: number
  timestamp: number
  status: string
}>>([])

const chartContainer = ref<HTMLElement>()
const chart = ref<echarts.ECharts>()

// 计算属性
const networkInfo = computed(() => getNetworkInfo())

const deviceInfo = computed(() => ({
  type: isMobileDevice() ? '移动设备' : '桌面设备',
  pixelRatio: getDevicePixelRatio()
}))

const suggestions = computed(() => {
  const result = []

  // 基于性能数据生成建议
  if (memoryUsage.value > 100) {
    result.push({
      id: 'memory-high',
      title: '内存使用过高',
      description: '当前内存使用超过100MB，建议清理不必要的数据',
      priority: 'high',
      icon: Warning
    })
  }

  if (fps.value < 30) {
    result.push({
      id: 'fps-low',
      title: 'FPS过低',
      description: '当前帧率低于30FPS，可能影响用户体验',
      priority: 'medium',
      icon: InfoFilled
    })
  }

  const slowOperations = measuresList.value.filter(m => m.duration > 1000)
  if (slowOperations.length > 0) {
    result.push({
      id: 'slow-operations',
      title: '存在慢操作',
      description: `发现${slowOperations.length}个耗时超过1秒的操作`,
      priority: 'high',
      icon: Warning
    })
  }

  if (networkInfo.value.effectiveType === 'slow-2g' || networkInfo.value.effectiveType === '2g') {
    result.push({
      id: 'slow-network',
      title: '网络连接较慢',
      description: '当前网络连接较慢，建议启用数据节省模式',
      priority: 'medium',
      icon: InfoFilled
    })
  }

  return result
})

// 方法
const toggleMonitoring = (enabled: boolean) => {
  if (enabled) {
    startMonitoring()
  } else {
    stopMonitoring()
  }
}

const startMonitoring = () => {
  // 开始监控内存使用
  startMemoryMonitoring()
  
  // 开始监控FPS
  startFPSMonitoring()
  
  // 监听性能测量
  startPerformanceMonitoring()
  
  // 初始化图表
  initChart()
}

const stopMonitoring = () => {
  // 停止所有监控
  if (memoryInterval.value) {
    clearInterval(memoryInterval.value)
  }
  
  if (fpsInterval.value) {
    clearInterval(fpsInterval.value)
  }
}

const memoryInterval = ref<number>()
const fpsInterval = ref<number>()
const lastFrameTime = ref(performance.now())
const frameCount = ref(0)

const startMemoryMonitoring = () => {
  memoryInterval.value = setInterval(() => {
    if ((performance as Performance & { memory?: { usedJSHeapSize: number } }).memory) {
      const memory = (performance as Performance & { memory: { usedJSHeapSize: number } }).memory
      memoryUsage.value = memory.usedJSHeapSize / 1024 / 1024
    }
  }, 1000)
}

const startFPSMonitoring = () => {
  const updateFPS = () => {
    frameCount.value++
    const now = performance.now()
    
    if (now - lastFrameTime.value >= 1000) {
      fps.value = Math.round((frameCount.value * 1000) / (now - lastFrameTime.value))
      frameCount.value = 0
      lastFrameTime.value = now
    }
    
    if (isEnabled.value) {
      requestAnimationFrame(updateFPS)
    }
  }
  
  requestAnimationFrame(updateFPS)
}

const startPerformanceMonitoring = () => {
  // 监听性能测量结果
  const originalMeasure = performanceMonitor.measure.bind(performanceMonitor)
  performanceMonitor.measure = function(name: string, startMark: string, endMark?: string) {
    const duration = originalMeasure(name, startMark, endMark)
    
    measuresList.value.unshift({
      name,
      duration,
      timestamp: Date.now(),
      status: duration > 1000 ? 'slow' : duration > 500 ? 'medium' : 'fast'
    })
    
    // 保持最近100条记录
    if (measuresList.value.length > 100) {
      measuresList.value = measuresList.value.slice(0, 100)
    }
    
    // 更新图表
    updateChart()
    
    return duration
  }
}

const initChart = () => {
  if (!chartContainer.value) {return}
  
  chart.value = echarts.init(chartContainer.value)
  
  const option = {
    title: {
      text: '性能趋势',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'time',
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '耗时(ms)',
      splitLine: { show: true }
    },
    series: [{
      name: '操作耗时',
      type: 'line',
      data: [],
      smooth: true,
      lineStyle: { color: '#409eff' },
      areaStyle: { opacity: 0.3 }
    }]
  }
  
  chart.value.setOption(option)
}

const updateChart = () => {
  if (!chart.value) {return}
  
  const data = measuresList.value
    .slice(0, 20)
    .reverse()
    .map(item => [item.timestamp, item.duration])
  
  chart.value.setOption({
    series: [{
      data
    }]
  })
}

const clearData = () => {
  measuresList.value = []
  performanceMonitor.clear()
  updateChart()
  ElMessage.success('性能数据已清除')
}

const exportData = () => {
  const data = {
    measures: measuresList.value,
    memoryUsage: memoryUsage.value,
    fps: fps.value,
    networkInfo: networkInfo.value,
    deviceInfo: deviceInfo.value,
    timestamp: Date.now()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-data-${new Date().toISOString().slice(0, 10)}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('性能数据已导出')
}

const applySuggestion = (suggestion: any) => {
  switch (suggestion.id) {
    case 'memory-high':
      // 触发垃圾回收（如果可能）
      if (window.gc) {
        window.gc()
      }
      ElMessage.success('已尝试清理内存')
      break
    case 'slow-network':
      // 启用数据节省模式的逻辑
      ElMessage.info('数据节省模式功能开发中')
      break
    default:
      ElMessage.info('优化建议已记录')
  }
}

const getDurationClass = (duration: number) => {
  if (duration > 1000) {return 'duration-slow'}
  if (duration > 500) {return 'duration-medium'}
  return 'duration-fast'
}

const getStatusType = (duration: number) => {
  if (duration > 1000) {return 'danger'}
  if (duration > 500) {return 'warning'}
  return 'success'
}

const getStatusText = (duration: number) => {
  if (duration > 1000) {return '慢'}
  if (duration > 500) {return '中'}
  return '快'
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  if (isEnabled.value) {
    startMonitoring()
  }
})

onUnmounted(() => {
  stopMonitoring()
  if (chart.value) {
    chart.value.dispose()
  }
})
</script>

<style scoped lang="scss">
.performance-panel {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .disabled-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .monitoring-content {
    .metrics-section,
    .measures-section,
    .charts-section,
    .suggestions-section {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }
    
    .metric-card {
      text-align: center;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .metric-value {
        font-size: 20px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .metric-label {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .chart-container {
      height: 300px;
      width: 100%;
    }
    
    .suggestions-list {
      .suggestion-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        border-left: 4px solid #e4e7ed;
        
        &.high {
          border-left-color: #f56c6c;
          background: #fef0f0;
        }
        
        &.medium {
          border-left-color: #e6a23c;
          background: #fdf6ec;
        }
        
        &.low {
          border-left-color: #67c23a;
          background: #f0f9ff;
        }
        
        .suggestion-icon {
          margin-right: 12px;
          font-size: 20px;
        }
        
        .suggestion-content {
          flex: 1;
          
          .suggestion-title {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .suggestion-description {
            font-size: 12px;
            color: #606266;
          }
        }
        
        .suggestion-action {
          margin-left: 12px;
        }
      }
    }
  }
}

// 表格中的耗时样式
:deep(.duration-slow) {
  color: #f56c6c;
  font-weight: 600;
}

:deep(.duration-medium) {
  color: #e6a23c;
  font-weight: 500;
}

:deep(.duration-fast) {
  color: #67c23a;
}
</style>
