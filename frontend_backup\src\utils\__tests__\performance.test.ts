import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  AsyncQueue,
  PerformanceMonitor,
  batchProcess,
  debounce,
  getDevicePixelRatio,
  getNetworkInfo,
  isMobileDevice,
  memoize,
  performanceMonitor,
  preloadImages,
  rafThrottle,
  supportsWebP,
  throttle
} from '../performance'

describe('Performance Utils', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.clearAllMocks()
  })

  describe('debounce', () => {
    it('should delay function execution', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn()
      expect(fn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(100)
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should cancel previous calls', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      vi.advanceTimersByTime(100)
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should execute immediately when immediate is true', () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100, true)

      debouncedFn()
      expect(fn).toHaveBeenCalledTimes(1)

      debouncedFn()
      expect(fn).toHaveBeenCalledTimes(1)
    })
  })

  describe('throttle', () => {
    it('should limit function execution frequency', () => {
      const fn = vi.fn()
      const throttledFn = throttle(fn, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(fn).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(100)
      expect(fn).toHaveBeenCalledTimes(2)
    })

    it('should respect leading and trailing options', () => {
      const fn = vi.fn()
      const throttledFn = throttle(fn, 100, { leading: false, trailing: true })

      throttledFn()
      expect(fn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(100)
      expect(fn).toHaveBeenCalledTimes(1)
    })
  })

  describe('rafThrottle', () => {
    it('should throttle using requestAnimationFrame', () => {
      const fn = vi.fn()
      const throttledFn = rafThrottle(fn)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(fn).not.toHaveBeenCalled()

      // 模拟RAF回调
      vi.runAllTimers()
      expect(fn).toHaveBeenCalledTimes(1)
    })
  })

  describe('memoize', () => {
    it('should cache function results', () => {
      const fn = vi.fn((x: number) => x * 2)
      const memoizedFn = memoize(fn)

      const result1 = memoizedFn(5)
      const result2 = memoizedFn(5)

      expect(result1).toBe(10)
      expect(result2).toBe(10)
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should use custom key generator', () => {
      const fn = vi.fn((obj: { id: number }) => obj.id * 2)
      const memoizedFn = memoize(fn, (obj) => obj.id.toString())

      const obj1 = { id: 1 }
      const obj2 = { id: 1 }

      memoizedFn(obj1)
      memoizedFn(obj2)

      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should respect max cache size', () => {
      const fn = vi.fn((x: number) => x * 2)
      const memoizedFn = memoize(fn, undefined, 2)

      memoizedFn(1)
      memoizedFn(2)
      memoizedFn(3) // Should evict first entry

      memoizedFn(1) // Should call function again
      expect(fn).toHaveBeenCalledTimes(4)
    })
  })

  describe('batchProcess', () => {
    it('should batch multiple calls', () => {
      const fn = vi.fn()
      const batchedFn = batchProcess(fn, 50)

      batchedFn('item1')
      batchedFn('item2')
      batchedFn('item3')

      expect(fn).not.toHaveBeenCalled()

      vi.advanceTimersByTime(50)
      expect(fn).toHaveBeenCalledWith(['item1', 'item2', 'item3'])
    })
  })

  describe('AsyncQueue', () => {
    it('should process tasks sequentially', async () => {
      const queue = new AsyncQueue(1)
      const results: number[] = []

      const task1 = () => Promise.resolve().then(() => results.push(1))
      const task2 = () => Promise.resolve().then(() => results.push(2))
      const task3 = () => Promise.resolve().then(() => results.push(3))

      queue.add(task1)
      queue.add(task2)
      queue.add(task3)

      await vi.waitFor(() => {
        expect(results).toEqual([1, 2, 3])
      })
    })

    it('should respect concurrency limit', async () => {
      const queue = new AsyncQueue(2)
      const executing: number[] = []
      const completed: number[] = []

      const createTask = (id: number) => () => {
        executing.push(id)
        return new Promise<void>(resolve => {
          setTimeout(() => {
            completed.push(id)
            resolve()
          }, 100)
        })
      }

      queue.add(createTask(1))
      queue.add(createTask(2))
      queue.add(createTask(3))

      await vi.waitFor(() => {
        expect(executing.length).toBe(2)
      })
    })
  })

  describe('preloadImages', () => {
    it('should preload images', async () => {
      const urls = ['image1.jpg', 'image2.jpg']
      
      // Mock Image constructor
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        src: ''
      }

      global.Image = vi.fn(() => mockImage) as unknown as typeof Image

      const promise = preloadImages(urls)
      
      // Simulate successful load
      mockImage.onload()
      
      await expect(promise).resolves.toBeUndefined()
    })
  })

  describe('supportsWebP', () => {
    it('should detect WebP support', async () => {
      const mockImage = {
        onload: null as (() => void) | null,
        onerror: null as (() => void) | null,
        height: 2,
        src: ''
      }

      global.Image = vi.fn(() => mockImage) as unknown as typeof Image

      const promise = supportsWebP()
      
      // Simulate successful load
      mockImage.onload()
      
      await expect(promise).resolves.toBe(true)
    })
  })

  describe('getDevicePixelRatio', () => {
    it('should return device pixel ratio', () => {
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        value: 2
      })

      expect(getDevicePixelRatio()).toBe(2)
    })

    it('should return 1 if devicePixelRatio is not available', () => {
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        value: undefined
      })

      expect(getDevicePixelRatio()).toBe(1)
    })
  })

  describe('isMobileDevice', () => {
    it('should detect mobile devices', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'
      })

      expect(isMobileDevice()).toBe(true)
    })

    it('should detect desktop devices', () => {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      })

      expect(isMobileDevice()).toBe(false)
    })
  })

  describe('getNetworkInfo', () => {
    it('should return network information', () => {
      const mockConnection = {
        effectiveType: '4g',
        downlink: 10,
        rtt: 100,
        saveData: false
      }

      Object.defineProperty(navigator, 'connection', {
        writable: true,
        value: mockConnection
      })

      const info = getNetworkInfo()
      expect(info).toEqual(mockConnection)
    })

    it('should return default values when connection API is not available', () => {
      Object.defineProperty(navigator, 'connection', {
        writable: true,
        value: undefined
      })

      const info = getNetworkInfo()
      expect(info).toEqual({
        effectiveType: 'unknown',
        downlink: 0,
        rtt: 0,
        saveData: false
      })
    })
  })

  describe('PerformanceMonitor', () => {
    let monitor: PerformanceMonitor

    beforeEach(() => {
      monitor = new PerformanceMonitor()
    })

    it('should mark time points', () => {
      monitor.mark('test-start')
      expect(monitor['marks'].has('test-start')).toBe(true)
    })

    it('should measure time intervals', () => {
      monitor.mark('test-start')
      vi.advanceTimersByTime(100)
      monitor.mark('test-end')

      const duration = monitor.measure('test-duration', 'test-start', 'test-end')
      expect(duration).toBeGreaterThan(0)
      expect(monitor.getMeasure('test-duration')).toBe(duration)
    })

    it('should clear marks and measures', () => {
      monitor.mark('test-mark')
      monitor.measure('test-measure', 'test-mark')

      monitor.clear()

      expect(monitor['marks'].size).toBe(0)
      expect(monitor['measures'].size).toBe(0)
    })

    it('should get all measures', () => {
      monitor.mark('start1')
      monitor.mark('start2')
      monitor.measure('measure1', 'start1')
      monitor.measure('measure2', 'start2')

      const allMeasures = monitor.getAllMeasures()
      expect(Object.keys(allMeasures)).toContain('measure1')
      expect(Object.keys(allMeasures)).toContain('measure2')
    })
  })

  describe('performanceMonitor singleton', () => {
    it('should be a singleton instance', () => {
      expect(performanceMonitor).toBeInstanceOf(PerformanceMonitor)
    })
  })
})
