import { beforeEach, describe, expect, it } from 'vitest'
import { mount } from '@vue/test-utils'
import VirtualList from '../VirtualList.vue'

describe('VirtualList', () => {
  const mockItems = Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `Item ${i}`,
    value: i * 10
  }))

  const defaultProps = {
    items: mockItems,
    itemHeight: 50,
    containerHeight: 400,
    keyField: 'id'
  }

  beforeEach(() => {
    // Mock getBoundingClientRect
    Element.prototype.getBoundingClientRect = vi.fn(() => ({
      width: 400,
      height: 400,
      top: 0,
      left: 0,
      bottom: 400,
      right: 400,
      x: 0,
      y: 0,
      toJSON: () => {}
    }))
  })

  it('should render correctly', () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    expect(wrapper.find('.virtual-list').exists()).toBe(true)
    expect(wrapper.find('.virtual-list-phantom').exists()).toBe(true)
    expect(wrapper.find('.virtual-list-content').exists()).toBe(true)
  })

  it('should calculate total height correctly', () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    const phantom = wrapper.find('.virtual-list-phantom')
    expect(phantom.attributes('style')).toContain('height: 50000px') // 1000 * 50
  })

  it('should render only visible items', () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item" data-testid="item-${item.id}">${item.name}</div>`
      }
    })

    // 应该只渲染可见的项目（约8个可见项目 + buffer）
    const items = wrapper.findAll('[data-testid^="item-"]')
    expect(items.length).toBeLessThan(50) // 远少于总数1000
    expect(items.length).toBeGreaterThan(5) // 但大于最小可见数
  })

  it('should handle scroll events', async () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    const container = wrapper.find('.virtual-list')
    
    // 模拟滚动事件
    await container.trigger('scroll', {
      target: { scrollTop: 500 }
    })

    // 检查内容是否相应地移动
    const content = wrapper.find('.virtual-list-content')
    expect(content.attributes('style')).toContain('translateY')
  })

  it('should use custom key field', () => {
    const customItems = [
      { customId: 1, name: 'Item 1' },
      { customId: 2, name: 'Item 2' }
    ]

    const wrapper = mount(VirtualList, {
      props: {
        ...defaultProps,
        items: customItems,
        keyField: 'customId'
      },
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    expect(wrapper.exists()).toBe(true)
  })

  it('should handle buffer prop', () => {
    const wrapper = mount(VirtualList, {
      props: {
        ...defaultProps,
        buffer: 10
      },
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    // 更大的buffer应该渲染更多项目
    const items = wrapper.findAll('.virtual-list-item')
    expect(items.length).toBeGreaterThan(8) // 基本可见项目数
  })

  it('should expose scroll methods', () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    const vm = wrapper.vm as { scrollToIndex: Function; scrollToTop: Function }
    expect(typeof vm.scrollToIndex).toBe('function')
    expect(typeof vm.scrollToTop).toBe('function')
  })

  it('should handle empty items array', () => {
    const wrapper = mount(VirtualList, {
      props: {
        ...defaultProps,
        items: []
      },
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    expect(wrapper.find('.virtual-list-phantom').attributes('style')).toContain('height: 0px')
    expect(wrapper.findAll('.virtual-list-item')).toHaveLength(0)
  })

  it('should update when items change', async () => {
    const wrapper = mount(VirtualList, {
      props: defaultProps,
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    const newItems = Array.from({ length: 500 }, (_, i) => ({
      id: i,
      name: `New Item ${i}`,
      value: i * 5
    }))

    await wrapper.setProps({ items: newItems })

    const phantom = wrapper.find('.virtual-list-phantom')
    expect(phantom.attributes('style')).toContain('height: 25000px') // 500 * 50
  })

  it('should handle different item heights', async () => {
    const wrapper = mount(VirtualList, {
      props: {
        ...defaultProps,
        itemHeight: 100
      },
      slots: {
        default: ({ item }) => `<div class="item">${item.name}</div>`
      }
    })

    const phantom = wrapper.find('.virtual-list-phantom')
    expect(phantom.attributes('style')).toContain('height: 100000px') // 1000 * 100

    const items = wrapper.findAll('.virtual-list-item')
    items.forEach(item => {
      expect(item.attributes('style')).toContain('height: 100px')
    })
  })
})
