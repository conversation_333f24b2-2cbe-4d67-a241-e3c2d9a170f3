# [DEV-FRONTEND-001] 前端用户交互界面开发文档

## 📋 概述

本文档基于需求分析和系统设计文档，详细定义了艾宾浩斯记忆曲线学习管理系统前端用户交互界面的开发规范、页面结构、组件设计和实现方案。

## 🎯 开发目标

### 核心目标
- **用户体验优先**：界面简洁易用，符合初中生用户认知习惯
- **响应式设计**：适配桌面端和移动端设备
- **交互流畅**：操作响应快速，反馈及时
- **视觉一致**：统一的设计语言和视觉风格

### 技术目标
- **Vue 3 + TypeScript**：现代化前端技术栈
- **Element Plus**：成熟的UI组件库
- **组件化开发**：高复用性和可维护性
- **状态管理**：Pinia统一状态管理

## 🏗️ 页面结构设计

### 整体布局架构
```
┌─────────────────────────────────────────┐
│                 Header                  │ ← 顶部导航栏
├─────────────────────────────────────────┤
│ Sidebar │        Main Content         │ ← 侧边栏 + 主内容区
│         │                             │
│ 导航菜单 │     页面内容区域             │
│         │                             │
│         │                             │
├─────────┴─────────────────────────────────┤
│                 Footer                  │ ← 底部信息栏
└─────────────────────────────────────────┘
```

### 核心页面结构

#### 1. 主页面 (Dashboard)
- **路由路径**：`/dashboard`
- **功能概述**：学习概览、今日任务、复习提醒
- **核心组件**：
  - `DashboardOverview` - 学习数据概览
  - `TodayTasks` - 今日任务列表
  - `ReviewReminders` - 复习提醒卡片
  - `ProgressChart` - 学习进度图表

#### 2. 任务管理页面 (Tasks)
- **路由路径**：`/tasks`
- **功能概述**：任务列表、创建任务、任务详情
- **核心组件**：
  - `TaskList` - 任务列表组件
  - `TaskCard` - 任务卡片组件
  - `TaskCreateForm` - 任务创建表单
  - `TaskFilter` - 任务筛选器

#### 3. 复习计划页面 (Review)
- **路由路径**：`/review`
- **功能概述**：复习计划展示、复习执行
- **核心组件**：
  - `ReviewSchedule` - 复习计划时间轴
  - `ReviewCard` - 复习任务卡片
  - `ReviewExecution` - 复习执行界面

#### 4. 思维导图页面 (MindMap)
- **路由路径**：`/mindmap`
- **功能概述**：知识结构可视化
- **核心组件**：
  - `MindMapCanvas` - 思维导图画布
  - `MindMapToolbar` - 工具栏
  - `NodeEditor` - 节点编辑器

## 🧩 组件划分方案

### 组件层次结构
```
src/
├── components/
│   ├── layout/              # 布局组件
│   │   ├── AppHeader.vue
│   │   ├── AppSidebar.vue
│   │   ├── AppFooter.vue
│   │   └── AppLayout.vue
│   │
│   ├── common/              # 通用组件
│   │   ├── BaseButton.vue
│   │   ├── BaseCard.vue
│   │   ├── BaseModal.vue
│   │   ├── LoadingSpinner.vue
│   │   └── EmptyState.vue
│   │
│   ├── task/                # 任务相关组件
│   │   ├── TaskCard.vue
│   │   ├── TaskList.vue
│   │   ├── TaskCreateForm.vue
│   │   ├── TaskFilter.vue
│   │   └── TaskDetail.vue
│   │
│   ├── review/              # 复习相关组件
│   │   ├── ReviewSchedule.vue
│   │   ├── ReviewCard.vue
│   │   ├── ReviewExecution.vue
│   │   └── ReviewTimer.vue
│   │
│   └── mindmap/             # 思维导图组件
│       ├── MindMapCanvas.vue
│       ├── MindMapToolbar.vue
│       └── NodeEditor.vue
│
└── views/                   # 页面组件
    ├── Dashboard.vue
    ├── Tasks.vue
    ├── Review.vue
    └── MindMap.vue
```

### 核心组件设计

#### TaskCard 组件
```vue
<template>
  <el-card class="task-card" :class="cardClass">
    <template #header>
      <div class="task-header">
        <h3 class="task-title">{{ task.title }}</h3>
        <el-tag :type="priorityType">{{ priorityText }}</el-tag>
      </div>
    </template>
    
    <div class="task-content">
      <p class="task-description">{{ task.content }}</p>
      <div class="task-meta">
        <span class="subject">{{ task.subject }}</span>
        <span class="time">{{ task.estimatedTime }}分钟</span>
        <span class="difficulty">难度: {{ task.difficulty }}/5</span>
      </div>
    </div>
    
    <template #footer>
      <div class="task-actions">
        <el-button @click="startReview">开始复习</el-button>
        <el-button type="primary" @click="viewDetail">查看详情</el-button>
      </div>
    </template>
  </el-card>
</template>
```

#### TaskCreateForm 组件
```vue
<template>
  <el-form 
    ref="formRef" 
    :model="form" 
    :rules="rules" 
    label-width="100px"
    class="task-create-form"
  >
    <el-form-item label="任务标题" prop="title">
      <el-input 
        v-model="form.title" 
        placeholder="请输入任务标题"
        maxlength="100"
        show-word-limit
      />
    </el-form-item>
    
    <el-form-item label="学科分类" prop="subject">
      <el-select v-model="form.subject" placeholder="请选择学科">
        <el-option 
          v-for="subject in subjects" 
          :key="subject.value"
          :label="subject.label" 
          :value="subject.value"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="任务内容" prop="content">
      <el-input 
        v-model="form.content"
        type="textarea"
        :rows="4"
        placeholder="请输入任务内容"
        maxlength="5000"
        show-word-limit
      />
    </el-form-item>
    
    <el-form-item label="预估时间">
      <el-input-number 
        v-model="form.estimatedTime"
        :min="1" 
        :max="300"
        controls-position="right"
      />
      <span class="time-unit">分钟</span>
    </el-form-item>
    
    <el-form-item label="优先级">
      <el-rate v-model="form.priority" :max="5" />
    </el-form-item>
    
    <el-form-item label="难度级别">
      <el-rate v-model="form.difficulty" :max="5" />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">创建任务</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>
```

## 🎨 UI组件库使用规范

### Element Plus 组件使用标准

#### 颜色主题配置
```scss
// src/styles/variables.scss
:root {
  // 主色调 - 学习主题蓝色
  --el-color-primary: #409EFF;
  --el-color-primary-light-3: #79BBFF;
  --el-color-primary-light-5: #A0CFFF;
  --el-color-primary-light-7: #C6E2FF;
  --el-color-primary-light-8: #D9ECFF;
  --el-color-primary-light-9: #ECF5FF;
  --el-color-primary-dark-2: #337ECC;
  
  // 成功色 - 完成状态绿色
  --el-color-success: #67C23A;
  
  // 警告色 - 提醒橙色
  --el-color-warning: #E6A23C;
  
  // 危险色 - 错误红色
  --el-color-danger: #F56C6C;
  
  // 信息色 - 中性灰色
  --el-color-info: #909399;
  
  // 文字颜色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #C0C4CC;
  
  // 边框颜色
  --el-border-color: #DCDFE6;
  --el-border-color-light: #E4E7ED;
  --el-border-color-lighter: #EBEEF5;
  --el-border-color-extra-light: #F2F6FC;
}
```

#### 组件使用规范
```typescript
// 按钮使用规范
interface ButtonUsage {
  primary: '主要操作按钮 - 创建任务、开始复习等'
  success: '成功操作按钮 - 完成复习、保存成功等'
  warning: '警告操作按钮 - 删除任务、重置数据等'
  info: '信息操作按钮 - 查看详情、更多信息等'
  text: '文本按钮 - 次要操作、链接操作等'
}

// 表单验证规范
const formRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入任务内容', trigger: 'blur' },
    { min: 1, max: 5000, message: '内容长度在 1 到 5000 个字符', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择学科', trigger: 'change' }
  ]
}
```

## 🔄 状态管理设计

### Pinia Store 结构
```typescript
// src/stores/task.ts
export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [] as Task[],
    currentTask: null as Task | null,
    loading: false,
    filters: {
      subject: '',
      priority: 0,
      status: ''
    }
  }),
  
  getters: {
    todayTasks: (state) => {
      return state.tasks.filter(task => 
        dayjs(task.nextReviewTime).isSame(dayjs(), 'day')
      )
    },
    
    tasksBySubject: (state) => {
      return (subject: string) => 
        state.tasks.filter(task => task.subject === subject)
    }
  },
  
  actions: {
    async createTask(taskData: CreateTaskRequest) {
      this.loading = true
      try {
        const response = await taskApi.createTask(taskData)
        this.tasks.push(response.data)
        return response.data
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async loadTasks() {
      this.loading = true
      try {
        const response = await taskApi.getTasks()
        this.tasks = response.data
      } catch (error) {
        console.error('加载任务失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 🛣️ 路由配置方案

### Vue Router 配置
```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '学习概览',
      icon: 'dashboard',
      requiresAuth: true
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/Tasks.vue'),
    meta: {
      title: '任务管理',
      icon: 'task',
      requiresAuth: true
    },
    children: [
      {
        path: 'create',
        name: 'TaskCreate',
        component: () => import('@/views/TaskCreate.vue'),
        meta: { title: '创建任务' }
      },
      {
        path: ':id',
        name: 'TaskDetail',
        component: () => import('@/views/TaskDetail.vue'),
        meta: { title: '任务详情' }
      }
    ]
  },
  {
    path: '/review',
    name: 'Review',
    component: () => import('@/views/Review.vue'),
    meta: {
      title: '复习计划',
      icon: 'review',
      requiresAuth: true
    }
  },
  {
    path: '/mindmap',
    name: 'MindMap',
    component: () => import('@/views/MindMap.vue'),
    meta: {
      title: '思维导图',
      icon: 'mindmap',
      requiresAuth: true
    }
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

## 📱 响应式设计规范

### 断点设置
```scss
// src/styles/breakpoints.scss
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

### 移动端适配
```vue
<template>
  <div class="task-list" :class="{ 'mobile': isMobile }">
    <!-- 桌面端显示表格 -->
    <el-table v-if="!isMobile" :data="tasks">
      <!-- 表格列定义 -->
    </el-table>
    
    <!-- 移动端显示卡片列表 -->
    <div v-else class="mobile-task-list">
      <task-card 
        v-for="task in tasks" 
        :key="task.id" 
        :task="task"
        class="mobile-task-card"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)
</script>
```

## 🎭 用户交互流程图

### 任务创建流程
```mermaid
graph TD
    A[用户点击新建任务] --> B[显示任务创建表单]
    B --> C[用户填写基本信息]
    C --> D[表单验证]
    D --> E{验证通过?}
    E -->|否| F[显示错误提示]
    F --> C
    E -->|是| G[调用负载检查API]
    G --> H{负载检查}
    H -->|正常| I[创建任务成功]
    H -->|超载| J[显示负载警告]
    J --> K[用户选择调整或继续]
    K -->|调整| C
    K -->|继续| I
    I --> L[显示成功提示]
    L --> M[跳转到任务列表]
```

### 复习执行流程
```mermaid
graph TD
    A[接收复习提醒] --> B[用户点击开始复习]
    B --> C[显示复习界面]
    C --> D[展示任务内容]
    D --> E[用户进行复习]
    E --> F[复习完成]
    F --> G[记录复习结果]
    G --> H[更新复习状态]
    H --> I[生成下次复习时间]
    I --> J[显示完成反馈]
```

## 🎨 视觉设计规范

### 设计原则
- **简洁明了**：界面元素精简，避免视觉干扰
- **层次清晰**：信息层级分明，重点突出
- **色彩和谐**：使用统一的色彩体系
- **符合认知**：符合初中生用户的使用习惯

### 色彩系统
```scss
// 主题色彩
$primary-color: #409EFF;      // 主色调 - 学习蓝
$success-color: #67C23A;      // 成功绿 - 完成状态
$warning-color: #E6A23C;      // 警告橙 - 提醒状态
$danger-color: #F56C6C;       // 危险红 - 错误状态
$info-color: #909399;         // 信息灰 - 中性状态

// 学科色彩
$subject-colors: (
  'chinese': #FF6B6B,         // 语文 - 红色
  'math': #4ECDC4,           // 数学 - 青色
  'english': #45B7D1,        // 英语 - 蓝色
  'physics': #96CEB4,        // 物理 - 绿色
  'chemistry': #FFEAA7,      // 化学 - 黄色
  'biology': #DDA0DD,        // 生物 - 紫色
  'history': #F4A261,        // 历史 - 橙色
  'geography': #2A9D8F       // 地理 - 深绿
);

// 优先级色彩
$priority-colors: (
  1: #C0C4CC,               // 最低优先级 - 浅灰
  2: #909399,               // 低优先级 - 灰色
  3: #409EFF,               // 中等优先级 - 蓝色
  4: #E6A23C,               // 高优先级 - 橙色
  5: #F56C6C                // 最高优先级 - 红色
);
```

### 字体规范
```scss
// 字体大小
$font-sizes: (
  'xs': 12px,
  'sm': 14px,
  'base': 16px,
  'lg': 18px,
  'xl': 20px,
  '2xl': 24px,
  '3xl': 30px
);

// 字体权重
$font-weights: (
  'light': 300,
  'normal': 400,
  'medium': 500,
  'semibold': 600,
  'bold': 700
);
```

## 🔧 Mock数据设计

### 任务数据模拟
```typescript
// src/mock/taskData.ts
export const mockTasks: Task[] = [
  {
    id: 'task-001',
    title: '英语单词 Unit 3',
    content: '学习Unit 3的30个核心词汇，包括单词拼写、词义理解和例句应用',
    subject: 'english',
    estimatedTime: 30,
    priority: 3,
    difficulty: 2,
    tags: ['词汇', 'Unit3'],
    status: 'active',
    createdAt: '2025-01-31T09:00:00.000Z',
    nextReviewTime: '2025-01-31T09:05:00.000Z',
    reviewSchedule: [
      {
        reviewTime: '2025-01-31T09:05:00.000Z',
        reviewIndex: 1,
        status: 'scheduled'
      },
      {
        reviewTime: '2025-01-31T09:30:00.000Z',
        reviewIndex: 2,
        status: 'scheduled'
      }
      // ... 其他复习时间点
    ]
  },
  {
    id: 'task-002',
    title: '数学二次函数练习',
    content: '完成二次函数的图像绘制和性质分析练习题',
    subject: 'math',
    estimatedTime: 45,
    priority: 4,
    difficulty: 4,
    tags: ['函数', '图像'],
    status: 'active',
    createdAt: '2025-01-30T14:00:00.000Z',
    nextReviewTime: '2025-01-31T14:00:00.000Z',
    reviewSchedule: []
  }
]

// 学科选项
export const subjectOptions = [
  { label: '语文', value: 'chinese', color: '#FF6B6B' },
  { label: '数学', value: 'math', color: '#4ECDC4' },
  { label: '英语', value: 'english', color: '#45B7D1' },
  { label: '物理', value: 'physics', color: '#96CEB4' },
  { label: '化学', value: 'chemistry', color: '#FFEAA7' },
  { label: '生物', value: 'biology', color: '#DDA0DD' },
  { label: '历史', value: 'history', color: '#F4A261' },
  { label: '地理', value: 'geography', color: '#2A9D8F' }
]
```

### API Mock服务
```typescript
// src/mock/api.ts
import { mockTasks } from './taskData'

export const mockApi = {
  // 获取任务列表
  getTasks: () => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          success: true,
          data: mockTasks,
          timestamp: new Date().toISOString()
        })
      }, 500)
    })
  },

  // 创建任务
  createTask: (taskData: CreateTaskRequest) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟表单验证
        if (!taskData.title || !taskData.content) {
          reject({
            success: false,
            error: {
              code: 'INVALID_INPUT',
              message: '标题和内容不能为空'
            }
          })
          return
        }

        // 模拟负载检查
        const loadWarning = taskData.estimatedTime > 60 ? {
          level: 'heavy',
          message: '当日学习负载较重',
          suggestions: ['建议调整到明天', '减少学习时间']
        } : null

        const newTask = {
          id: `task-${Date.now()}`,
          ...taskData,
          status: 'active',
          createdAt: new Date().toISOString(),
          reviewSchedule: generateReviewSchedule()
        }

        resolve({
          success: true,
          data: {
            taskId: newTask.id,
            reviewSchedule: newTask.reviewSchedule,
            loadWarning
          }
        })
      }, 800)
    })
  }
}

// 生成复习计划
function generateReviewSchedule() {
  const now = new Date()
  const intervals = [5, 30, 720, 1440, 4320, 10080, 20160, 43200, 86400] // 分钟

  return intervals.map((interval, index) => ({
    reviewTime: new Date(now.getTime() + interval * 60000).toISOString(),
    reviewIndex: index + 1,
    status: 'scheduled'
  }))
}
```

## 🧪 开发测试策略

### 组件测试
```typescript
// src/components/__tests__/TaskCard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import TaskCard from '../task/TaskCard.vue'

describe('TaskCard', () => {
  const mockTask = {
    id: 'test-task',
    title: '测试任务',
    content: '这是一个测试任务',
    subject: 'math',
    priority: 3,
    difficulty: 2,
    estimatedTime: 30
  }

  it('正确渲染任务信息', () => {
    const wrapper = mount(TaskCard, {
      props: { task: mockTask }
    })

    expect(wrapper.find('.task-title').text()).toBe('测试任务')
    expect(wrapper.find('.task-description').text()).toBe('这是一个测试任务')
    expect(wrapper.find('.subject').text()).toBe('math')
  })

  it('点击开始复习按钮触发事件', async () => {
    const wrapper = mount(TaskCard, {
      props: { task: mockTask }
    })

    await wrapper.find('.start-review-btn').trigger('click')
    expect(wrapper.emitted('start-review')).toBeTruthy()
  })
})
```

### E2E测试场景
```typescript
// cypress/e2e/task-creation.cy.ts
describe('任务创建流程', () => {
  beforeEach(() => {
    cy.visit('/tasks')
  })

  it('成功创建新任务', () => {
    // 点击新建任务按钮
    cy.get('[data-cy=create-task-btn]').click()

    // 填写任务信息
    cy.get('[data-cy=task-title]').type('测试任务标题')
    cy.get('[data-cy=task-subject]').select('math')
    cy.get('[data-cy=task-content]').type('这是测试任务内容')
    cy.get('[data-cy=estimated-time]').clear().type('30')

    // 提交表单
    cy.get('[data-cy=submit-btn]').click()

    // 验证成功提示
    cy.get('.el-message--success').should('be.visible')

    // 验证跳转到任务列表
    cy.url().should('include', '/tasks')

    // 验证新任务出现在列表中
    cy.contains('测试任务标题').should('be.visible')
  })

  it('表单验证错误处理', () => {
    cy.get('[data-cy=create-task-btn]').click()

    // 不填写必填字段直接提交
    cy.get('[data-cy=submit-btn]').click()

    // 验证错误提示
    cy.get('.el-form-item__error').should('contain', '请输入任务标题')
  })
})
```

---

**文档版本**：v1.0
**创建时间**：2025-01-31
**维护团队**：前端开发团队
**实现需求**：[REQ-FUNC-001] ~ [REQ-FUNC-006]
