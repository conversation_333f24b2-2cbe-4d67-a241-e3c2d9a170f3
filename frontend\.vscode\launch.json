{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor"]}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/src", "sourceMaps": true}, {"name": "Launch Firefox", "type": "firefox", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true}, {"name": "Launch Edge", "type": "msedge", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/edge-debug-profile"}, {"name": "Debug Vitest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run", "--reporter=verbose"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "test"}, "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Debug Vitest Current File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run", "${relativeFile}", "--reporter=verbose"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "test"}, "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Debug Playwright Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@playwright/test/cli.js", "args": ["test", "--debug"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "test"}, "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Debug Build Process", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vite/bin/vite.js", "args": ["build", "--mode", "development"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}], "compounds": [{"name": "Launch Dev Server + Chrome", "configurations": ["Launch Chrome"], "preLaunchTask": "npm: dev", "stopAll": true}, {"name": "Debug All Tests", "configurations": ["Debug Vitest Tests", "Debug Playwright Tests"], "stopAll": true}]}