<template>
  <div class="mindmap-settings">
    <div class="settings-content">
      <!-- 基本信息 -->
      <div class="settings-section">
        <h5>基本信息</h5>
        
        <div class="setting-item">
          <label>标题</label>
          <el-input
            v-model="settings.title"
            placeholder="思维导图标题"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>描述</label>
          <el-input
            v-model="settings.description"
            type="textarea"
            :rows="2"
            placeholder="思维导图描述"
            @change="updateSettings"
          />
        </div>
      </div>

      <!-- 布局设置 -->
      <div class="settings-section">
        <h5>布局设置</h5>
        
        <div class="setting-item">
          <label>布局类型</label>
          <el-select v-model="settings.layout" @change="updateSettings">
            <el-option label="树形布局" value="tree" />
            <el-option label="径向布局" value="radial" />
            <el-option label="力导向布局" value="force" />
            <el-option label="层次布局" value="hierarchical" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <label>节点间距</label>
          <el-slider
            v-model="settings.nodeSpacing"
            :min="20"
            :max="200"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>层级间距</label>
          <el-slider
            v-model="settings.levelSpacing"
            :min="50"
            :max="300"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>自动布局</label>
          <el-switch
            v-model="settings.autoLayout"
            @change="updateSettings"
          />
        </div>
      </div>

      <!-- 画布设置 -->
      <div class="settings-section">
        <h5>画布设置</h5>
        
        <div class="setting-item">
          <label>背景颜色</label>
          <el-color-picker
            v-model="settings.backgroundColor"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>显示网格</label>
          <el-switch
            v-model="settings.gridEnabled"
            @change="updateSettings"
          />
        </div>
        
        <div v-if="settings.gridEnabled" class="setting-item">
          <label>网格大小</label>
          <el-slider
            v-model="settings.gridSize"
            :min="10"
            :max="50"
            @change="updateSettings"
          />
        </div>
        
        <div v-if="settings.gridEnabled" class="setting-item">
          <label>网格颜色</label>
          <el-color-picker
            v-model="settings.gridColor"
            @change="updateSettings"
          />
        </div>
      </div>

      <!-- 默认样式 -->
      <div class="settings-section">
        <h5>默认样式</h5>
        
        <div class="setting-item">
          <label>节点颜色</label>
          <el-color-picker
            v-model="settings.defaultNodeColor"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>节点形状</label>
          <el-select v-model="settings.defaultNodeShape" @change="updateSettings">
            <el-option label="矩形" value="rectangle" />
            <el-option label="圆形" value="circle" />
            <el-option label="椭圆" value="ellipse" />
            <el-option label="菱形" value="diamond" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <label>字体大小</label>
          <el-slider
            v-model="settings.defaultFontSize"
            :min="10"
            :max="32"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>连线颜色</label>
          <el-color-picker
            v-model="settings.defaultEdgeColor"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>连线宽度</label>
          <el-slider
            v-model="settings.defaultEdgeWidth"
            :min="1"
            :max="10"
            @change="updateSettings"
          />
        </div>
      </div>

      <!-- 交互设置 -->
      <div class="settings-section">
        <h5>交互设置</h5>
        
        <div class="setting-item">
          <label>启用拖拽</label>
          <el-switch
            v-model="settings.enableDrag"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>启用缩放</label>
          <el-switch
            v-model="settings.enableZoom"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>启用平移</label>
          <el-switch
            v-model="settings.enablePan"
            @change="updateSettings"
          />
        </div>
        
        <div class="setting-item">
          <label>多选模式</label>
          <el-switch
            v-model="settings.enableMultiSelect"
            @change="updateSettings"
          />
        </div>
      </div>

      <!-- 动画设置 -->
      <div class="settings-section">
        <h5>动画设置</h5>
        
        <div class="setting-item">
          <label>启用动画</label>
          <el-switch
            v-model="settings.animationEnabled"
            @change="updateSettings"
          />
        </div>
        
        <div v-if="settings.animationEnabled" class="setting-item">
          <label>动画时长</label>
          <el-slider
            v-model="settings.animationDuration"
            :min="100"
            :max="2000"
            :step="100"
            @change="updateSettings"
          />
          <span class="setting-unit">ms</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button size="small" @click="resetSettings">重置设置</el-button>
        <el-button size="small" type="primary" @click="saveSettings">保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { MindMap, MindMapSettings } from '@/types/mindmap'

  interface Props {
    mindmap: MindMap
  }

  interface Emits {
    (e: 'update-settings', settings: Partial<MindMapSettings>): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 设置数据
  const settings = ref({
    // 基本信息
    title: '',
    description: '',
    layout: 'tree' as const,
    
    // 布局设置
    nodeSpacing: 100,
    levelSpacing: 150,
    autoLayout: true,
    
    // 画布设置
    backgroundColor: '#fafafa',
    gridEnabled: true,
    gridSize: 20,
    gridColor: '#f0f0f0',
    
    // 默认样式
    defaultNodeColor: '#409EFF',
    defaultNodeShape: 'rectangle' as const,
    defaultFontSize: 14,
    defaultEdgeColor: '#999999',
    defaultEdgeWidth: 2,
    
    // 交互设置
    enableDrag: true,
    enableZoom: true,
    enablePan: true,
    enableMultiSelect: false,
    
    // 动画设置
    animationEnabled: true,
    animationDuration: 300
  })

  // 监听思维导图变化
  watch(() => props.mindmap, (newMindmap) => {
    if (newMindmap) {
      settings.value.title = newMindmap.title
      settings.value.description = newMindmap.description
      settings.value.layout = newMindmap.layout as any
      
      // 合并设置
      if (newMindmap.settings) {
        Object.assign(settings.value, newMindmap.settings)
      }
    }
  }, { immediate: true })

  // 方法
  const updateSettings = () => {
    emit('update-settings', { ...settings.value })
  }

  const resetSettings = () => {
    settings.value = {
      title: props.mindmap.title,
      description: props.mindmap.description,
      layout: 'tree',
      nodeSpacing: 100,
      levelSpacing: 150,
      autoLayout: true,
      backgroundColor: '#fafafa',
      gridEnabled: true,
      gridSize: 20,
      gridColor: '#f0f0f0',
      defaultNodeColor: '#409EFF',
      defaultNodeShape: 'rectangle',
      defaultFontSize: 14,
      defaultEdgeColor: '#999999',
      defaultEdgeWidth: 2,
      enableDrag: true,
      enableZoom: true,
      enablePan: true,
      enableMultiSelect: false,
      animationEnabled: true,
      animationDuration: 300
    }
    updateSettings()
    ElMessage.success('设置已重置')
  }

  const saveSettings = () => {
    updateSettings()
    ElMessage.success('设置已保存')
  }
</script>

<style scoped>
  .mindmap-settings {
    height: 100%;
    overflow-y: auto;
  }

  .settings-content {
    padding: 8px 0;
  }

  .settings-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .settings-section:last-child {
    border-bottom: none;
  }

  .settings-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
  }

  .setting-item label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    min-width: 60px;
    flex-shrink: 0;
  }

  .setting-item .el-input,
  .setting-item .el-select {
    flex: 1;
    max-width: 120px;
  }

  .setting-item .el-slider {
    flex: 1;
    max-width: 100px;
  }

  .setting-unit {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    margin-left: 4px;
  }

  .settings-actions {
    display: flex;
    gap: 8px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .settings-actions .el-button {
    flex: 1;
  }
</style>
