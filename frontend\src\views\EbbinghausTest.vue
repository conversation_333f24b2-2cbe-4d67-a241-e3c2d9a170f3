<template>
  <div class="ebbinghaus-test">
    <ElCard>
      <template #header>
        <h2>艾宾浩斯9个时间点展示测试</h2>
      </template>
      
      <div class="test-content">
        <!-- 标准时间点展示 -->
        <div class="standard-points">
          <h3>标准艾宾浩斯时间点</h3>
          <div class="time-points">
            <div
              v-for="(point, index) in timePoints"
              :key="index"
              :class="[
                'time-point',
                {
                  'completed': index < currentLevel,
                  'current': index === currentLevel,
                  'pending': index > currentLevel
                }
              ]"
            >
              <div class="point-indicator">
                <ElIcon v-if="index < currentLevel">
                  <Check />
                </ElIcon>
                <ElIcon v-else-if="index === currentLevel">
                  <Clock />
                </ElIcon>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="point-info">
                <div class="point-name">{{ point.name }}</div>
                <div class="point-description">{{ point.description }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 模拟进度控制 -->
        <div class="progress-control">
          <h3>模拟复习进度</h3>
          <ElSlider
            v-model="currentLevel"
            :min="0"
            :max="9"
            :marks="marks"
            show-stops
          />
          <p>当前进度：{{ currentLevel }}/9</p>
        </div>
        
        <!-- 复习历史组件测试 -->
        <div class="history-test">
          <h3>复习历史组件测试</h3>
          <ReviewHistory
            :sessions="mockSessions"
            task-id="test-task"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElCard, ElIcon, ElSlider } from 'element-plus'
import { Check, Clock } from '@element-plus/icons-vue'
import { EbbinghausAlgorithm, type ReviewSession } from '@/services/EbbinghausAlgorithm'
import ReviewHistory from '@/components/review/ReviewHistory.vue'

// 算法实例
const algorithm = new EbbinghausAlgorithm()

// 响应式数据
const currentLevel = ref(2) // 模拟当前在第3个时间点

// 计算属性
const timePoints = computed(() => algorithm.getStandardTimePoints())

const marks = computed(() => {
  const result: Record<number, string> = {}
  timePoints.value.forEach((point, index) => {
    result[index] = point.name
  })
  return result
})

// 模拟复习会话数据
const mockSessions = computed((): ReviewSession[] => {
  const sessions: ReviewSession[] = []
  
  for (let i = 0; i < currentLevel.value; i++) {
    sessions.push({
      taskId: 'test-task',
      reviewIndex: i,
      features: {
        subject: '英语',
        difficulty: 3,
        estimatedTime: 30,
        tags: ['单词', '记忆'],
        description: '英语单词记忆练习',
        contentLength: 100,
        hasImages: false,
        hasFormulas: false,
        taskType: 'review'
      },
      actualTime: 25 + Math.random() * 10,
      completionRate: 0.8 + Math.random() * 0.2,
      quality: 3 + Math.floor(Math.random() * 3),
      timestamp: new Date(Date.now() - (currentLevel.value - i) * 24 * 60 * 60 * 1000).toISOString(),
      timeOfDay: 14,
      dayOfWeek: new Date().getDay(),
      userState: {
        energy: 3 + Math.floor(Math.random() * 2),
        focus: 3 + Math.floor(Math.random() * 2),
        mood: 3 + Math.floor(Math.random() * 2)
      }
    })
  }
  
  return sessions
})
</script>

<style scoped lang="scss">
.ebbinghaus-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-content {
  .standard-points {
    margin-bottom: 40px;
    
    h3 {
      margin-bottom: 20px;
      color: #303133;
    }
    
    .time-points {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    .time-point {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 8px;
      border: 2px solid #e4e7ed;
      background: #fff;
      min-width: 200px;
      
      &.completed {
        background: #f0f9ff;
        border-color: #409eff;
        
        .point-indicator {
          background: #409eff;
          color: white;
        }
      }
      
      &.current {
        background: #fff7e6;
        border-color: #e6a23c;
        
        .point-indicator {
          background: #e6a23c;
          color: white;
        }
      }
      
      &.pending {
        .point-indicator {
          background: #f5f7fa;
          color: #909399;
          border: 1px solid #dcdfe6;
        }
      }
      
      .point-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        font-size: 14px;
        font-weight: 600;
        flex-shrink: 0;
      }
      
      .point-info {
        .point-name {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .point-description {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }
  
  .progress-control {
    margin-bottom: 40px;
    
    h3 {
      margin-bottom: 20px;
      color: #303133;
    }
    
    .el-slider {
      margin: 20px 0;
    }
    
    p {
      margin: 10px 0;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .history-test {
    h3 {
      margin-bottom: 20px;
      color: #303133;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .test-content {
    .time-points {
      flex-direction: column;
    }
    
    .time-point {
      min-width: auto;
      width: 100%;
    }
  }
}
</style>
