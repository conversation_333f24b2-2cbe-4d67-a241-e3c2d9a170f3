import type { ReviewPlan, ReviewRating, ReviewRecord, ReviewStatus } from '@/types'
import { EBBINGHAUS_INTERVALS } from '@/types'
import { mockTasks } from './taskData'

// 生成复习时间
function generateReviewTime(startTime: string, intervalId: number): string {
  const start = new Date(startTime)
  const interval = EBBINGHAUS_INTERVALS.find(i => i.id === intervalId)
  if (!interval) {return startTime}

  const reviewTime = new Date(start)
  reviewTime.setMinutes(reviewTime.getMinutes() + interval.minutes)
  reviewTime.setHours(reviewTime.getHours() + interval.hours)
  reviewTime.setDate(reviewTime.getDate() + interval.days)

  return reviewTime.toISOString()
}

// 生成复习记录
function generateReviewRecords(taskId: string, startTime: string): ReviewRecord[] {
  const records: ReviewRecord[] = []
  const now = new Date()

  for (let i = 1; i <= 9; i++) {
    const scheduledTime = generateReviewTime(startTime, i)
    const scheduled = new Date(scheduledTime)
    
    let status: ReviewStatus = 'scheduled'
    let actualTime: string | undefined
    let rating: ReviewRating | undefined
    let duration: number | undefined
    let notes: string | undefined

    // 模拟已完成的复习（过去的时间点）
    if (scheduled < now) {
      if (Math.random() > 0.1) { // 90%概率完成
        status = 'completed'
        actualTime = new Date(scheduled.getTime() + Math.random() * 3600000).toISOString() // 1小时内完成
        rating = Math.floor(Math.random() * 5) + 1 as ReviewRating
        duration = Math.floor(Math.random() * 30) + 10 // 10-40分钟
        
        // 根据评分生成笔记
        const noteTemplates = {
          1: '复习效果不佳，需要重新学习基础概念',
          2: '部分内容还不够熟练，需要加强练习',
          3: '基本掌握，但还需要巩固',
          4: '掌握良好，理解比较深入',
          5: '完全掌握，可以灵活运用'
        }
        notes = noteTemplates[rating]
      } else {
        status = Math.random() > 0.5 ? 'overdue' : 'skipped'
      }
    }

    records.push({
      id: `review_${taskId}_${i}`,
      taskId,
      intervalId: i,
      scheduledTime,
      actualTime,
      status,
      rating,
      notes,
      duration,
      createdAt: startTime,
      updatedAt: actualTime || startTime
    })
  }

  return records
}

// 生成复习计划
function generateReviewPlan(taskId: string): ReviewPlan {
  const task = mockTasks.find(t => t.id === taskId)
  if (!task) {throw new Error(`Task ${taskId} not found`)}

  // 模拟开始学习时间（1-7天前）
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - Math.floor(Math.random() * 7) - 1)
  const startTimeStr = startTime.toISOString()

  const reviews = generateReviewRecords(taskId, startTimeStr)
  const completedCount = reviews.filter(r => r.status === 'completed').length
  const nextReview = reviews.find(r => r.status === 'scheduled')

  return {
    id: `plan_${taskId}`,
    taskId,
    task,
    startTime: startTimeStr,
    reviews,
    completedCount,
    totalCount: 9,
    nextReviewTime: nextReview?.scheduledTime,
    isCompleted: completedCount === 9,
    createdAt: startTimeStr,
    updatedAt: new Date().toISOString()
  }
}

// Mock复习计划数据
export const mockReviewPlans: ReviewPlan[] = [
  generateReviewPlan('task_1'), // 英语单词 Unit 3
  generateReviewPlan('task_2'), // 数学二次函数练习
  generateReviewPlan('task_3'), // 物理光学实验报告
  generateReviewPlan('task_4'), // 历史明朝政治制度
  generateReviewPlan('task_5')  // 化学酸碱中和反应
]

// 获取所有复习计划
export function getReviewPlans(): ReviewPlan[] {
  return mockReviewPlans
}

// 根据任务ID获取复习计划
export function getReviewPlanByTaskId(taskId: string): ReviewPlan | undefined {
  return mockReviewPlans.find(plan => plan.taskId === taskId)
}

// 获取今日复习任务
export function getTodayReviews(): ReviewRecord[] {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  const todayReviews: ReviewRecord[] = []
  
  mockReviewPlans.forEach(plan => {
    plan.reviews.forEach(review => {
      const reviewDate = new Date(review.scheduledTime)
      if (reviewDate >= today && reviewDate < tomorrow && review.status === 'scheduled') {
        todayReviews.push(review)
      }
    })
  })

  return todayReviews.sort((a, b) => 
    new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
  )
}

// 更新复习记录
export function updateReviewRecord(
  taskId: string, 
  intervalId: number, 
  updates: Partial<ReviewRecord>
): boolean {
  const plan = mockReviewPlans.find(p => p.taskId === taskId)
  if (!plan) {return false}

  const review = plan.reviews.find(r => r.intervalId === intervalId)
  if (!review) {return false}

  Object.assign(review, updates, { updatedAt: new Date().toISOString() })
  
  // 更新计划的完成数量
  plan.completedCount = plan.reviews.filter(r => r.status === 'completed').length
  plan.isCompleted = plan.completedCount === 9
  
  // 更新下次复习时间
  const nextReview = plan.reviews.find(r => r.status === 'scheduled')
  plan.nextReviewTime = nextReview?.scheduledTime

  return true
}
