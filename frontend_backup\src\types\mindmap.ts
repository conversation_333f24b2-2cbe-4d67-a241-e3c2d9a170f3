// 思维导图相关类型定义

export interface MindMapNode {
  id: string
  text: string
  x: number
  y: number
  color: string
  level: number
  parentId?: string
  icon?: string
  notes?: string
  width?: number
  height?: number
  fontSize?: number
  fontWeight?: string
  borderWidth?: number
  borderColor?: string
  backgroundColor?: string
  shape?: 'rectangle' | 'circle' | 'ellipse' | 'diamond'
  collapsed?: boolean
  children?: MindMapNode[]
}

export interface MindMapEdge {
  id: string
  source: string
  target: string
  color?: string
  width?: number
  style?: 'solid' | 'dashed' | 'dotted'
  animated?: boolean
}

export interface MindMap {
  id: string
  title: string
  description: string
  nodes: MindMapNode[]
  edges: MindMapEdge[]
  layout: 'tree' | 'radial' | 'force' | 'hierarchical'
  nodeCount: number
  createdAt: string
  updatedAt: string
  settings?: MindMapSettings
  thumbnail?: string
  tags?: string[]
  isPublic?: boolean
  collaborators?: string[]
}

export interface MindMapSettings {
  // 画布设置
  canvasWidth?: number
  canvasHeight?: number
  backgroundColor?: string
  gridEnabled?: boolean
  gridSize?: number
  gridColor?: string
  
  // 节点默认样式
  defaultNodeColor?: string
  defaultNodeShape?: 'rectangle' | 'circle' | 'ellipse' | 'diamond'
  defaultNodeSize?: { width: number; height: number }
  defaultFontSize?: number
  defaultFontWeight?: string
  defaultBorderWidth?: number
  defaultBorderColor?: string
  
  // 连线默认样式
  defaultEdgeColor?: string
  defaultEdgeWidth?: number
  defaultEdgeStyle?: 'solid' | 'dashed' | 'dotted'
  
  // 布局设置
  nodeSpacing?: number
  levelSpacing?: number
  autoLayout?: boolean
  
  // 交互设置
  enableDrag?: boolean
  enableZoom?: boolean
  enablePan?: boolean
  enableSelection?: boolean
  enableMultiSelect?: boolean
  
  // 动画设置
  animationEnabled?: boolean
  animationDuration?: number
  
  // 导出设置
  exportFormat?: 'png' | 'jpg' | 'svg' | 'pdf'
  exportQuality?: number
}

export interface MindMapTemplate {
  id: string
  name: string
  description: string
  preview: string
  nodes: Omit<MindMapNode, 'id'>[]
  settings?: Partial<MindMapSettings>
  category: 'education' | 'business' | 'personal' | 'project'
}

export interface MindMapHistory {
  id: string
  mindMapId: string
  action: 'create' | 'update' | 'delete' | 'move' | 'style'
  timestamp: string
  data: any
  description: string
}

export interface MindMapExportOptions {
  format: 'png' | 'jpg' | 'svg' | 'pdf' | 'json' | 'xml'
  quality?: number
  width?: number
  height?: number
  includeBackground?: boolean
  onlySelected?: boolean
  transparent?: boolean
}

export interface MindMapImportOptions {
  format: 'json' | 'xml' | 'xmind' | 'freemind'
  mergeMode?: 'replace' | 'append' | 'merge'
  preserveIds?: boolean
  preserveStyles?: boolean
}

export interface MindMapSearchResult {
  nodeId: string
  text: string
  path: string[]
  relevance: number
}

export interface MindMapStatistics {
  totalNodes: number
  totalLevels: number
  averageChildrenPerNode: number
  mostUsedColors: string[]
  creationDate: string
  lastModified: string
  totalEditTime: number
  collaboratorCount: number
}

// 思维导图操作类型
export type MindMapOperation = 
  | { type: 'ADD_NODE'; payload: { node: MindMapNode; parentId?: string } }
  | { type: 'UPDATE_NODE'; payload: { nodeId: string; updates: Partial<MindMapNode> } }
  | { type: 'DELETE_NODE'; payload: { nodeId: string } }
  | { type: 'MOVE_NODE'; payload: { nodeId: string; x: number; y: number } }
  | { type: 'ADD_EDGE'; payload: { edge: MindMapEdge } }
  | { type: 'UPDATE_EDGE'; payload: { edgeId: string; updates: Partial<MindMapEdge> } }
  | { type: 'DELETE_EDGE'; payload: { edgeId: string } }
  | { type: 'UPDATE_LAYOUT'; payload: { layout: MindMap['layout'] } }
  | { type: 'UPDATE_SETTINGS'; payload: { settings: Partial<MindMapSettings> } }

// 思维导图事件类型
export interface MindMapEvents {
  'node:click': (node: MindMapNode, event: MouseEvent) => void
  'node:dblclick': (node: MindMapNode, event: MouseEvent) => void
  'node:contextmenu': (node: MindMapNode, event: MouseEvent) => void
  'node:drag': (node: MindMapNode, event: MouseEvent) => void
  'node:dragend': (node: MindMapNode, event: MouseEvent) => void
  'edge:click': (edge: MindMapEdge, event: MouseEvent) => void
  'canvas:click': (event: MouseEvent) => void
  'canvas:contextmenu': (event: MouseEvent) => void
  'selection:change': (selectedNodes: MindMapNode[], selectedEdges: MindMapEdge[]) => void
  'zoom:change': (scale: number) => void
  'layout:change': (layout: MindMap['layout']) => void
}

// 思维导图渲染器接口
export interface MindMapRenderer {
  render(mindMap: MindMap, container: HTMLElement): void
  updateNode(nodeId: string, updates: Partial<MindMapNode>): void
  updateEdge(edgeId: string, updates: Partial<MindMapEdge>): void
  setLayout(layout: MindMap['layout']): void
  zoom(scale: number): void
  pan(x: number, y: number): void
  fitToView(): void
  exportImage(options: MindMapExportOptions): Promise<string | Blob>
  destroy(): void
}

// 思维导图布局算法接口
export interface MindMapLayoutAlgorithm {
  name: string
  calculate(nodes: MindMapNode[], edges: MindMapEdge[], settings: MindMapSettings): {
    nodes: MindMapNode[]
    edges: MindMapEdge[]
  }
}

// 思维导图插件接口
export interface MindMapPlugin {
  name: string
  version: string
  install(mindMap: any): void
  uninstall(mindMap: any): void
}

// 思维导图主题
export interface MindMapTheme {
  id: string
  name: string
  description: string
  colors: {
    primary: string
    secondary: string
    background: string
    text: string
    border: string
    accent: string[]
  }
  fonts: {
    primary: string
    secondary: string
    sizes: {
      small: number
      medium: number
      large: number
    }
  }
  shapes: {
    node: 'rectangle' | 'circle' | 'ellipse' | 'diamond'
    edge: 'solid' | 'dashed' | 'dotted'
  }
  spacing: {
    node: number
    level: number
    edge: number
  }
}

// 思维导图协作相关
export interface MindMapCollaboration {
  id: string
  mindMapId: string
  userId: string
  userName: string
  userAvatar?: string
  role: 'owner' | 'editor' | 'viewer'
  permissions: {
    read: boolean
    write: boolean
    delete: boolean
    share: boolean
    export: boolean
  }
  lastActive: string
  cursor?: {
    x: number
    y: number
    nodeId?: string
  }
}

export interface MindMapComment {
  id: string
  mindMapId: string
  nodeId?: string
  userId: string
  userName: string
  userAvatar?: string
  content: string
  createdAt: string
  updatedAt?: string
  replies?: MindMapComment[]
  resolved?: boolean
}

// 思维导图快捷键配置
export interface MindMapKeyboardShortcuts {
  addNode: string
  deleteNode: string
  editNode: string
  copyNode: string
  pasteNode: string
  undoAction: string
  redoAction: string
  selectAll: string
  zoomIn: string
  zoomOut: string
  fitToView: string
  save: string
  export: string
}

// 思维导图配置
export interface MindMapConfig {
  theme: MindMapTheme
  settings: MindMapSettings
  shortcuts: MindMapKeyboardShortcuts
  plugins: MindMapPlugin[]
  templates: MindMapTemplate[]
}
