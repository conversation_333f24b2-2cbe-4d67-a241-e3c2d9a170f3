<template>
  <div class="review-history">
    <ElCard class="history-card">
      <template #header>
        <div class="card-header">
          <ElIcon><TrendCharts /></ElIcon>
          <span>复习历史与统计</span>
        </div>
      </template>
      
      <div class="history-content">
        <!-- 统计概览 -->
        <div class="stats-overview">
          <div class="stat-item">
            <div class="stat-value">{{ progress.currentLevel }}/9</div>
            <div class="stat-label">复习进度</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ progress.averageQuality.toFixed(1) }}</div>
            <div class="stat-label">平均评分</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ progress.consecutiveCorrect }}</div>
            <div class="stat-label">连续正确</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ progress.masteryLevel }}</div>
            <div class="stat-label">掌握程度</div>
          </div>
        </div>
        
        <!-- 复习策略建议 -->
        <div class="strategy-section">
          <h4>复习策略建议</h4>
          <div class="strategy-card">
            <div class="strategy-header">
              <ElTag :type="getStrategyTagType(strategy.strategy)">
                {{ strategy.strategy }}
              </ElTag>
              <span class="strategy-reason">{{ strategy.reason }}</span>
            </div>
            <ul class="strategy-suggestions">
              <li v-for="(suggestion, index) in strategy.suggestions" :key="index">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 复习记录时间轴 -->
        <div class="timeline-section">
          <h4>复习记录</h4>
          <div v-if="sessions.length === 0" class="empty-timeline">
            <ElEmpty description="暂无复习记录" />
          </div>
          <ElTimeline v-else>
            <ElTimelineItem
              v-for="(session, index) in sessions"
              :key="index"
              :timestamp="formatTimestamp(session.timestamp)"
              :type="getTimelineType(session.quality)"
              placement="top"
            >
              <div class="timeline-content">
                <div class="session-header">
                  <span class="session-title">第{{ session.reviewIndex + 1 }}次复习</span>
                  <div class="session-rating">
                    <ElRate
                      :model-value="session.quality"
                      disabled
                      :max="5"
                      size="small"
                    />
                    <span class="rating-text">{{ getRatingText(session.quality) }}</span>
                  </div>
                </div>
                <div class="session-details">
                  <div class="detail-item">
                    <ElIcon><Clock /></ElIcon>
                    <span>难度：{{ getDifficultyText(session.difficulty) }}</span>
                  </div>
                </div>
              </div>
            </ElTimelineItem>
          </ElTimeline>
        </div>
        
        <!-- 艾宾浩斯时间点进度 -->
        <div class="ebbinghaus-section">
          <h4>艾宾浩斯复习进度</h4>
          <div class="time-points">
            <div
              v-for="(point, index) in timePoints"
              :key="index"
              :class="[
                'time-point',
                {
                  'completed': index < progress.currentLevel,
                  'current': index === progress.currentLevel,
                  'pending': index > progress.currentLevel
                }
              ]"
            >
              <div class="point-indicator">
                <ElIcon v-if="index < progress.currentLevel">
                  <Check />
                </ElIcon>
                <ElIcon v-else-if="index === progress.currentLevel">
                  <Clock />
                </ElIcon>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="point-info">
                <div class="point-name">{{ point.name }}</div>
                <div class="point-description">{{ point.description }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 下次复习预告 -->
        <div v-if="progress.currentLevel < 9" class="next-review-section">
          <h4>下次复习</h4>
          <div class="next-review-card">
            <div class="next-review-time">
              <ElIcon><Clock /></ElIcon>
              <span>{{ getNextReviewText() }}</span>
            </div>
            <div class="next-review-interval">
              预计间隔：{{ algorithm.formatInterval(progress.nextInterval) }}
            </div>
          </div>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElCard, ElEmpty, ElIcon, ElRate, ElTag, ElTimeline, ElTimelineItem } from 'element-plus'
import { 
  Check, 
  Clock, 
  TrendCharts
} from '@element-plus/icons-vue'
import { EbbinghausAlgorithm, type ReviewSession } from '@/services/EbbinghausAlgorithm'

interface Props {
  sessions: ReviewSession[]
  taskId: string
}

const props = defineProps<Props>()

// 算法实例
const algorithm = new EbbinghausAlgorithm()

// 计算属性
const progress = computed(() => algorithm.getProgress(props.sessions))
const strategy = computed(() => algorithm.getReviewStrategy(props.sessions))
const timePoints = computed(() => algorithm.getStandardTimePoints())

// 方法
const getStrategyTagType = (strategy: string) => {
  const typeMap: Record<string, string> = {
    '重点复习': 'danger',
    '稳固复习': 'warning',
    '常规复习': 'primary',
    '维持复习': 'success',
    '开始学习': 'info'
  }
  return typeMap[strategy] || 'default'
}

const getTimelineType = (quality: number) => {
  if (quality >= 4) {return 'success'}
  if (quality >= 3) {return 'primary'}
  if (quality >= 2) {return 'warning'}
  return 'danger'
}

const getRatingText = (quality: number) => {
  const texts = ['', '很差', '较差', '一般', '良好', '很好']
  return texts[quality] || ''
}

const getDifficultyText = (difficulty: number) => {
  const texts = ['', '很简单', '简单', '一般', '困难', '很困难']
  return texts[difficulty] || ''
}

const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getNextReviewText = () => {
  if (progress.value.currentLevel >= 9) {
    return '已完成所有复习'
  }
  
  const nextTime = new Date(Date.now() + progress.value.nextInterval)
  return nextTime.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.review-history {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #303133;
  }
}

.history-content {
  .stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    
    .stat-item {
      text-align: center;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .strategy-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .strategy-card {
      padding: 16px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 8px;
      
      .strategy-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .strategy-reason {
          color: #606266;
          font-size: 14px;
        }
      }
      
      .strategy-suggestions {
        margin: 0;
        padding-left: 20px;
        
        li {
          color: #606266;
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .timeline-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .timeline-content {
      .session-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .session-title {
          font-weight: 500;
          color: #303133;
        }
        
        .session-rating {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .rating-text {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .session-details {
        display: flex;
        gap: 16px;
        
        .detail-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }
  
  .ebbinghaus-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .time-points {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }
    
    .time-point {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid #e4e7ed;
      background: #fff;
      
      &.completed {
        background: #f0f9ff;
        border-color: #409eff;
        
        .point-indicator {
          background: #409eff;
          color: white;
        }
      }
      
      &.current {
        background: #fff7e6;
        border-color: #e6a23c;
        
        .point-indicator {
          background: #e6a23c;
          color: white;
        }
      }
      
      &.pending {
        .point-indicator {
          background: #f5f7fa;
          color: #909399;
          border: 1px solid #dcdfe6;
        }
      }
      
      .point-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        font-size: 12px;
        font-weight: 500;
      }
      
      .point-info {
        .point-name {
          font-size: 12px;
          font-weight: 500;
          color: #303133;
        }
        
        .point-description {
          font-size: 11px;
          color: #909399;
        }
      }
    }
  }
  
  .next-review-section {
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .next-review-card {
      padding: 16px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 8px;
      
      .next-review-time {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #409eff;
        margin-bottom: 8px;
      }
      
      .next-review-interval {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>
