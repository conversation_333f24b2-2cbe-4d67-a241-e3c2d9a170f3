# [DES-API-001] API接口设计

## 📋 概述

本文档定义了艾宾浩斯记忆曲线学习管理系统的所有API接口，包括请求格式、响应格式、错误处理和安全机制。

## 🔧 API设计原则

### [DES-API-PRINCIPLE-001] RESTful设计原则
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 资源导向的URL设计
- 统一的响应格式
- 合理的HTTP状态码使用

### [DES-API-PRINCIPLE-002] 安全设计原则
- JWT Token认证
- 请求频率限制
- 输入数据验证
- 敏感信息加密

## 📊 通用响应格式

### [DES-API-RESPONSE-001] 标准响应格式
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2025-01-31T10:00:00.000Z",
  "requestId": "uuid-string"
}
```

### [DES-API-ERROR-001] 错误响应格式
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2025-01-31T10:00:00.000Z",
  "requestId": "uuid-string"
}
```

## 🔧 任务管理API

### [DES-API-001] 创建学习任务
**设计ID**：DES-API-001  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/tasks  
**实现需求**：[REQ-FUNC-001]

**请求格式**：
```json
{
  "title": "string(1-100字符)",
  "content": "string(1-5000字符)",
  "subject": "enum(学科枚举)",
  "estimatedTime": "number(1-300分钟，可选)",
  "priority": "number(1-5，可选)",
  "difficulty": "number(1-5，可选)",
  "tags": ["string"]
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "taskId": "uuid-string",
    "reviewSchedule": [
      {
        "reviewTime": "2025-01-31T10:05:00.000Z",
        "reviewIndex": 1,
        "status": "scheduled"
      }
    ],
    "loadWarning": {
      "level": "heavy",
      "message": "当日学习负载较重",
      "suggestions": ["建议调整到明天"]
    }
  }
}
```

**错误码定义**：
- [ERR-001] INVALID_INPUT：输入数据验证失败
- [ERR-002] TITLE_DUPLICATE：任务标题重复
- [ERR-003] LOAD_EXCEEDED：学习负载超限

### [DES-API-002] 负载均衡检查
**设计ID**：DES-API-002  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/tasks/load-check  
**实现需求**：[REQ-FUNC-003]

**请求格式**：
```json
{
  "targetDate": "2025-02-01",
  "newTaskDuration": 60,
  "userDailyLimit": 120
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "loadLevel": "heavy",
    "currentLoad": 100,
    "suggestions": [
      "建议将任务调整到2025-02-02",
      "或者减少任务预估时间"
    ],
    "alternativeDates": [
      "2025-02-02",
      "2025-02-03"
    ]
  }
}
```

### [DES-API-003] 获取任务列表
**设计ID**：DES-API-003  
**接口类型**：REST API  
**请求方法**：GET  
**接口路径**：/api/tasks  
**实现需求**：[REQ-FUNC-005]

**请求参数**：
```
?subject=english&status=pending&page=1&limit=20&sortBy=createdAt&sortOrder=desc
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "taskId": "uuid-string",
        "title": "英语单词 Unit 3",
        "subject": "english",
        "status": "pending",
        "createdAt": "2025-01-31T10:00:00.000Z",
        "estimatedTime": 30,
        "priority": 3,
        "difficulty": 2
      }
    ],
    "totalCount": 100,
    "pagination": {
      "page": 1,
      "limit": 20,
      "totalPages": 5
    }
  }
}
```

### [DES-API-004] 更新任务状态
**设计ID**：DES-API-004  
**接口类型**：REST API  
**请求方法**：PUT  
**接口路径**：/api/tasks/{taskId}/status  
**实现需求**：[REQ-FUNC-006]

**请求格式**：
```json
{
  "status": "completed",
  "completedAt": "2025-01-31T11:00:00.000Z",
  "actualTime": 35,
  "notes": "学习笔记内容"
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "taskId": "uuid-string",
    "status": "completed",
    "updatedAt": "2025-01-31T11:00:00.000Z"
  }
}
```

## 🧠 复习管理API

### [DES-API-005] 执行复习任务
**设计ID**：DES-API-005  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/reviews/{reviewId}/execute  
**实现需求**：[REQ-FUNC-006]

**请求格式**：
```json
{
  "startTime": "2025-01-31T10:00:00.000Z",
  "endTime": "2025-01-31T10:30:00.000Z",
  "effectiveness": 4,
  "notes": "复习效果很好，记忆清晰"
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "reviewRecord": {
      "reviewId": "uuid-string",
      "actualTime": 30,
      "effectiveness": 4,
      "completedAt": "2025-01-31T10:30:00.000Z"
    },
    "nextReviewTime": "2025-02-03T10:00:00.000Z",
    "adjustedSchedule": [
      {
        "reviewTime": "2025-02-03T10:00:00.000Z",
        "reviewIndex": 5,
        "status": "scheduled"
      }
    ]
  }
}
```

### [DES-API-006] 获取复习提醒
**设计ID**：DES-API-006  
**接口类型**：REST API  
**请求方法**：GET  
**接口路径**：/api/reviews/reminders  
**实现需求**：[REQ-FUNC-004]

**请求参数**：
```
?date=2025-01-31&upcoming=true
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "reminders": [
      {
        "reviewId": "uuid-string",
        "taskTitle": "英语单词 Unit 3",
        "reviewTime": "2025-01-31T10:05:00.000Z",
        "reviewIndex": 1,
        "isOverdue": false
      }
    ],
    "totalCount": 5
  }
}
```

## ⏰ 时间管理API

### [DES-API-007] 智能时间预估
**设计ID**：DES-API-007  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/time/estimate  
**实现需求**：[REQ-FUNC-007]

**请求格式**：
```json
{
  "taskContent": "英语单词学习内容",
  "subject": "english",
  "difficulty": 2,
  "contentLength": 500
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "estimatedTime": 30,
    "confidence": 0.85,
    "factors": [
      "基于个人英语学习效率",
      "内容长度适中",
      "难度等级为2"
    ]
  }
}
```

## 🎨 思维导图API

### [DES-API-008] 创建思维导图
**设计ID**：DES-API-008  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/mindmaps  
**实现需求**：[REQ-FUNC-009]

**请求格式**：
```json
{
  "title": "数学知识体系",
  "centerNode": {
    "id": "center",
    "label": "数学",
    "style": {
      "color": "#ff6b6b",
      "shape": "ellipse"
    }
  },
  "nodes": [
    {
      "id": "node1",
      "label": "代数",
      "parentId": "center",
      "position": { "x": 100, "y": 50 }
    }
  ],
  "edges": [
    {
      "id": "edge1",
      "source": "center",
      "target": "node1"
    }
  ]
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "mindMapId": "uuid-string",
    "title": "数学知识体系",
    "createdAt": "2025-01-31T10:00:00.000Z",
    "nodeCount": 2,
    "edgeCount": 1
  }
}
```

## 🔒 认证授权API

### [DES-API-009] 用户登录
**设计ID**：DES-API-009  
**接口类型**：REST API  
**请求方法**：POST  
**接口路径**：/api/auth/login

**请求格式**：
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-string",
    "user": {
      "userId": "uuid-string",
      "username": "<EMAIL>",
      "profile": {
        "nickname": "学习者",
        "avatar": "avatar-url"
      }
    },
    "expiresIn": 86400
  }
}
```

## 📊 错误码汇总

| 错误码 | 错误信息 | HTTP状态码 | 说明 |
|--------|----------|------------|------|
| ERR-001 | INVALID_INPUT | 400 | 输入数据验证失败 |
| ERR-002 | TITLE_DUPLICATE | 409 | 任务标题重复 |
| ERR-003 | LOAD_EXCEEDED | 422 | 学习负载超限 |
| ERR-004 | UNAUTHORIZED | 401 | 未授权访问 |
| ERR-005 | FORBIDDEN | 403 | 权限不足 |
| ERR-006 | NOT_FOUND | 404 | 资源不存在 |
| ERR-007 | RATE_LIMITED | 429 | 请求频率超限 |
| ERR-008 | SERVER_ERROR | 500 | 服务器内部错误 |

## 🔗 相关文档

- [功能需求规格](../01-需求分析/02-功能需求规格.md)
- [数据模型设计](./09-数据模型设计.md)
- [系统架构设计](./01-系统整体架构设计.md)

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：后端工程师  
**审核人**：系统架构师  
**状态**：草稿
