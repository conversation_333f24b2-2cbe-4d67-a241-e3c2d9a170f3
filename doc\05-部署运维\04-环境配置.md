# [CONFIG-ENV-001] 环境配置

## 📋 概述

本文档详细说明了艾宾浩斯记忆曲线学习管理系统在不同环境下的配置要求和配置方法。

## 🏗️ 环境分类

### [CONFIG-ENV-001] 开发环境
**用途**：本地开发和调试  
**特点**：快速启动、热更新、详细日志  
**数据库**：本地MongoDB/MySQL  
**缓存**：本地Redis或内存缓存

### [CONFIG-ENV-002] 测试环境
**用途**：功能测试和集成测试  
**特点**：模拟生产环境、测试数据隔离  
**数据库**：独立测试数据库  
**缓存**：独立Redis实例

### [CONFIG-ENV-003] 生产环境
**用途**：正式运行环境  
**特点**：高可用、高性能、安全性  
**数据库**：生产数据库集群  
**缓存**：Redis集群

## ⚙️ 配置文件说明

### [CONFIG-FILE-001] 环境变量配置
```bash
# .env.development (开发环境)
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_dev
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=ebbinghaus_dev
MYSQL_USERNAME=root
MYSQL_PASSWORD=password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=dev-secret-key
JWT_EXPIRES_IN=7d

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

```bash
# .env.production (生产环境)
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置
MONGODB_URI=**********************************************************************************
MYSQL_HOST=mysql-cluster
MYSQL_PORT=3306
MYSQL_DATABASE=ebbinghaus_prod
MYSQL_USERNAME=app_user
MYSQL_PASSWORD=secure_password

# Redis配置
REDIS_HOST=redis-cluster
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT配置
JWT_SECRET=super-secure-secret-key-change-in-production
JWT_EXPIRES_IN=24h

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/ebbinghaus/app.log

# 文件上传配置
UPLOAD_PATH=/var/uploads
MAX_FILE_SIZE=10485760

# 安全配置
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### [CONFIG-FILE-002] 数据库配置

#### MongoDB配置
```javascript
// config/database.js
const mongoose = require('mongoose');

const connectMongoDB = async () => {
  try {
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    };

    if (process.env.NODE_ENV === 'production') {
      options.ssl = true;
      options.sslValidate = true;
    }

    await mongoose.connect(process.env.MONGODB_URI, options);
    console.log('MongoDB连接成功');
  } catch (error) {
    console.error('MongoDB连接失败:', error);
    process.exit(1);
  }
};

module.exports = { connectMongoDB };
```

#### MySQL配置
```javascript
// config/mysql.js
const mysql = require('mysql2/promise');

const createMySQLPool = () => {
  const pool = mysql.createPool({
    host: process.env.MYSQL_HOST,
    port: process.env.MYSQL_PORT,
    user: process.env.MYSQL_USERNAME,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true
  });

  return pool;
};

module.exports = { createMySQLPool };
```

### [CONFIG-FILE-003] Redis配置
```javascript
// config/redis.js
const Redis = require('ioredis');

const createRedisClient = () => {
  const options = {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true
  };

  if (process.env.NODE_ENV === 'production') {
    options.tls = {};
  }

  const redis = new Redis(options);

  redis.on('connect', () => {
    console.log('Redis连接成功');
  });

  redis.on('error', (err) => {
    console.error('Redis连接错误:', err);
  });

  return redis;
};

module.exports = { createRedisClient };
```

## 🔧 应用配置

### [CONFIG-APP-001] 服务器配置
```javascript
// config/server.js
module.exports = {
  development: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    cors: {
      origin: ['http://localhost:8080', 'http://localhost:3000'],
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000 // 每个IP最多1000次请求
    }
  },
  production: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0',
    cors: {
      origin: process.env.CORS_ORIGIN,
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100 // 每个IP最多100次请求
    }
  }
};
```

### [CONFIG-APP-002] 日志配置
```javascript
// config/logger.js
const winston = require('winston');

const createLogger = () => {
  const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    defaultMeta: { service: 'ebbinghaus-app' },
    transports: [
      new winston.transports.File({ 
        filename: process.env.LOG_FILE || './logs/app.log' 
      })
    ]
  });

  if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
      format: winston.format.simple()
    }));
  }

  return logger;
};

module.exports = { createLogger };
```

## 🔒 安全配置

### [CONFIG-SECURITY-001] JWT配置
```javascript
// config/auth.js
module.exports = {
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    algorithm: 'HS256'
  },
  bcrypt: {
    saltRounds: 12
  },
  session: {
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
  }
};
```

### [CONFIG-SECURITY-002] 文件上传配置
```javascript
// config/upload.js
const multer = require('multer');
const path = require('path');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, process.env.UPLOAD_PATH || './uploads');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'audio/mpeg', 'audio/wav'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  }
});

module.exports = { upload };
```

## 📋 配置检查清单

### 开发环境配置检查
- [ ] Node.js版本 >= 18
- [ ] MongoDB/MySQL连接正常
- [ ] Redis连接正常
- [ ] 环境变量配置完整
- [ ] 日志目录可写
- [ ] 上传目录可写

### 生产环境配置检查
- [ ] 所有密钥已更换为安全值
- [ ] 数据库连接使用加密
- [ ] Redis连接使用密码
- [ ] CORS配置正确
- [ ] 日志级别设置为info
- [ ] 文件权限配置正确
- [ ] 防火墙规则配置
- [ ] SSL证书配置

## 🔗 相关文档

- [部署指南](./03-部署指南.md)
- [系统架构设计](../02-系统设计/01-系统整体架构设计.md)
- [测试计划](./01-测试计划.md)

---

**文档版本**：v1.0  
**创建时间**：2025-01-31  
**负责人**：运维工程师  
**审核人**：项目经理  
**状态**：草稿
