<template>
  <div class="mobile-navigation">
    <!-- 顶部导航栏 -->
    <div class="mobile-header safe-area-top">
      <div class="header-content">
        <div class="header-left">
          <el-button
            v-if="showBack"
            type="text"
            :icon="ArrowLeft"
            class="back-button"
            @click="handleBack"
          />
          <h1 class="page-title">{{ title }}</h1>
        </div>
        
        <div class="header-right">
          <slot name="header-actions">
            <el-button
              type="text"
              :icon="Menu"
              class="menu-button"
              @click="showDrawer = true"
            />
          </slot>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="mobile-bottom-nav safe-area-bottom">
      <div class="nav-items">
        <div
          v-for="item in navItems"
          :key="item.path"
          class="nav-item"
          :class="{ active: isActive(item.path) }"
          @click="navigateTo(item.path)"
        >
          <el-icon class="nav-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="nav-label">{{ item.label }}</span>
          <div v-if="item.badge" class="nav-badge">{{ item.badge }}</div>
        </div>
      </div>
    </div>

    <!-- 侧边抽屉菜单 -->
    <el-drawer
      v-model="showDrawer"
      direction="rtl"
      size="280px"
      :with-header="false"
      class="mobile-drawer"
    >
      <div class="drawer-content">
        <div class="drawer-header">
          <div class="user-info">
            <el-avatar :size="60" :src="userAvatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details">
              <h3>{{ userName || '未登录' }}</h3>
              <p>{{ userEmail || '请先登录' }}</p>
            </div>
          </div>
        </div>

        <div class="drawer-menu">
          <div
            v-for="item in menuItems"
            :key="item.path"
            class="menu-item"
            @click="handleMenuClick(item)"
          >
            <el-icon class="menu-icon">
              <component :is="item.icon" />
            </el-icon>
            <span class="menu-label">{{ item.label }}</span>
            <el-icon v-if="item.children" class="menu-arrow">
              <ArrowRight />
            </el-icon>
          </div>
        </div>

        <div class="drawer-footer">
          <div class="footer-actions">
            <el-button link :icon="Setting" @click="openSettings">
              设置
            </el-button>
            <el-button link :icon="QuestionFilled" @click="openHelp">
              帮助
            </el-button>
            <el-button link :icon="SwitchButton" @click="logout">
              退出
            </el-button>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 浮动操作按钮 -->
    <div v-if="showFab" class="floating-action-button" @click="handleFabClick">
      <el-icon class="fab-icon">
        <component :is="fabIcon" />
      </el-icon>
    </div>

    <!-- 快速操作面板 -->
    <el-drawer
      v-model="showQuickActions"
      direction="btt"
      size="50%"
      :with-header="false"
      class="quick-actions-drawer"
    >
      <div class="quick-actions-content">
        <div class="quick-actions-header">
          <h3>快速操作</h3>
          <el-button type="text" :icon="Close" @click="showQuickActions = false" />
        </div>
        
        <div class="quick-actions-grid">
          <div
            v-for="action in quickActions"
            :key="action.key"
            class="quick-action-item"
            @click="handleQuickAction(action)"
          >
            <div class="action-icon" :style="{ backgroundColor: action.color }">
              <el-icon>
                <component :is="action.icon" />
              </el-icon>
            </div>
            <span class="action-label">{{ action.label }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import {
    ArrowLeft,
    ArrowRight,
    Calendar,
    Close,
    Document,
    Edit,
    HomeFilled,
    Menu,
    Plus,
    QuestionFilled,
    Search,
    Setting,
    Star,
    SwitchButton,
    TrendCharts,
    User
  } from '@element-plus/icons-vue'

  interface NavItem {
    path: string
    label: string
    icon: any
    badge?: number
  }

  interface MenuItem {
    path: string
    label: string
    icon: any
    children?: MenuItem[]
  }

  interface QuickAction {
    key: string
    label: string
    icon: any
    color: string
    action: string
  }

  interface Props {
    title?: string
    showBack?: boolean
    showFab?: boolean
    fabIcon?: any
  }

  interface Emits {
    (e: 'back'): void
    (e: 'fab-click'): void
    (e: 'quick-action', action: QuickAction): void
  }

  withDefaults(defineProps<Props>(), {
    title: '学习助手',
    showBack: false,
    showFab: true,
    fabIcon: Plus
  })

  const emit = defineEmits<Emits>()

  const router = useRouter()
  const route = useRoute()

  // 响应式数据
  const showDrawer = ref(false)
  const showQuickActions = ref(false)

  // 模拟用户数据
  const userAvatar = ref('')
  const userName = ref('学习者')
  const userEmail = ref('<EMAIL>')

  // 底部导航项
  const navItems: NavItem[] = [
    {
      path: '/',
      label: '首页',
      icon: HomeFilled
    },
    {
      path: '/tasks',
      label: '任务',
      icon: Document,
      badge: 3
    },
    {
      path: '/review',
      label: '复习',
      icon: Calendar,
      badge: 5
    },
    {
      path: '/analytics',
      label: '统计',
      icon: TrendCharts
    }
  ]

  // 侧边菜单项
  const menuItems: MenuItem[] = [
    {
      path: '/profile',
      label: '个人资料',
      icon: User
    },
    {
      path: '/tasks',
      label: '任务管理',
      icon: Document
    },
    {
      path: '/review',
      label: '复习计划',
      icon: Calendar
    },
    {
      path: '/analytics',
      label: '学习统计',
      icon: TrendCharts
    },
    {
      path: '/favorites',
      label: '我的收藏',
      icon: Star
    }
  ]

  // 快速操作
  const quickActions: QuickAction[] = [
    {
      key: 'create-task',
      label: '创建任务',
      icon: Plus,
      color: '#409EFF',
      action: 'create-task'
    },
    {
      key: 'quick-review',
      label: '快速复习',
      icon: Calendar,
      color: '#67C23A',
      action: 'quick-review'
    },
    {
      key: 'search',
      label: '搜索',
      icon: Search,
      color: '#E6A23C',
      action: 'search'
    },
    {
      key: 'edit-note',
      label: '记笔记',
      icon: Edit,
      color: '#F56C6C',
      action: 'edit-note'
    }
  ]

  // 计算属性
  const currentPath = computed(() => route.path)

  // 方法
  const isActive = (path: string): boolean => {
    if (path === '/') {
      return currentPath.value === '/'
    }
    return currentPath.value.startsWith(path)
  }

  const navigateTo = (path: string) => {
    if (path !== currentPath.value) {
      router.push(path)
    }
  }

  const handleBack = () => {
    emit('back')
    router.back()
  }

  const handleMenuClick = (item: MenuItem) => {
    showDrawer.value = false
    if (item.children) {
      // 处理子菜单
      return
    }
    navigateTo(item.path)
  }

  const handleFabClick = () => {
    emit('fab-click')
    showQuickActions.value = true
  }

  const handleQuickAction = (action: QuickAction) => {
    showQuickActions.value = false
    emit('quick-action', action)
    
    switch (action.action) {
      case 'create-task':
        router.push('/tasks/create')
        break
      case 'quick-review':
        router.push('/review')
        break
      case 'search':
        // 实现搜索功能
        ElMessage.info('搜索功能开发中...')
        break
      case 'edit-note':
        // 实现笔记功能
        ElMessage.info('笔记功能开发中...')
        break
    }
  }

  const openSettings = () => {
    showDrawer.value = false
    router.push('/settings')
  }

  const openHelp = () => {
    showDrawer.value = false
    ElMessage.info('帮助功能开发中...')
  }

  const logout = () => {
    showDrawer.value = false
    ElMessage.success('已退出登录')
    // 实现退出登录逻辑
  }
</script>

<style scoped>
  .mobile-navigation {
    position: relative;
  }

  /* 顶部导航栏 */
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    backdrop-filter: blur(10px);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    height: 56px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .back-button {
    padding: 8px;
    margin-left: -8px;
  }

  .page-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .menu-button {
    padding: 8px;
    margin-right: -8px;
  }

  /* 底部导航栏 */
  .mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    backdrop-filter: blur(10px);
  }

  .nav-items {
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
  }

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-width: 60px;
  }

  .nav-item:active {
    transform: scale(0.95);
  }

  .nav-item.active .nav-icon {
    color: var(--el-color-primary);
  }

  .nav-item.active .nav-label {
    color: var(--el-color-primary);
    font-weight: 600;
  }

  .nav-icon {
    font-size: 20px;
    color: var(--el-text-color-secondary);
    transition: color 0.3s ease;
  }

  .nav-label {
    font-size: 10px;
    color: var(--el-text-color-secondary);
    transition: color 0.3s ease;
  }

  .nav-badge {
    position: absolute;
    top: 4px;
    right: 8px;
    background: var(--el-color-danger);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 侧边抽屉 */
  .mobile-drawer :deep(.el-drawer__body) {
    padding: 0;
  }

  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .drawer-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .user-details h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }

  .user-details p {
    margin: 0;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .drawer-menu {
    flex: 1;
    padding: 8px 0;
  }

  .menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .menu-item:hover {
    background: var(--el-fill-color-light);
  }

  .menu-item:active {
    background: var(--el-fill-color);
  }

  .menu-icon {
    font-size: 18px;
    color: var(--el-text-color-secondary);
  }

  .menu-label {
    flex: 1;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .menu-arrow {
    font-size: 14px;
    color: var(--el-text-color-placeholder);
  }

  .drawer-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-light);
  }

  .footer-actions {
    display: flex;
    justify-content: space-around;
  }

  /* 浮动操作按钮 */
  .floating-action-button {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--el-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 999;
  }

  .floating-action-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }

  .floating-action-button:active {
    transform: scale(0.95);
  }

  .fab-icon {
    font-size: 24px;
  }

  /* 快速操作面板 */
  .quick-actions-drawer :deep(.el-drawer__body) {
    padding: 0;
  }

  .quick-actions-content {
    padding: 20px;
  }

  .quick-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .quick-actions-header h3 {
    margin: 0;
    color: var(--el-text-color-primary);
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border-radius: 12px;
    background: var(--el-fill-color-light);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .quick-action-item:hover {
    background: var(--el-fill-color);
    transform: translateY(-2px);
  }

  .quick-action-item:active {
    transform: scale(0.95);
  }

  .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }

  .action-label {
    font-size: 14px;
    color: var(--el-text-color-primary);
    text-align: center;
  }

  /* 响应式适配 */
  @media (max-width: 480px) {
    .header-content {
      padding: 12px;
    }

    .page-title {
      font-size: 16px;
    }

    .nav-item {
      padding: 6px 8px;
      min-width: 50px;
    }

    .nav-icon {
      font-size: 18px;
    }

    .nav-label {
      font-size: 9px;
    }

    .floating-action-button {
      width: 48px;
      height: 48px;
      bottom: 70px;
      right: 16px;
    }

    .fab-icon {
      font-size: 20px;
    }

    .quick-actions-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
