/* 移动端适配样式 */

/* 基础响应式断点 */
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;
$small-mobile: 480px;

/* 移动端基础样式 */
@media (max-width: $mobile-breakpoint) {
  body {
    font-size: 14px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  .container {
    padding: 0 12px;
  }

  /* 按钮适配 */
  .el-button {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 8px;
  }

  .el-button--small {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 12px;
  }

  .el-button--large {
    min-height: 48px;
    padding: 14px 20px;
    font-size: 16px;
  }

  /* 表单适配 */
  .el-input__inner {
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
    border-radius: 8px;
    padding: 0 12px;
  }

  .el-textarea__inner {
    font-size: 16px;
    border-radius: 8px;
    padding: 12px;
  }

  .el-select .el-input__inner {
    padding-right: 35px;
  }

  /* 卡片适配 */
  .el-card {
    margin-bottom: 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .el-card__body {
    padding: 16px;
  }

  .el-card__header {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  /* 对话框适配 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto;
    border-radius: 12px;
  }

  .el-dialog__header {
    padding: 20px 20px 10px;
  }

  .el-dialog__body {
    padding: 10px 20px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
  }

  /* 抽屉适配 */
  .el-drawer {
    border-radius: 12px 12px 0 0;
  }

  .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-drawer__body {
    padding: 20px;
  }

  /* 表格适配 */
  .el-table {
    font-size: 12px;
  }

  .el-table th,
  .el-table td {
    padding: 8px 4px;
  }

  .el-table__header-wrapper {
    overflow-x: auto;
  }

  .el-table__body-wrapper {
    overflow-x: auto;
  }

  /* 分页适配 */
  .el-pagination {
    justify-content: center;
    flex-wrap: wrap;
  }

  .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
  }

  /* 标签适配 */
  .el-tag {
    height: 24px;
    line-height: 22px;
    padding: 0 8px;
    font-size: 12px;
    border-radius: 6px;
  }

  .el-tag--small {
    height: 20px;
    line-height: 18px;
    padding: 0 6px;
    font-size: 11px;
  }

  /* 评分组件适配 */
  .el-rate {
    height: auto;
  }

  .el-rate__item {
    margin-right: 4px;
  }

  .el-rate--small .el-rate__icon {
    font-size: 14px;
  }

  /* 步骤条适配 */
  .el-steps {
    flex-direction: column;
  }

  .el-steps--horizontal .el-step__line {
    display: none;
  }

  /* 时间轴适配 */
  .el-timeline {
    padding-left: 20px;
  }

  .el-timeline-item__wrapper {
    padding-left: 20px;
  }

  /* 菜单适配 */
  .el-menu--horizontal {
    flex-direction: column;
  }

  .el-menu--horizontal .el-menu-item {
    height: 48px;
    line-height: 48px;
  }

  /* 导航栏适配 */
  .el-breadcrumb {
    font-size: 12px;
  }

  .el-breadcrumb__item {
    display: inline-block;
  }

  /* 消息提示适配 */
  .el-message {
    min-width: 280px;
    max-width: 90vw;
    margin: 0 5vw;
  }

  .el-notification {
    width: 90vw;
    margin: 0 5vw;
  }

  /* 加载适配 */
  .el-loading-mask {
    border-radius: 12px;
  }

  /* 工具提示适配 */
  .el-tooltip__popper {
    max-width: 250px;
    font-size: 12px;
  }

  /* 气泡确认框适配 */
  .el-popconfirm {
    max-width: 280px;
  }

  /* 日期选择器适配 */
  .el-date-editor {
    width: 100% !important;
  }

  .el-picker-panel {
    width: 90vw;
    max-width: 320px;
  }

  /* 颜色选择器适配 */
  .el-color-picker {
    height: 44px;
  }

  /* 滑块适配 */
  .el-slider {
    margin: 20px 0;
  }

  .el-slider__runway {
    height: 6px;
  }

  .el-slider__button {
    width: 20px;
    height: 20px;
  }

  /* 开关适配 */
  .el-switch {
    height: 24px;
  }

  .el-switch__core {
    height: 24px;
    min-width: 44px;
  }

  /* 上传组件适配 */
  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
    height: 120px;
  }

  /* 穿梭框适配 */
  .el-transfer {
    flex-direction: column;
  }

  .el-transfer-panel {
    width: 100%;
    margin-bottom: 20px;
  }

  /* 树形控件适配 */
  .el-tree {
    font-size: 14px;
  }

  .el-tree-node__content {
    height: 36px;
    line-height: 36px;
  }

  /* 级联选择器适配 */
  .el-cascader-panel {
    width: 90vw;
    max-width: 320px;
  }

  /* 虚拟列表适配 */
  .el-virtual-list {
    height: 300px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: $small-mobile) {
  .el-button {
    min-height: 40px;
    padding: 10px 14px;
    font-size: 13px;
  }

  .el-input__inner {
    min-height: 40px;
    font-size: 15px;
  }

  .el-card__body {
    padding: 12px;
  }

  .el-dialog {
    width: 98% !important;
    margin: 2vh auto;
  }

  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 15px;
  }

  .el-message {
    min-width: 260px;
  }

  .el-notification {
    width: 95vw;
    margin: 0 2.5vw;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除hover效果，使用active效果 */
  .el-button:hover {
    transform: none;
    box-shadow: none;
  }

  .el-button:active {
    transform: scale(0.98);
  }

  .el-card:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .el-card:active {
    transform: scale(0.99);
  }

  /* 增大点击区域 */
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化滚动 */
  .scrollable {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

/* 横屏适配 */
@media (max-width: $mobile-breakpoint) and (orientation: landscape) {
  .el-dialog {
    width: 90% !important;
    margin: 2vh auto;
    max-height: 96vh;
    overflow-y: auto;
  }

  .el-drawer {
    height: 90% !important;
  }

  .mobile-landscape-full {
    height: 100vh;
    overflow-y: auto;
  }
}

/* 安全区域适配 (iPhone X等) */
@supports (padding: max(0px)) {
  @media (max-width: $mobile-breakpoint) {
    .safe-area-top {
      padding-top: max(20px, env(safe-area-inset-top));
    }

    .safe-area-bottom {
      padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    .safe-area-left {
      padding-left: max(12px, env(safe-area-inset-left));
    }

    .safe-area-right {
      padding-right: max(12px, env(safe-area-inset-right));
    }

    .safe-area-all {
      padding-top: max(20px, env(safe-area-inset-top));
      padding-bottom: max(20px, env(safe-area-inset-bottom));
      padding-left: max(12px, env(safe-area-inset-left));
      padding-right: max(12px, env(safe-area-inset-right));
    }
  }
}

/* 移动端专用工具类 */
@media (max-width: $mobile-breakpoint) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-only {
    display: block !important;
  }

  .mobile-flex {
    display: flex !important;
  }

  .mobile-grid {
    display: grid !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-text-left {
    text-align: left !important;
  }

  .mobile-margin-0 {
    margin: 0 !important;
  }

  .mobile-padding-0 {
    padding: 0 !important;
  }

  .mobile-padding-sm {
    padding: 8px !important;
  }

  .mobile-padding-md {
    padding: 16px !important;
  }

  .mobile-margin-sm {
    margin: 8px !important;
  }

  .mobile-margin-md {
    margin: 16px !important;
  }

  .mobile-font-sm {
    font-size: 12px !important;
  }

  .mobile-font-md {
    font-size: 14px !important;
  }

  .mobile-font-lg {
    font-size: 16px !important;
  }

  .mobile-scroll-x {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-scroll-y {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    overflow: hidden !important;
  }

  .mobile-sticky-top {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--el-bg-color);
  }

  .mobile-fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-light);
    padding: 12px;
  }

  .mobile-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  .mobile-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .mobile-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .mobile-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .mobile-fade-out {
    animation: fadeOut 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
}

/* 桌面端隐藏移动端元素 */
@media (min-width: $mobile-breakpoint + 1px) {
  .mobile-only {
    display: none !important;
  }

  .desktop-hidden {
    display: none !important;
  }
}
