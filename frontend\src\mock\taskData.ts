import type { ApiResponse, CreateTaskRequest, SubjectOption, Task } from '@/types'

// 学科选项数据
export const subjectOptions: SubjectOption[] = [
  { label: '语文', value: 'chinese', color: '#FF6B6B' },
  { label: '数学', value: 'math', color: '#4ECDC4' },
  { label: '英语', value: 'english', color: '#45B7D1' },
  { label: '物理', value: 'physics', color: '#96CEB4' },
  { label: '化学', value: 'chemistry', color: '#FFEAA7' },
  { label: '生物', value: 'biology', color: '#DDA0DD' },
  { label: '历史', value: 'history', color: '#F4A261' },
  { label: '地理', value: 'geography', color: '#2A9D8F' }
]

// 模拟任务数据
export const mockTasks: Task[] = [
  {
    id: 'task-001',
    title: '英语单词 Unit 3',
    content:
      '学习Unit 3的30个核心词汇，包括单词拼写、词义理解和例句应用。重点掌握：environment, pollution, recycle, sustainable等环保主题词汇。',
    subject: 'english',
    estimatedTime: 30,
    priority: 3,
    difficulty: 2,
    tags: ['词汇', 'Unit3', '环保主题'],
    status: 'active',
    createdAt: '2025-01-31T09:00:00.000Z',
    nextReviewTime: '2025-01-31T09:05:00.000Z',
    reviewSchedule: [
      {
        reviewTime: '2025-01-31T09:05:00.000Z',
        reviewIndex: 1,
        status: 'scheduled'
      },
      {
        reviewTime: '2025-01-31T09:30:00.000Z',
        reviewIndex: 2,
        status: 'scheduled'
      },
      {
        reviewTime: '2025-01-31T21:00:00.000Z',
        reviewIndex: 3,
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'task-002',
    title: '数学二次函数练习',
    content:
      '完成二次函数的图像绘制和性质分析练习题，包括：1. 二次函数的开口方向 2. 对称轴和顶点坐标 3. 与x轴的交点 4. 函数的增减性',
    subject: 'math',
    estimatedTime: 45,
    priority: 4,
    difficulty: 4,
    tags: ['函数', '图像', '练习题'],
    status: 'active',
    createdAt: '2025-01-30T14:00:00.000Z',
    nextReviewTime: '2025-01-31T14:00:00.000Z',
    reviewSchedule: [
      {
        reviewTime: '2025-01-30T14:05:00.000Z',
        reviewIndex: 1,
        status: 'completed'
      },
      {
        reviewTime: '2025-01-30T14:30:00.000Z',
        reviewIndex: 2,
        status: 'completed'
      },
      {
        reviewTime: '2025-01-31T14:00:00.000Z',
        reviewIndex: 3,
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'task-003',
    title: '物理光学实验报告',
    content:
      '完成光的折射实验报告，记录实验现象，分析折射定律，计算折射率。实验器材：激光笔、三角玻璃块、量角器、白纸。',
    subject: 'physics',
    estimatedTime: 60,
    priority: 5,
    difficulty: 3,
    tags: ['实验', '光学', '报告'],
    status: 'active',
    createdAt: '2025-01-29T16:00:00.000Z',
    nextReviewTime: '2025-02-01T16:00:00.000Z',
    reviewSchedule: [
      {
        reviewTime: '2025-01-29T16:05:00.000Z',
        reviewIndex: 1,
        status: 'completed'
      },
      {
        reviewTime: '2025-01-29T16:30:00.000Z',
        reviewIndex: 2,
        status: 'completed'
      },
      {
        reviewTime: '2025-01-30T04:00:00.000Z',
        reviewIndex: 3,
        status: 'completed'
      },
      {
        reviewTime: '2025-02-01T16:00:00.000Z',
        reviewIndex: 4,
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'task-004',
    title: '历史明朝政治制度',
    content:
      '学习明朝的政治制度特点，包括：1. 废除丞相制度 2. 设立内阁制度 3. 厂卫制度的建立 4. 科举制度的完善。理解明朝专制主义中央集权的特点。',
    subject: 'history',
    estimatedTime: 40,
    priority: 2,
    difficulty: 3,
    tags: ['明朝', '政治制度', '专制主义'],
    status: 'active',
    createdAt: '2025-01-28T10:00:00.000Z',
    nextReviewTime: '2025-02-04T10:00:00.000Z',
    reviewSchedule: []
  },
  {
    id: 'task-005',
    title: '化学酸碱中和反应',
    content:
      '掌握酸碱中和反应的原理和应用，学习pH值的概念，练习中和反应的化学方程式书写。重点实验：用氢氧化钠溶液中和盐酸。',
    subject: 'chemistry',
    estimatedTime: 35,
    priority: 3,
    difficulty: 2,
    tags: ['酸碱', '中和反应', 'pH值'],
    status: 'active',
    createdAt: '2025-01-31T08:00:00.000Z',
    nextReviewTime: '2025-01-31T08:05:00.000Z',
    reviewSchedule: [
      {
        reviewTime: '2025-01-31T08:05:00.000Z',
        reviewIndex: 1,
        status: 'scheduled'
      }
    ]
  }
]

// 生成复习计划的时间间隔（分钟）
const reviewIntervals = [5, 30, 720, 1440, 4320, 10080, 20160, 43200, 86400]

// 生成复习计划
export function generateReviewSchedule(createdAt: string = new Date().toISOString()) {
  const baseTime = new Date(createdAt)

  return reviewIntervals.map((interval, index) => ({
    reviewTime: new Date(baseTime.getTime() + interval * 60000).toISOString(),
    reviewIndex: index + 1,
    status: 'scheduled' as const
  }))
}

// Mock API 服务
export const mockApi = {
  // 获取任务列表
  getTasks: (): Promise<ApiResponse<Task[]>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: mockTasks,
          timestamp: new Date().toISOString()
        })
      }, 500)
    })
  },

  // 创建任务
  createTask: (
    taskData: CreateTaskRequest
  ): Promise<ApiResponse<{ taskId: string; reviewSchedule: any[]; loadWarning?: any }>> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟表单验证
        if (!taskData.title || !taskData.content) {
          const error = new Error('标题和内容不能为空')
          Object.assign(error, {
            success: false,
            error: {
              code: 'INVALID_INPUT',
              message: '标题和内容不能为空'
            },
            timestamp: new Date().toISOString()
          })
          reject(error)
          return
        }

        // 模拟负载检查
        const loadWarning =
          (taskData.estimatedTime || 0) > 60
            ? {
                level: 'heavy',
                message: '当日学习负载较重',
                suggestions: ['建议调整到明天', '减少学习时间']
              }
            : null

        const newTaskId = `task-${Date.now()}`
        const reviewSchedule = generateReviewSchedule()

        // 添加到模拟数据中
        const newTask: Task = {
          id: newTaskId,
          title: taskData.title,
          content: taskData.content,
          subject: taskData.subject,
          estimatedTime: taskData.estimatedTime || 30,
          priority: taskData.priority || 3,
          difficulty: taskData.difficulty || 3,
          tags: taskData.tags || [],
          status: 'active',
          createdAt: new Date().toISOString(),
          nextReviewTime: reviewSchedule[0]?.reviewTime || '',
          reviewSchedule
        }

        mockTasks.unshift(newTask)

        resolve({
          success: true,
          data: {
            taskId: newTaskId,
            reviewSchedule,
            loadWarning
          },
          timestamp: new Date().toISOString()
        })
      }, 800)
    })
  },

  // 获取单个任务
  getTask: (id: string): Promise<ApiResponse<Task>> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const task = mockTasks.find((t) => t.id === id)
        if (task) {
          resolve({
            success: true,
            data: task,
            timestamp: new Date().toISOString()
          })
        } else {
          const error = new Error('任务不存在')
          Object.assign(error, {
            success: false,
            error: {
              code: 'TASK_NOT_FOUND',
              message: '任务不存在'
            },
            timestamp: new Date().toISOString()
          })
          reject(error)
        }
      }, 300)
    })
  }
}
